import imsHttp from '@/common/http/ims-index.js';
var apis = {};
//用户
//登录
apis.login = (form, config) => api("/Login/Check", "", form, config);
//获取验证码
apis.getSms = (form, config) => api("/Login/GetVerifyCode", "", form, config);
//获取Guid
apis.getGuid = (form, config) => api("/InvoiceApply/GetGuidStr", "", form, config);
//附件照片
//列表
apis.getPhotoAttachmentList= (form, config) => api("/InvoiceApply/GetPageListByAttachment", "", form, config);
//删除
apis.deletePhotoAttachment= (form, config) => api("/InvoiceApply/Execute", "filedelete", form, config);
//上传
apis.upLoadPhotoAttachment= (form, config) => api("/OAMSWebApi/Seal/Upload", "", form, config);

//修改用户手机号
apis.modifyPhone = (form, config) => api("/ChangePhone/Check", "", form, config);
//考勤列表
apis.getAttendanceList = (form, config) => api("/AttendanceRecord/GetPageList", "", form, config);
//外出申请单列表
apis.getGoOutList = (form, config) => api("/GoOutAttendance/GetPageList", "", form, config);

//开票申请
//列表
apis.getInvoiceApplyList= (form, config) => api("/InvoiceApply/GetPageList", "", form, config);
//详情
apis.getInvoiceApplyDetail= (form, config) => api("/InvoiceApply/GetEntity", "", form, config);
//新增
apis.addInvoiceApply= (form, config) => api("/InvoiceApply/Execute","add", form, config);
//编辑
apis.editInvoiceApply= (form, config) => api("/InvoiceApply/Execute","modify", form, config);
//删除
apis.deleteInvoiceApply= (form, config) => api("/InvoiceApply/Execute","delete", form, config);
//提交
apis.submitInvoiceApply= (form, config) => api("/InvoiceApply/Execute","submit", form, config);
//审批
apis.approveInvoiceApply= (form, config) => api("/InvoiceApply/Execute","approve", form, config);



//审批信息
//审批列表
apis.getApproveList= (form, config) => api("/Wf/GetWfTaskPageList", "", form, config);
//审批进度
apis.getApproveProgress= (form, config) => api("/Wf/GetWFApprovalProgress", "", form, config);
//审批历史
apis.getApproveHistory = (form, config) => api("/Wf/GetDealList", "", form, config);

function api(api, docmd, form, config) {
    return new Promise((resolve, reject) => {
        imsHttp
            .post(
                api,
                {
                    DoCmd: docmd,
                    ...form,
                },
                { custom: { ...config } }
            )
            .then((res) => {
                resolve(res);
            })
            .catch((res) => {
                reject(res);
            });
    });
}

export default apis;
