var dbs = []
var codes = []
var arrs = []
var p_codes = []
for(let i in db){
	if(!db[i].parent_code){
		var index = codes.indexOf(db[i].code)
		if(codes.indexOf(db[i].code)==-1){
			arrs.push([])
			codes.push(db[i].code)
		}
	}
	else{
		var index = codes.indexOf(db[i].parent_code)
		if(index>-1){
			if(db[i].name.indexOf('行政区划')>-1){
				p_codes.push(db[i].code+'_'+index)
			}
			else{
				arrs[index].push(db[i].name)
			}
		}
		
		var p_index =  p_codes.toString().indexOf(db[i].parent_code)
		
		if(p_index>-1){
			var jjj = p_codes.toString().substring(p_index+7,p_index+9)
			arrs[jjj].push(db[i].name)
		}
	}
}
var ss = ''
for(let i in arrs){
	var line = ''
	for(let j in arrs[i]){
		if(j==0){
			line = line + '"'+arrs[i][j]+'"'
		}
		else{
			line = line +','+ '"'+arrs[i][j]+'"'
		}
	}
	ss = ss +"["+line+"],"
}
console.log(ss)