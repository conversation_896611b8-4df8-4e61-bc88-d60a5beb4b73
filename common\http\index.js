import Request from '@/common/luch-request/index.js'
import utils from '../../components/utils.js'
const http = new Request();
var loading = 0

let defaultUrl = ''
if (process.env.NODE_ENV === 'production') {
	//线上环境
	defaultUrl = 'https://miaojie.anyoucloud.com/api'
} else {
	//开发环境
	defaultUrl = 'https://miaojie.anyoucloud.com/api'
}

//设置全局配置
http.setConfig((config) => {
	config.baseURL = defaultUrl
	config.staticURL = 'https://miaojie.anyoucloud.com'
	config.custom = {
		loading: true
	}
	return config
})

//请求前
http.interceptors.request.use((config) => {
	if (config.custom.loading) {
		if (loading < 1) {
			
			uni.showLoading({
				title:"加载中"
			})
		}
		loading++		
	}
	let now_time = new Date().getTime()
	let access_token=uni.getStorageSync('access_token')
	let token_time=uni.getStorageSync('token_time')
	if (!access_token||now_time > (token_time + 1800 * 1000)) {		
		let token=new Promise(resolve=>{
			uni.request({
				url:defaultUrl+'/token',
				method :'POST',
				data: {
					client_id:'shenghe',
					client_secret: 'shenghe',
					grant_type:'client_credentials'
				},
				header: {
					"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8"
				},
				success: (res) => {
					try {
						uni.setStorageSync('access_token', res.data.access_token);
						uni.setStorageSync('token_time', new Date().getTime())
						config.header = {
							Authorization: 'Bearer '+ res.data.access_token
						}
						resolve(config);
					} catch (e) {
						console.log(e)
					}
				}
			})
		})
		
		return token
	}
	else{
		
		config.header = {
			Authorization: 'Bearer '+ access_token
		}
		return config
	}	
}, config => {
	return Promise.reject(config)
})

// 请求后
http.interceptors.response.use((response) => {
	if (response.config.custom.loading) {
		loading--
		if (loading == 0) {
			checkUser()
			uni.hideLoading()
		}
	}
	return response.data
}, (response) => {
	if (response.config.custom.loading) {
		loading--
		if (loading == 0) {
			uni.hideLoading()
		}
	}
	uni.showToast({
		icon:"none",
	    title: '请求错误，请稍后再试',
	    duration: 2000
	});
	return Promise.reject(response)
})


function checkUser(){
	var UserCode=uni.getStorageSync('UserCode');
	if(UserCode){
		uni.request({
			url:defaultUrl+'/LoginMng/GetIsChangeRole',
			method :'GET',
			data: {
				userCode:UserCode
			},
			header: {
				"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
				'Authorization': 'Bearer '+ uni.getStorageSync('access_token')
			},
			success: (res) => {
				if(res.data.Data==1){
					updateUser();
				}
			}
		})
	}
}

function updateUser(){
	var CellPhone=uni.getStorageSync('CellPhone')
	if(CellPhone){
		uni.request({
			url:defaultUrl+'/HomepageMng/GetUserInfo',
			method :'GET',
			data: {
				CellPhone:CellPhone
			},
			header: {
				"Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
				'Authorization': 'Bearer '+ uni.getStorageSync('access_token')
			},
			success: (res) => {
				console.log(res)
				var res=res.data
				if (res.ErrCode== 100) {
					uni.setStorageSync('CellPhone', res.Data.CellPhone)
					uni.setStorageSync('UserCode', res.Data.UserCode)
					uni.setStorageSync('IsCheck', res.Data.IsCheck)
					uni.setStorageSync('UserType', res.Data.UserType)
					uni.setStorageSync('HeadImg', res.Data.HeadImg)
					uni.setStorageSync('UserName', res.Data.UserName)
					uni.setStorageSync('JobNumber', res.Data.JobNumber)
					uni.setStorageSync('projectName', res.Data.DefaultProjectName)
					if (res.Data.DefaultProject) {
						uni.setStorageSync('projectCode', res.Data.DefaultProject.ProjectCode)
					} else {
						uni.setStorageSync('projectCode', '')
					}
					uni.setStorageSync('WxOpenid', res.Data.WxOpenid)
					uni.setStorageSync('UserRemark', res.Data.UserRemark)
					var role=utils.getRole(res.Data.UserRemark)
					uni.setStorageSync('roleName',role)
				
				}
			}
		})
	}
}


export default http