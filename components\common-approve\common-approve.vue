<template>
	<view class="card">
		<view class="card-header bor-b">
			<text class="model-text">审批</text>
		</view>

		<view class="form-pannel">
			<uv-form ref="uForm" labelWidth="80">
				<common-picker :borderBottom="false" required label="审批结果" pickerType="radio" v-model="approveForm.DealStatus" :radioOptions="radioOptions" v-model:displayValue="approveForm.DealStatusName" @change="handleStatusChange"></common-picker>

				<uv-form-item label="审批意见" labelPosition="top" :required="isRequired">
					<uv-textarea border="none" v-model="approveForm.DealOption" :cursorSpacing="50" count placeholder="请输入审核意见"></uv-textarea>
				</uv-form-item>
				<uv-button type="primary" shape="circle" :customStyle="{ background: $c.themeColor(), color: '#fff', marginTop: '30rpx' }" @click="approve()">确定</uv-button>
			</uv-form>
		</view>
		<view class="blank"></view>
	</view>
</template>

<script>
export default {
	name: "common-approve",

	props: {
		code: {
			type: String,
			default: "",
		},
		taskCode: {
			type: String,
			default: "",
		},
		api: {
			type: String,
			default: "",
		},
	},

	data() {
		return {
			isRequired: false,
			radioOptions: [
				{ name: "通过", value: 1 },
				{ name: "不通过", value: -1 },
			],
			approveForm: {
				Code: "", //主键编码
				DealStatusName: "",
				DealStatus: null, //1 通过 2-不通过
				DealOption: "",
				LoginUserCode: uni.getStorageSync("ImsUserCode"),
				GlobalUserCode: uni.getStorageSync("ImsUserCode"),
			},
		};
	},

	created() {
		this.approveForm.Code = this.code;
		this.approveForm.TaskCode = this.taskCode;
	},

	methods: {
		handleStatusChange(val) {
			this.approveForm.DealStatus = val;
			if (val == -1) {
				this.isRequired = true;
			} else {
				this.isRequired = false;
			}
			 // 手动根据选中值 val 查找对应的 name，同步到 DealStatusName
			  const selectedOption = this.radioOptions.find(option => option.value === val);
			  if (selectedOption) {
			    this.approveForm.DealStatusName = selectedOption.name;
			  }
			console.log(this.approveForm.DealStatus, this.approveForm.DealStatusName);
			// 状态变化时重新验证意见字段
			//this.validateField("DealOption");
		},
		approve() {
			if (!this.approveForm.DealStatusName) {
				return uni.$uv.toast("请选择审批结果");
			}
			if (this.isRequired == true && !this.approveForm.DealOption) {
				return uni.$uv.toast("请输入审批意见！");
			}
			this.$apis[this.api](this.approveForm).then((res) => {
				if (res.code == 100) {
					uni.showToast({
						title: "审批成功",
					});
					setTimeout(() => {
						uni.navigateBack();
						uni.$emit("refreshList");
					}, 1500);
				} else {
					uni.$uv.toast(res.msg);
				}
			});
		},
	},
};
</script>

<style lang="scss">
.form-pannel {
	.uv-form-item__body {
		padding: 0 !important;
	}
}
</style>
