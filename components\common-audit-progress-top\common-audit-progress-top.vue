<template>
  <view>
    <!-- 审批进度卡片，仅当流程状态有效时显示 -->
    <view class="card" >
      <view class="card-body">
        <view class="approve-progress">
          <!-- 步骤条组件 -->
          <uv-steps 
            :current="currentStep" 
            :active-color="$c.themeColor()" 
            v-if="progress.length"
          >
            <uv-steps-item 
              v-for="(step, index) in progress" 
              :key="step.NodeId || index"  
              :error="step.DealStatus === 'fail'"
              :title="step.NodeName"
              :status="getStepStatus(step.DealStatus)"
            >
			
			
			</uv-steps-item>
          </uv-steps>

          <!-- 无进度数据时的空提示 -->
          <view class="empty-tip" v-else>
            <uv-empty 
              mode="list" 
              text="暂无审批进度数据" 
              :margin-top="30"
            ></uv-empty>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "common-audit-progress-top",
  props: {
    // 单据唯一标识（用于获取审批进度）
    code: {
      type: String,
      required: true,  // 必传参数
      default: ''
    },
  },
  data() {
    return {
      progress: [],  // 审批进度数组
      currentStep: 0  ,// 当前激活的步骤索引
	  
    };
  },
  created() {
    // 组件创建时加载审批进度
    this.loadApproveProgress();
  },
  methods: {
    // 加载审批进度数据
    loadApproveProgress() {
      if (!this.code) {
        this.$uv.toast("缺少单据编号，无法获取审批进度");
        return;
      }

      this.$apis
        .getApproveProgress({ Code: this.code })  // 修正大小写问题
        .then((res) => {
          if (Array.isArray(res.data)) {
            this.progress = res.data;
            this.calculateCurrentStep();  // 计算当前步骤
          } else {
            this.progress = [];
            this.$uv.toast(res.msg || "获取审批进度失败");
          }
        })
        .catch((err) => {
          console.error("审批进度接口异常：", err);
          this.$uv.toast("获取审批进度失败，请稍后重试");
        });
		
    },

    // 计算当前激活的步骤
    calculateCurrentStep() {
      const steps = this.progress;
      if (!steps.length) return;

      // 特殊处理"完成"节点（依赖所有前置节点完成）
      const finishStepIdx = steps.findIndex(s => s.NodeName === "完成");
      if (finishStepIdx > -1) {
        const hasUnfinished = steps.some(
          (s, idx) => idx !== finishStepIdx && s.DealStatus !== "success"
        );
        if (hasUnfinished) steps[finishStepIdx].DealStatus = "";
      }

      // 1. 优先激活"进行中"节点
      const inProgressIdx = steps.findIndex(s => s.DealStatus === "inprogress");
      if (inProgressIdx > -1) {
        this.currentStep = inProgressIdx;
        return;
      }

      // 2. 激活最后一个完成节点的下一个节点
      const lastSuccessIdx = steps.reduce((last, s, idx) => {
        return s.DealStatus === "success" ? idx : last;
      }, -1);

      if (lastSuccessIdx === steps.length - 1) {
        this.currentStep = lastSuccessIdx;  // 全部完成
      } else if (lastSuccessIdx > -1) {
        this.currentStep = lastSuccessIdx + 1;  // 部分完成
      } else {
        this.currentStep = 0;  // 未开始
      }
    },

    // 映射步骤状态（适配uView步骤条）
    getStepStatus(dealStatus) {
      const statusMap = {
        success: "finish",
        inprogress: "process",
        fail: "error"
      };
      return statusMap[dealStatus] || "wait";
    },

    // 格式化步骤描述（处理人+时间）
    formatStepDesc(step) {
      if (step.DealStatus === "wait" || !step.DealUser) {
        return "待处理";
      }
      const time = step.DealTime ? this.$utils.formatDate(step.DealTime) : "无时间";
      return `${step.DealUser} · ${time}`;
    }
  }
};
</script>
<style>

</style>