<template>
  <view>
    <!-- 审批历史卡片 -->
    <view class="card">
      <!-- 卡片标题 -->
      <view class="card-header bor-b">
        <text class="model-text">审批历史</text>
      </view>

      <!-- 加载状态 -->
     

      <view class="card-body" >
        <!-- 审批历史记录（倒序展示） -->
        <view v-if="History.length">
          <view class="history-list">
            <view class="history-item" v-for="(item, i) in History" :key="i">
              <view class="history-info">
                <view class="history-user">{{ item.DealUserName || '未知用户' }}</view>
                <view class="history-status" :class="getStatusClass(item.StatusName)">{{ item.StatusName }}</view>
              </view>
              <view class="history-time">{{ item.DealDatetime || '无时间' }}</view>
              <view class="history-option" v-if="item.DealOption">{{ item.DealOption }}</view>
            </view>
          </view>
        </view>

        <!-- 空状态提示 -->
        <view class="empty-tip" v-else>
          <uv-empty 
            mode="list" 
            text="暂无审批历史记录" 
            :margin-top="30"
          ></uv-empty>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "common-audit-history",
  props: {
    // 单据编号（必传）
    code: {
      type: String,
      required: true,
      default: ''
    }
  },
  data() {
    return {
      History: [],       // 审批历史数组（倒序）
      loading: false     // 加载状态
    };
  },
  created() {
    this.loadApproveHistory();  // 加载并处理历史记录
  },
  methods: {
    // 加载审批历史并处理为倒序
    loadApproveHistory() {
      if (!this.code) {
        this.$uv.toast("缺少单据编号，无法获取审批历史");
        return;
      }

      this.loading = true;
      this.$apis.getApproveHistory({ Code: this.code })
        .then((res) => {
          this.loading = false;
          if (Array.isArray(res.data)) {
            // 核心：将历史记录倒序（最新的在前）
            this.History = [...res.data].reverse(); 
          } else {
            this.History = [];
            this.$uv.toast(res.msg || "获取审批历史失败");
          }
        })
        .catch((err) => {
          this.loading = false;
          console.error("审批历史接口异常：", err);
          this.$uv.toast("获取审批历史失败，请稍后重试");
        });
    },

    // 历史记录状态样式
    getStatusClass(statusName) {
      const statusMap = {
        "提交完成": "success",
        "通过": "success",
        "审批不通过": "error",
        "处理中": "processing"
      };
      return statusMap[statusName] || "";
    }
  }
};
</script>

<style scoped>
.card {
  background-color: #fff;
  border-radius: 12rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 标题样式 */
.card-header {
  padding: 20rpx 20rpx;
}
.model-text {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.bor-b {
  border-bottom: 1px solid #f5f5f5;
}

/* 内容区域 */
.card-body {
  padding: 30rpx 20rpx;
}

/* 历史记录列表 */
.history-list {
  margin-top: 10rpx;
}
.history-item {
  padding: 15rpx 10rpx;
  border-bottom: 1px solid #f5f5f5;
}
.history-item:last-child {
  border-bottom: none;
}
.history-info {
  display: flex;
  gap: 15rpx;
  margin-bottom: 5rpx;
  align-items: center;
}
.history-user {
  color: #666;
  font-size: 28rpx;
}
.history-status {
  font-size: 24rpx;
  padding: 2rpx 12rpx;
  border-radius: 12rpx;
}
.history-status.success {
  background-color: #eaffea;
  color: #4cd964;
}
.history-status.error {
  background-color: #fff2f2;
  color: #ff4d4f;
}
.history-status.processing {
  background-color: #fff8e6;
  color: #faad14;
}
.history-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}
.history-option {
  font-size: 26rpx;
  color: #666;
  background-color: #f9f9f9;
  padding: 8rpx 15rpx;
  border-radius: 6rpx;
  line-height: 1.5;
}

/* 空状态 */
.empty-tip {
  text-align: center;
  padding: 40rpx 0;
}

/* 加载状态 */
.loading-tip {
  padding: 40rpx 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
}
.loading-text {
  font-size: 28rpx;
  color: #666;
}
</style>
