<template>
	<view>
		<uv-form-item v-if="pickerMode !== 'date' && pickerMode !== 'radio'" :required="required" :label="label" :borderBottom="borderBottom" @click="handleClick">
			<uv-input border="none" disabled disabledColor="#ffffff" v-model="showValue" type="text" :placeholder="placeholder" />
			<uv-icon slot="right" name="arrow-right"></uv-icon>
		</uv-form-item>

		<uv-form-item v-if="pickerMode === 'date'" :required="required" :label="label" borderBottom @click="handleClick">
			<uni-datetime-picker :class="internalValue ? 'uni-date-picker-active' : ''" :show="showPicker" type="date" :border="false" v-model="internalValue" :end="new Date().getTime()" :clear-icon="false" placeholder="请选择" @change="onDateConfirm" @close="onClose"></uni-datetime-picker>
			<uv-icon slot="right" name="arrow-right"></uv-icon>
		</uv-form-item>

		<!-- 添加日期选择器 -->

		<!-- 整合后的选择器 -->
		<uv-popup ref="popup" mode="bottom" :show="showPicker && (pickerMode === 'normal' || pickerMode === 'category')" :closeable="true" @close="onClose">
			<view class="_select">
				<view class="tit">{{ title || label }}</view>

				<!-- 搜索框 (普通选择器模式) -->
				<view class="pd030" v-if="(pickerMode === 'normal' || pickerMode === 'category') && searchField">
					<uv-search borderColor="#ddd" bgColor="#fff" v-model="keyword" :placeholder="'请输入' + (searchLabel || label)" :showAction="true" @custom="onSearch" @search="onSearch" @clear="onClear"></uv-search>
				</view>

				<!-- 分类选择器的面包屑导航 -->
				<view class="_head" v-if="pickerMode === 'category'">
					<scroll-view class="head" :scroll-x="true" :scroll-into-view="toView">
						<view class="i" v-for="(item, index) in headList" :key="index" :id="'item' + index" @click="cateHeadClick(index, item)">
							<view class="inline">{{ item[labelField] }}</view>
							<view class="inline icon" v-if="index != headList.length - 1">
								<uv-icon name="arrow-right" :size="14"></uv-icon>
							</view>
						</view>
					</scroll-view>
				</view>

				<!-- 列表区域 -->
				<view class="_list">
					<scroll-view class="list" :style="{ height: listHeight }" :scroll-y="true" @scrolltolower="loadMore">
						<!-- 普通选择器列表 -->
						<template v-if="pickerMode === 'normal'">
							<view class="i bor-b flex" v-for="(item, index) in list" :key="index">
								<view class="flex-bd" @click="selectItem(item)">
									<view class="flex">
										<view class="check-icon" style="margin-right: 20rpx">
											<uv-icon name="checkmark-circle-fill" :size="20" :color="item.select ? themeColor : '#ddd'"></uv-icon>
										</view>
										<view class="name">{{ item[labelField] }}</view>
									</view>
								</view>
							</view>
						</template>

						<!-- 分类选择器列表 -->
						<template v-else-if="pickerMode === 'category'">
							<view class="i bor-b flex" v-for="(item, index) in list" :key="index">
								<view class="flex-bd" @click="cateClick(item)">
									<view class="flex">
										<view class="check-icon" style="margin-right: 20rpx" @click.stop="selectItem(item)">
											<uv-icon name="checkmark-circle-fill" :size="20" :color="item.select ? themeColor : '#ddd'"></uv-icon>
										</view>
										<view class="name">{{ item[labelField] }}</view>
									</view>
								</view>
								<view class="arrow-icon" @click="cateClick(item)">
									<uv-icon name="arrow-right" :size="14"></uv-icon>
								</view>
							</view>
						</template>

						<!-- 加载更多 (普通选择器模式) -->
						<view class="pd30" v-if="pickerMode === 'normal' && list.length > 0">
							<uv-load-more :status="loadMoreStatus" :loading-text="loadingText" :loadmore-text="loadmoreText" :nomore-text="nomoreText" />
						</view>
					</scroll-view>
				</view>
			</view>
		</uv-popup>

		<!-- 普通下拉选择器 -->
		<uv-picker ref="uvPicker" v-if="pickerMode === 'picker'" :defaultIndex="[0]" :columns="pickerColumns" :keyName="labelField" @confirm="onPickerConfirm" @cancel="onClose"></uv-picker>

		<!-- Radio 选择器 -->
		<uv-form-item v-if="pickerMode === 'radio'" :required="required" :label="label" :borderBottom="borderBottom">
			<uv-radio-group v-model="showValue" placement="row" @change="onRadioChange" style="padding: 20rpx 0">
				<uv-radio v-for="(item, index) in radioOptions" :key="index" :label="item.name" :customStyle="{ marginRight: '15px' }" :name="item.name"></uv-radio>
			</uv-radio-group>
		</uv-form-item>
	</view>
</template>

<script>
export default {
	name: "common-picker",
	props: {
		modelValue: {
			type: [String, Number],
			default: "",
		},
		displayValue: {
			type: String,
			default: "",
		},
		label: { type: String, default: "选择" },
		placeholder: { type: String, default: "请选择" },
		required: { type: Boolean, default: false },
		title: { type: String, default: "" },
		pickerType: {
			type: String,
			default: "",
			validator: (value) => {
				return ["", "category", "warehouse", "org", "dept", "user", "location", "supplier", "brand", "status", "source", "date", "radio", "disposeWay"].includes(value);
			},
		},
		userType: {
			type: String,
			default: "",
		},
		condition: { type: Object, default: () => null },
		watchValue: { type: [String, Number], default: "" },
		height: { type: [String, Number], default: 900 },
		pageSize: { type: Number, default: 20 },
		loadingText: { type: String, default: "努力加载中" },
		loadmoreText: { type: String, default: "轻轻上拉" },
		nomoreText: { type: String, default: "没有更多了" },
		themeColor: { type: String, default: "#1677ff" },
		columns: { type: Array, default: () => [] },
		radioOptions: {
			type: Array,
			default: () => [],
		},
		borderBottom: { type: Boolean, default: true },
		requireLeaf: { type: Boolean, default: false },
		extra: { type: Object, default: () => {} },
	},
	data() {
		return {
			popupRef: null,
			showPicker: false,
			list: [],
			page: 1,
			loadMoreStatus: "loadmore",
			keyword: "",
			headList: [],
			toView: "",
			isEnd: false,
			currentParentId: "",
			endCodes: [],
			cachedState: null,
			pickerData: [],
			showValue: "",
			internalValue: "",
		};
	},
	computed: {
		pickerMode() {
			const categoryTypes = ["category", "dept", "location"];
			if (categoryTypes.includes(this.pickerType)) return "category";
			const pickerTypes = ["status", "source", "disposeWay"];
			if (pickerTypes.includes(this.pickerType)) return "picker";
			if (this.pickerType === "date") return "date";
			if (this.pickerType === "radio") return "radio";
			return "normal";
		},
		labelField() {
			if (this.pickerMode === "picker") return "label";
			const fieldMap = {
				category: "Name",
				warehouse: "WarehouseName",
				org: "OrgName",
				dept: "OrgName",
				user: "username",
				location: "Name",
				supplier: "Name",
				brand: "Name",
			};
			return fieldMap[this.pickerType] || "Name";
		},
		valueField() {
			if (this.pickerMode === "picker") return "value";
			return "Code";
		},
		searchField() {
			const fieldMap = {
				warehouse: "WarehouseName",
				org: "Name",
				user: "UserName",
				supplier: "Name",
				brand: "Name",
			};
			return fieldMap[this.pickerType] || this.labelField;
		},
		searchLabel() {
			const labelMap = {
				warehouse: "仓库名称",
				org: "单位名称",
				user: "人员名称",
				supplier: "供应商名称",
				brand: "品牌名称",
			};
			return labelMap[this.pickerType] || this.label;
		},
		defaultParentId() {
			const parentMap = { category: "root", org: "system", dept: "system", location: "root" };
			return parentMap[this.pickerType] || "root";
		},
		extraParams() {
			const loginUserCode = uni.getStorageSync("UserCode");
			const orgCode = uni.getStorageSync("UserInfo").OrgCode;
			const extraMap = {
				category: { LoginUserCode: loginUserCode, CateNumber: "", ...this.extra },
				warehouse: { WarehouseName: "", ChargeUserName: "", WareHouseType: "固定资产", ...this.extra },
				org: { Name: "", ...this.extra },
				dept: { Name: "", ...this.extra },
				user: {
					OrgCode: this.userType == "org" ? this.watchValue : orgCode,
					DeptCode: this.userType == "dept" ? this.watchValue : "",
					...this.extra,
				},
				location: { Name: "", ...this.extra },
				supplier: { ContactName: "", ...this.extra },
				brand: { Name: "", ...this.extra },
			};
			return extraMap[this.pickerType] || {};
		},
		listHeight() {
			return typeof this.height === "number" ? `${this.height}rpx` : this.height;
		},
		pickerColumns() {
			return [this.pickerData];
		},
	},
	watch: {
		showPicker(val) {
			if (val) {
				if (!this.cachedState) this.init();
				else Object.assign(this, this.cachedState);
			} else {
				this.cacheCurrentState();
			}
		},
		watchValue: {
			handler(newVal, oldVal) {
				if (newVal !== oldVal) {
					this.cachedState = null;
					if (this.showPicker) {
						this.list = [];
						this.page = 1;
						this.loadMoreStatus = "loadmore";
						this.keyword = "";
						this.loadData();
					}
					if (oldVal && !newVal) this.handleSelectItem(null);
				}
			},
			immediate: true,
		},
		labelField: {
			handler(newVal) {
				if (this.pickerMode === "category" && (!this.headList || this.headList.length === 0)) {
					this.headList = [{ [newVal]: "全部" }];
				}
			},
			immediate: true,
		},
		displayValue: {
			handler(val) {
				this.showValue = val || "";
			},
			immediate: true,
		},
		
		modelValue: {
			handler(newVal) {
				this.internalValue = newVal;
				if (this.list && this.list.length > 0) {
					this.list.forEach((item) => {
						const itemValue = item[this.valueField];
						this.$set(item, "select", itemValue == newVal);
					});
				}
			},
			immediate: true,
		},
	},
	created() {
		this.internalValue = this.modelValue;
	},
	methods: {
		init() {
			this.page = 1;
			this.list = [];
			this.loadMoreStatus = "loadmore";
			this.keyword = "";
			if (this.pickerMode === "normal") this.loadData();
			else if (this.pickerMode === "category") {
				this.headList = [{ [this.labelField]: "全部" }];
				this.isEnd = false;
				this.currentParentId = this.defaultParentId;
				this.loadCategoryData(this.defaultParentId);
			}
		},
		cacheCurrentState() {
			if (this.pickerMode === "normal") {
				this.cachedState = {
					list: [...this.list],
					page: this.page,
					loadMoreStatus: this.loadMoreStatus,
					keyword: this.keyword,
				};
			} else if (this.pickerMode === "category") {
				this.cachedState = {
					list: [...this.list],
					headList: [...this.headList],
					toView: this.toView,
					isEnd: this.isEnd,
					currentParentId: this.currentParentId,
				};
			}
		},
		handleClick() {
			if (this.condition && (!this.condition.value || this.condition.value.trim() === "")) {
				uni.$uv.toast(this.condition.message || `请先选择${this.condition.field}`);
				return;
			}

			if (this.pickerType === "date") {
				this.showPicker = true;
				return;
			}

			this.loadPickerData();

			// 根据不同模式使用不同的打开方式
			if (this.pickerMode === "picker") {
				this.$refs.uvPicker.open();
				this.showPicker = true;
			} else if (this.$refs.popup) {
				this.$refs.popup.open();
				this.showPicker = true;
			}

			this.$emit("picker-show", { type: this.pickerType, params: this.extraParams });
		},
		onClose() {
			if (this.pickerMode === "picker") {
				this.$refs.uvPicker.close();
			} else if (this.$refs.popup) {
				this.$refs.popup.close();
			}
			this.showPicker = false;
		},
		getRequestMethod() {
			const apis = {
				category: this.$apis.getAssetCateList,
				warehouse: this.$apis.getWarehouseList,
				org: this.$apis.getDeptList,
				dept: this.$apis.getDeptList,
				user: this.$apis.getUserList,
				location: this.$apis.getLocationList,
				supplier: this.$apis.getSupplierList,
				brand: this.$apis.getBrandList,
				source: this.$apis.getAssetSourceList,
				status: this.$apis.getAssetStatusList,
			};
			return apis[this.pickerType] || this.$apis.getCommonList;
		},
		handleSelectItem(item) {
			if (!item) {
				this.showValue = "";
				this.$emit("confirm", null);
				this.$emit("update:modelValue", "");
				this.$emit("update:displayValue", "");

				this.showPicker = false;
				return;
			}

			const value = item[this.valueField];
			const label = item[this.labelField];

			// 处理数字字符串
			const emitValue = typeof value === "string" && !isNaN(value) ? Number(value) : value;

			// 如果点击的是已选中项,则取消选中
			if (item.select) {
				this.showValue = "";
				this.$emit("confirm", null);
				this.$emit("update:displayValue", "");
				this.$emit("update:modelValue", "");

				this.list.forEach((i) => this.$set(i, "select", false));
				return;
			}

			// 更新组件内部显示值
			this.showValue = label;

			// 更新选中状态
			this.list.forEach((i) => this.$set(i, "select", i[this.valueField] === value));

			// 同步更新父组件
			this.$emit("confirm", item);
			this.$emit("update:displayValue", label);
			this.$emit("update:modelValue", emitValue);

			// 关闭弹窗
			this.$refs.popup.close();
		},
		onPickerConfirm(e) {
			const selectedItem = e.value[0];
			if (selectedItem) {
				let value = selectedItem[this.valueField];
				const label = selectedItem[this.labelField];

				// 处理数字字符串
				if (typeof value === "string" && !isNaN(value)) value = Number(value);

				// 更新组件内部显示值
				this.showValue = label;

				// 同步更新父组件
				this.$emit("update:displayValue", label);
				this.$emit("update:modelValue", value);
			}
			this.showPicker = false;
		},
		async loadData() {
			if (this.loadMoreStatus === "nomore") return;
			this.loadMoreStatus = "loading";

			try {
				const params = {
					PageIndex: this.page,
					PageSize: this.pageSize,
					...this.extraParams,
				};

				// 处理所有需要父级ID的树形选择
				switch (this.pickerType) {
					case "dept":
					case "org":
						params.ParentCode = this.currentParentId || this.defaultParentId;
						break;
					case "category":
						params.ParentId = this.currentParentId || this.defaultParentId;
						break;
					case "location":
						params.PId = this.currentParentId || this.defaultParentId;
						break;
				}

				if (this.keyword && this.searchField) {
					params[this.searchField] = this.keyword;
				}

				const res = await this.getRequestMethod()(params);

				if (res && Array.isArray(res.data)) {
					const currentValue = this.internalValue;

					res.data.forEach((item) => {
						const itemValue = item[this.valueField];
						this.$set(item, "select", itemValue == currentValue);
					});

					this.list = this.page === 1 ? res.data : [...this.list, ...res.data];
					this.loadMoreStatus = res.data.length < this.pageSize ? "nomore" : "loadmore";
				} else {
					this.loadMoreStatus = "nomore";
				}
			} catch (e) {
				console.error(e);
				this.loadMoreStatus = "loadmore";
			}
		},
		loadMore() {
			if (this.loadMoreStatus !== "loadmore") return;
			this.page++;
			this.loadData();
		},
		selectItem(item) {
			if (!item) return;
			if (this.requireLeaf) {
				this.cateClick(item);
				return;
			}
			this.handleSelectItem(item);
		},
		onSearch() {
			if (this.pickerMode === "normal") {
				this.loadMoreStatus = "loading";
				this.page = 1;
				this.loadData();
			} else if (this.pickerMode === "category") {
				// 分类选择器的搜索处理
				this.loadCategoryData(this.currentParentId);
			}
		},
		onClear() {
			this.keyword = "";
			if (this.pickerMode === "normal") {
				this.loadMoreStatus = "loading";
				this.page = 1;
				this.loadData();
			} else if (this.pickerMode === "category") {
				// 分类选择器清除搜索时重新加载当前层级数据
				this.loadCategoryData(this.currentParentId);
			}
		},
		async loadCategoryData(parentId) {
			try {
				const params = {
					ParentCode: parentId,
					PageIndex: 1,
					PageSize: this.pageSize,
					...this.extraParams,
				};

				// 添加搜索关键字参数
				if (this.keyword) {
					params[this.searchField] = this.keyword;
				}
				const res = await this.getRequestMethod()(params);
				if (res && res.data.length > 0) {
					const currentValue = this.internalValue;
					// 设置选中状态，根据当前value值
					res.data.forEach((item) => {
						const itemValue = item[this.valueField];
						this.$set(item, "select", itemValue == currentValue);
					});

					this.list = res.data;
					return true;
				} else {
					if (!this.keyword) {
						// 只有在非搜索状态下才添加到 endCodes
						this.endCodes.push(parentId);
					}
					uni.$uv.toast(this.keyword ? "未找到相关数据" : "已经到底了1");
					return false;
				}
			} catch (e) {
				console.error(e);
				return false;
			}
		},
		cateHeadClick(index, item) {
			if (this.endCodes.includes(item[this.valueField])) {
				return uni.$uv.toast("已经到底了");
			}

			// 如果在搜索状态下，先清除搜索
			if (this.keyword) {
				this.keyword = "";
			}

			this.headList = this.headList.slice(0, index + 1);
			const parentId = item[this.valueField] || this.defaultParentId;
			this.currentParentId = parentId;
			this.loadCategoryData(parentId);
		},
		cateClick(item) {
			// 判断是否是父节点
			const isParent = item.IsParent === true || item.IsParent === "true" || item.IsParent === "True";

			// 如果不是父节点，直接选中并关闭
			if (!isParent) {
				this.handleSelectItem(item);
				return;
			}

			// 如果在搜索状态下，先清除搜索
			if (this.keyword) {
				this.keyword = "";
			}

			// 如果是父节点，加载下一级
			this.loadCategoryData(item[this.valueField]).then((hasData) => {
				if (hasData) {
					this.headList.push(item);
					this.currentParentId = item[this.valueField];
				} else {
					// 如果需要叶子节点，则选中当前项
					if (this.requireLeaf) {
						this.handleSelectItem(item);
					} else {
						uni.$uv.toast("已经到底了");
					}
				}
			});
		},
		async loadPickerData() {
			if (this.pickerType === "source" || this.pickerType === "status" || this.pickerType === "disposeWay") {
				try {
					let data = [];
					if (this.pickerType === "source") {
						const res = await this.$apis.getDicList({ ParentCode: "SourceType" });
						data = res.data.map((item) => ({
							label: item.DataDicName,
							value: item.DataDicKey,
						}));
					} else if (this.pickerType === "status") {
						const res = await this.$apis.getDicList({ ParentCode: "AssetStatus" });
						data = res.data.map((item) => ({
							label: item.DataDicName,
							value: item.DataDicKey,
						}));
					} else if (this.pickerType === "disposeWay") {
						const res = await this.$apis.getDicList({ ParentCode: "Dispose" });
						data = res.data.map((item) => ({
							label: item.DataDicName,
							value: item.DataDicKey,
						}));
					}
					this.pickerData = data;

					// 使用 internalValue 代替 value
					const currentValue = this.internalValue;
					if (currentValue) {
						const selectedItem = this.pickerData.find((item) => {
							const itemValue = item[this.valueField];
							return itemValue == currentValue;
						});
						if (selectedItem) {
							this.showValue = selectedItem[this.labelField];
							this.$emit("update:displayValue", selectedItem[this.labelField]);
						}
					}
				} catch (error) {
					console.error("获取选择器数据失败:", error);
					this.pickerData = [];
				}
			} else {
				if (!this.columns || !Array.isArray(this.columns)) {
					this.pickerData = [];
					return;
				}
				this.pickerData = this.columns.map((item) => {
					if (typeof item !== "object") {
						return { [this.labelField]: item, [this.valueField]: item };
					}
					const result = { ...item };
					if (!result[this.labelField]) result[this.labelField] = item.label || item.name || item.text || "";
					if (!result[this.valueField]) result[this.valueField] = item.value || item.code || item.id || "";
					return result;
				});

				// 使用 internalValue 代替 value
				const currentValue = this.internalValue;
				if (currentValue) {
					const selectedItem = this.pickerData.find((item) => {
						const itemValue = item[this.valueField];
						return itemValue == currentValue;
					});
					if (selectedItem) {
						this.showValue = selectedItem[this.labelField];
						this.$emit("update:displayValue", selectedItem[this.labelField]);
					}
				}
			}
		},
		onDateConfirm(e) {
			const value = e;
			// 格式化日期显示
			const date = new Date(value);
			const label = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;

			// 更新内部值
			this.internalValue = value;
			this.showValue = label;

			const dateItem = {
				value: value,
				label: label,
				date: date,
			};

			// 同步更新父组件
			this.$emit("update:displayValue", label);
			this.$emit("update:modelValue", value);
			this.showPicker = false;
		},
		onRadioChange(e) {
			const selectedOption = this.radioOptions.find((opt) => opt.name === e);
			if (selectedOption) {
				// console.log("子组件发射 displayValue 更新:", selectedOption.name); 
				this.showValue = selectedOption.name;
				this.$emit("update:displayValue", selectedOption.name);
				this.$emit("update:modelValue", selectedOption.value);
				this.$emit("change", selectedOption.value);
			}else {
				console.warn("未找到匹配的选项:", e); // 检查是否有找不到选项的情况
			  }
		},
		handleSelect(item, index) {
			// 判断是否是父节点
			const isParent = item.IsParent === true || item.IsParent === "true";

			// 如果不是父节点，直接选中并关闭
			if (!isParent) {
				this.selectedItems = this.selectedItems.slice(0, index).concat([item]);
				this.columns = this.columns.slice(0, index + 1);
				this.emitChange();
				this.close();
				return;
			}

			// 如果是父节点，保持原有逻辑
			this.selectedItems = this.selectedItems.slice(0, index).concat([item]);
			this.columns = this.columns.slice(0, index + 1);
			this.loadNextLevel(item.Id, index + 1);
		},
	},
	beforeDestroy() {
		this.cachedState = null;
	},
	mounted() {
		if (this.pickerMode === "category") this.headList = [{ [this.labelField]: "全部" }];
		if (this.modelValue && !this.displayValue) {
			this.$emit("request-display-value", this.modelValue); // 请求父组件提供初始显示值
		}
	},
};
</script>

<style lang="scss" scoped>
._select .uni-input-placeholder {
	background: #fff !important;
}
</style>