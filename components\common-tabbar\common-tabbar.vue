<template>
  <uv-tabbar 
    :value="tabbarValue"  
    :inactiveColor="inactiveColor" 
    :activeColor="activeColor" 
    :border="false"
    @change="handleTabChange"
  >
    <uv-tabbar-item 
      v-for="(tab, index) in tabList" 
      :key="index"
      :text="tab.text"
      v-if="tab.show"
      @click="handleTabClick(tab)"
    >
      <template v-slot:active-icon>
        <image 
          class="tabbar-icon" 
          :src="tab.activeIcon" 
          mode="widthFix"
          alt="active icon"
        ></image>
      </template>
      <template v-slot:inactive-icon>
        <image 
          class="tabbar-icon" 
          :src="tab.inactiveIcon" 
          mode="widthFix"
          alt="inactive icon"
        ></image>
      </template>
    </uv-tabbar-item>
  </uv-tabbar>
</template>

<script>
export default {
  name: 'common-tabBar',
  props: {
    // 初始激活的tab索引
    initialValue: {
      type: Number,
      default: 0
    },
    // 角色权限（用于控制审批tab显示）
    role: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // 导航栏配置 - 集中管理所有tab信息
      tabList: [
        {
          text: '首页',
          path: '/ims/index/index',
          activeIcon: '/static/ims_images/<EMAIL>',
          inactiveIcon: '/static/ims_images/<EMAIL>',
          show: true,
          navType: 'reLaunch' // 首页通常用reLaunch避免页面栈累积
        },
        {
          text: '审批',
          path: '/ims/audit/audit',
          activeIcon: '/static/ims_images/<EMAIL>',
          inactiveIcon: '/static/ims_images/<EMAIL>',
          show: false, // 动态判断
          navType: 'navigateTo'
        },
        {
          text: '通讯录',
          path: '/ims/ContactList/ContactList',
          activeIcon: '/static/ims_images/<EMAIL>',
          inactiveIcon: '/static/ims_images/<EMAIL>',
          show: true,
          navType: 'navigateTo'
        },
        {
          text: '我的',
          path: '/ims/user/user',
          activeIcon: '/static/ims_images/<EMAIL>',
          inactiveIcon: '/static/ims_images/<EMAIL>',
          show: true,
          navType: 'navigateTo'
        }
      ],
      tabbarValue: 0,
      inactiveColor: '#000',
      activeColor: '#5771F9'
    }
  },
  created() {
    // 初始化激活状态
    this.tabbarValue = this.initialValue;
    // 根据角色权限设置审批tab是否显示
    this.tabList[1].show = this.role === 1;
  },
  methods: {
    // 处理tab切换事件（同步激活状态）
    handleTabChange(index) {
      this.tabbarValue = index;
    },
    
    // 处理tab点击事件（统一导航逻辑）
    handleTabClick(tab) {
      // 避免重复跳转同一页面
      if (this.getCurrentPage() === tab.path) return;
      
      // 根据配置的导航类型执行对应导航方法
      if (uni[tab.navType]) {
        uni[tab.navType]({
          url: tab.path
        }).catch(err => {
          console.error(`导航到${tab.text}失败:`, err);
        });
      } else {
        console.error(`不支持的导航类型: ${tab.navType}`);
      }
    },
    
    // 获取当前页面路径（用于避免重复跳转）
    getCurrentPage() {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        return pages[pages.length - 1].route;
      }
      return '';
    }
  }
}
</script>

<style scoped>

</style>
