<template>
  <view class="my-upload">
    <uv-upload
      :width="width"
      :height="height"
      :maxCount="maxCount"
      :fileList="innerFileList"
	  :code="Code"
      multiple
      @afterRead="handleAfterRead"
      @delete="handleDelete"
      :name="name"
      :capture="['album', 'camera']"
      :sourceType="['album', 'camera']"
      :previewFullImage="true"
      :compressed="true"
      @error="handleError"></uv-upload>
  </view>
</template>

<script>
export default {
  name: "common-upload",
  props: {
    // 图片宽度
    width: {
      type: [String, Number],
      default: 60,
    },
    // 图片高度
    height: {
      type: [String, Number],
      default: 60,
    },
    // 最大上传数量
    maxCount: {
      type: [String, Number],
      default: 10,
    },
    // 组件标识
    name: {
      type: [String, Number],
      default: "1",
    },
    // 双向绑定的值
    modelValue: {
      type: [String, Array],
      default: "",
    },
	Code:{
		type: String,
		default: "",
	}
  },
  data() {
    return {
      innerFileList: [],
      isUploading: false,
      lastUploadTime: 0,
    };
  },
  watch: {
    modelValue: {
      handler(newVal) {
        const newFileList = newVal
          ? newVal.includes(",")
            ? newVal.split(",").map((pic) => ({
                url: this.$c.getImageUrl(pic),
                pic: pic,
              }))
            : [
                {
                  url: this.$c.getImageUrl(newVal),
                  pic: newVal,
                },
              ]
          : [];

        const currentPics = this.innerFileList.map((item) => item.pic).join(",");
        const newPics = newFileList.map((item) => item.pic).join(",");

        if (currentPics !== newPics) {
          this.innerFileList = newFileList;
        }
      },
      immediate: true,
    },
  },
  created() {
    this.getFileList();  // 加载并处理历史记录
  },
  methods: {
    async handleAfterRead(event) {
      const lists = [].concat(event.file);
      const startIndex = this.innerFileList.length;

      // 先添加所有文件到列表中，状态为上传中
      lists.forEach((item, index) => {
        this.innerFileList.push({
          ...item,
          status: "uploading",
          message: "上传中",
        });
      });

      // 使用 Promise.all 并行处理所有上传
      try {
        await Promise.all(
          lists.map(async (item, index) => {
            const currentIndex = startIndex + index;
            try {
              const res = await this.$c.uploadFilePromise(item, this.Code);
              // 使用 Vue.set 确保响应式更新
              this.$set(this.innerFileList, currentIndex, {
                status: "success",
                message: "",
                url: this.$c.getImageUrl(res.ServerRelativeFileName),
                pic: res.ServerRelativeFileName,
              });
            } catch (error) {
              // 使用 Vue.set 确保响应式更新
              this.$set(this.innerFileList, currentIndex, {
                ...item,
                status: "error",
                message: "上传失败",
              });
              console.error("上传失败:", error);
            }
          })
        );

        // 所有上传完成后，触发一次更新
        this.emitChange();
      } catch (error) {
        console.error("上传过程出错:", error);
      }
    },

    handleDelete(event) {
		
      this.innerFileList.splice(event.index, 1);
      this.emitChange();
	  this.$apis
	  	.deletePhotoAttachment({
	  			Code:this.Code
	  		})
	  	.then((res) => {
	  		console.log("删除",res)
	  	})
	  	.catch((err) => {
	  		console.error("获取图片失败", err);
	  		this.$uv.toast("获取图片失败，请重试");
	  	});
    },

    emitChange() {
      const urls = this.innerFileList
        .filter((item) => item.status === "success" || !item.status)
        .map((item) => item.pic)
        .join(",");
      this.$emit("update:modelValue", urls);
      this.$emit("change", {
        files: JSON.parse(JSON.stringify(this.innerFileList)),
        urls: urls,
      });
    },

    // 获取当前文件列表
    getFileList() {
      //return this.innerFileList;
	  this.$apis
	  	.getPhotoAttachmentList({
			Code:this.Code
		})
	  	.then((res) => {
	  		this.innerFileList = this.convertToFileList(res.data)
			 console.log(this.innerFileList)
	  	})
	  	.catch((err) => {
	  		console.error("获取图片失败", err);
	  		this.$uv.toast("获取图片失败，请重试");
	  	});
    },
    // 获取拼接后的url字符串
	convertToFileList(rawData) {
	  // 校验原始数据是否为数组
	  if (!Array.isArray(rawData)) {
	    console.warn("原始数据格式错误，需传入数组类型");
	    return [];
	  }	
	  // 遍历数组，提取ServerFileName并拼接URL
	  return rawData.map((item, index) => {
	    // 校验当前项是否存在ServerFileName字段，避免URL拼接异常
	    const serverFileName = item?.ServerFileName || "";
	    return {
	      url:  this.$imsHttp.config.staticUrl + `/upload/`+ uni.$uv.date(new Date().getTime(),'yyyy/mm/dd') +`/${serverFileName}` // 拼接目标URL
	    };
	  });
	},
    getUrls() {
      return this.innerFileList.map((item) => item.ServerFileName).join(",");
	 
    },
    // 清空文件列表
    clear() {
      this.innerFileList = [];
    },
    // 处理上传错误
    handleError(error) {
      console.error("上传错误:", error);
      if (error.errMsg == "chooseImage:fail cancel") {
        uni.showToast({
          title: "取消上传",
          icon: "none",
        });
      } else {
        uni.showToast({
          title: "上传出错",
          icon: "none",
        });
      }
    },
  },
};
</script>

<style>
.my-upload {
  display: inline-block;
}
</style>
