const formatTime = date => {
	const year = date.getFullYear()
	const month = date.getMonth() + 1
	const day = date.getDate()
	const hour = date.getHours()
	const minute = date.getMinutes()
	const second = date.getSeconds()

	return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
}

const formatNumber = n => {
	n = n.toString()
	return n[1] ? n : '0' + n
}

function getRole(code) {
	var role
	switch (code) {
		case '001':
			role='业主领导'
			break;
		case '011':
			role='业主成员'
			break;
		case '002':
			role='项目负责人'
			break;
		case '003':
			role='会议服务负责人'
			break;
		case '004':
			role='设备服务负责人'
			break;
		case '005':
			role='卤菜外卖负责人'
			break;
		case '006':
			role='餐食预留负责人'
			break;
		case '007':
			role='包厢预定负责人'
			break;
		case '008':
			role='会议服务人员'
			break;
		case '009':
			role='维修人员'
			break;
		case '101':
			role='总经理室'
			break;
		case '102':
			role='副总经理室1'
			break;
		case '103':
			role='副总经理室2'
			break;
		case '104':
			role='项目部主任'
			break;
		case '105':
			role='项目管理员'
			break;
		case '106':
			role='综合管理员（主任助理）'
			break;
		case '107':
			role='110接线员'
			break;
		case '108':
			role='保洁服务负责人'
			break;
		case '109':
			role='保洁服务人员'
			break;
		case '110':
			role='安保部负责人'
			break;
		case '111':
			role='保安'
			break;
		case '112':
			role='音响师'
			break;
		default:
			role=''					
	}
	return role
}

function getRoleGroup(code){
	var role=''
	if(code=='101'||code=='102'||code=='103'||code=='104'||code=='105'){
		role='admin'
	}
	else if(code=='002'||code=='003'||code=='004'||code=='106'||code=='107'){
		role='fzr'
	}
	else if(code=='005'||code=='006'||code=='007'){
		role='dsf'
	}
	else if(code=='008'||code=='009'||code=='108'||code=='109'||code=='110'||code=='111'||code=='112'){
		role='fw'
	}
	else{
		role='yz'
	}
	return role
}

function checkTime(time){
	var t=time.replace(/-/g, '/')
	
	var now=new Date().getTime()
	var d=new Date(t).getTime()
	console.log(d,now)
	if(d<now){
		return false
	}
	else{
		return true
	}
	
}

function checkTwoTime(time1,time2){
	var t1=time1.replace(/-/g, '/')
	var t2=time2.replace(/-/g, '/')
	var d1=new Date(t1).getTime()
	var d2=new Date(t2).getTime()
	if(d2>d1){
		return true
	}
	else{
		return false
	}
	
}

function imgArray(list){
	var listArray=list.split(',')
	for(let index in listArray){
		listArray[index]='https://miaojie.anyoucloud.com'+listArray[index]
	
	}
	return listArray
}

export default {
	getRole,
	getRoleGroup,
	checkTime,
	checkTwoTime,
	formatTime,
	imgArray
}
