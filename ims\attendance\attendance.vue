<template>
  <view>
    <uv-navbar title="我的考勤" :fixed="true" :placeholder="true" :autoBack="true" bgColor="#f8f9fb"></uv-navbar>
    <view class="m30">
      <uv-search placeholder="请选择月份" v-model="AttendanceMonth" clearabled disabled bgColor="#fff" shape="square" @click="showMonthPicker" actionText="清空" @custom="clearMonth"></uv-search>
    </view>
    <view class="attendance-title flex">
      <view class="flex-bd">本月考勤</view>
      <view class="flex-hd flex" @click="ascDesc">
        <view>排序</view>
        <image src="@/static/ims_images/icon-sort.png" class="arrow-down" mode="widthFix"></image>
      </view>
    </view>
    <view class="attendance-list">
      <view class="item flex">
        <image src="@/static/ims_images/icon-daka.png" class="icon" mode="widthFix"></image>
        <view class="info flex-bd">
          <view class="time">2025-01-01 18:00:00</view>
          <view class="desc">到场</view>
        </view>
        <view class="status success">正常</view>
      </view>
      <view class="item flex">
        <image src="@/static/ims_images/icon-xiaban.png" class="icon" mode="widthFix"></image>
        <view class="info flex-bd">
          <view class="time">2025-01-01 18:00:00</view>
          <view class="desc">下班</view>
        </view>
        <view class="status info">未排班</view>
      </view>
      <view class="item flex">
        <image src="@/static/ims_images/icon-xiaban.png" class="icon" mode="widthFix"></image>
        <view class="info flex-bd">
          <view class="time">2025-01-01 18:00:00</view>
          <view class="desc">下班</view>
        </view>
        <view class="status warning">迟到</view>
      </view>
      <view class="item flex">
        <image src="@/static/ims_images/icon-xiaban.png" class="icon" mode="widthFix"></image>
        <view class="info flex-bd">
          <view class="time">2025-01-01 18:00:00</view>
          <view class="desc">下班</view>
        </view>
        <view class="status error">旷工</view>
      </view>
      <view class="item flex">
        <image src="@/static/ims_images/icon-xiaban.png" class="icon" mode="widthFix"></image>
        <view class="info flex-bd">
          <view class="time">2025-01-01 18:00:00</view>
          <view class="desc">下班</view>
        </view>
        <view class="status normal">缺卡</view>
      </view>
    </view>
    <uv-datetime-picker ref="datetimePicker" mode="year-month" @confirm="confirmMonth" :max-date="maxDate"></uv-datetime-picker>
    <view class="pd30">
      <uv-load-more fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
    </view>
    <uv-back-top
      :scroll-top="scrollTop"
      mode="square"
      :duration="0"
      :iconStyle="{
        fontSize: '32rpx',
        color: '#fff',
      }"
      :customStyle="'background-color:' + $c.themeColor()"></uv-back-top>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      page: 1,
      scrollTop: 0,
      list: [],
      AttendanceMonth: "",
      OrderBy: "asc",
      maxDate: Number(new Date()),
    };
  },
  onLoad() {
    this.getList(1).then((res) => {
      this.list = res;
    });
  },
  methods: {
    clearMonth() {
      this.AttendanceMonth = "";
      this.page = 1;
      this.loadmore.status = "loading";
      this.list = [];
      this.getList(1).then((res) => {
        this.list = res;
      });
    },
    showMonthPicker() {
      this.$refs.datetimePicker.open();
    },
    confirmMonth(e) {
      this.AttendanceMonth = uni.$uv.date(e.value, "yyyy-mm");
      this.page = 1;
      this.loadmore.status = "loading";
      this.list = [];
      this.getList(1).then((res) => {
        this.list = res;
      });
    },
    ascDesc() {
      this.OrderBy = this.OrderBy == "asc" ? "desc" : "asc";
      this.page = 1;
      this.loadmore.status = "loading";
      this.list = [];
      this.getList(1).then((res) => {
        this.list = res;
      });
    },
    getList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getAttendanceList(
            {
              AttendanceMonth: this.AttendanceMonth, //月份
              UserCode: uni.getStorageSync("ImsUserCode"), //人员编码
              OrderBy: this.OrderBy, //排序( desc   asc)
            },
            { custom: { loading: false } }
          )
          .then((res) => {
            if (res.data.length < 10) {
              this.loadmore.status = "nomore";
            }
            for (let i in res.data) {
              console.log(res.data[i].WfName);
            }
            resolve(res.data);
          });
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
};
</script>

<style></style>
