<template>
  <view class="ims">
    <uv-navbar title="外出申请单" :fixed="true" :placeholder="true" :autoBack="true" bgColor="#f8f9fb"></uv-navbar>
    <view class="page-wrap">
      <!-- 列表区域 -->
      <view class="list">
        <view class="list-item" v-for="(item, index) in list" :key="index" @click="select">
          <view class="list-item-header">
            <view class="title-h1">{{ item.BillNo }}</view>
            <view><uv-tags :text="item.WfStatusCaption" :type="item.WfStatusType" shape="circle" size="mini"></uv-tags></view>
          </view>
          <view class="list-item-body">
            <view class="li-title">{{ item.ApplyTitle }}</view>
            <!-- 日期格式化：已有处理，保留 -->
            <view class="li-item">申请时间：{{ formattedDate(item.ApplyDate) }}</view>
            <!-- 关键修改：显示HeadTypeName，加默认值避免异常 -->
            <view class="li-item">开票抬头：[{{ item.HeadTypeName }}]{{ item.HeadName }}</view>
            <view class="li-item">
              开票金额：
              <text class="primary-color">¥{{ item.InvoiceAmount }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="pd30">
      <uv-load-more fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
    </view>
    <uv-back-top
      :scroll-top="scrollTop"
      mode="square"
      :duration="0"
      :iconStyle="{
        fontSize: '32rpx',
        color: '#fff',
      }"
      :customStyle="'background-color:' + $c.themeColor()"></uv-back-top>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loadmore: {
        status: "loading",
        loadingText: "努力加载中",
        loadmoreText: "轻轻上拉",
        nomoreText: "没有更多了",
      },
      page: 1,
      scrollTop: 0,
      list: [],
      AttendanceMonth: "",
      OrderBy: "asc",
      maxDate: Number(new Date()),
    };
  },
  onLoad() {
    this.getList(1).then((res) => {
      this.list = res;
    });
  },
  methods: {
    clearMonth() {
      this.AttendanceMonth = "";
      this.page = 1;
      this.loadmore.status = "loading";
      this.list = [];
      this.getList(1).then((res) => {
        this.list = res;
      });
    },
    showMonthPicker() {
      this.$refs.datetimePicker.open();
    },
    confirmMonth(e) {
      this.AttendanceMonth = uni.$uv.date(e.value, "yyyy-mm");
      this.page = 1;
      this.loadmore.status = "loading";
      this.list = [];
      this.getList(1).then((res) => {
        this.list = res;
      });
    },
    ascDesc() {
      this.OrderBy = this.OrderBy == "asc" ? "desc" : "asc";
      this.page = 1;
      this.loadmore.status = "loading";
      this.list = [];
      this.getList(1).then((res) => {
        this.list = res;
      });
    },
    getList(page) {
      return new Promise((resolve) => {
        this.$apis
          .getAttendanceList(
            {
              AttendanceMonth: this.AttendanceMonth, //月份
              UserCode: uni.getStorageSync("ImsUserCode"), //人员编码
              OrderBy: this.OrderBy, //排序( desc   asc)
            },
            { custom: { loading: false } }
          )
          .then((res) => {
            if (res.data.length < 10) {
              this.loadmore.status = "nomore";
            }
            for (let i in res.data) {
              console.log(res.data[i].WfName);
            }
            resolve(res.data);
          });
      });
    },
  },
  onReachBottom() {
    if (this.loadmore.status == "nomore") {
      return;
    }
    var list = this.list;
    this.getList(this.page + 1).then((res) => {
      for (let index in res) {
        list.push(res[index]);
      }
      if (res.length > 0) {
        this.list = list;
        this.page++;
        if (res.length < 10) {
          this.loadmore.status = "nomore";
        }
      }
    });
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
};
</script>

<style></style>
