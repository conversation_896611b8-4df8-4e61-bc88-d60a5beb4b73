<template>
	<view class="ims">
		<uv-navbar :title="title" :fixed="true" :placeholder="true" leftIcon="" bgColor="#f8f9fb"></uv-navbar>
		<view class="fixed-tab">
			<view class="fixed-tab-box" :style="'top: ' + divTop + 'px;'">
				<view class="page-tab">
					<uv-tabs :current="tabCurrent" :list="tabList" :lineColor="$c.themeColor()" @click="tabClick" itemStyle="width:33%;height:80rpx;font-size:28rpx" lineWidth="40"></uv-tabs>
				</view>
			</view>
		</view>
		<view class="page-wrap">
			<view class="search-container">
				<view class="flex-item">
					<uv-search placeholder="请输入单据号或申请人" v-model="KeyName" bgColor="#fff" :showAction="false" @search="search" @confirm="search"></uv-search>
				</view>
			</view>
			<view class="audit-list">
				<view class="list-item" v-for="(item, index) in list" :key="index" @click="goDetail(item)">
					<view class="list-item-header">
						<view class="title-h1">{{ item.ApplyTitle }}</view>
						<view><uv-tags :text="item.DealStatusName" :type="item.WfStatusType" shape="circle" size="mini"></uv-tags></view>
					</view>
					<view class="list-item-body">
						<view class="icon">
							<image :src="item.WfNameIcon" mode="widthFix"></image>
						</view>
						<view class="desc">
							<view class="li-item">申请时间：{{ item.SendDatetime }}</view>
							<view class="li-item">申请人：{{ item.ApplyUserName }}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view>
			<common-tabbar :initialValue="1" :role="userRole"></common-tabbar>
		</view>
		<!-- 加载更多 -->
		<view class="load-more">
			<uv-load-more fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
		</view>
		<!-- 回到顶部 -->
		<uv-back-top :duration="0" :scroll-top="scrollTop" mode="square" :icon-style="{ fontSize: '32rpx', color: '#fff' }" :custom-style="'background-color:' + $c.themeColor()"></uv-back-top>
	</view>
</template>

<script>
export default {
	data() {
		return {
			title: "审批",
			divTop: "",
			UserInfo: "",
			userRole: "",
			loadmore: {
				status: "loading",
				loadingText: "努力加载中",
				loadmoreText: "轻轻上拉",
				nomoreText: "没有更多了",
			},
			page: 1,
			scrollTop: 0,
			tabList: [
				{ name: "待审批", value: 0 },
				{ name: "已通过", value: 1 },
				{ name: "未通过", value: -1 },
			],
			tabCurrent: 0,
			DealStatus: 0,
			popShow: false,
			KeyName: "",
			list: [],
		};
	},
	onLoad() {
		var sys = uni.getSystemInfoSync();
		this.divTop = sys.statusBarHeight + 44;
		this.UserInfo = uni.getStorageSync("ImsUserInfo");
		this.userRole = uni.getStorageSync("ImsUserRole");
		this.getListData(1);
	},
	onUnload() {
		// 页面卸载时移除监听
		uni.$off("refreshList", this.refreshListener);
	},
	methods: {
		tabClick(item) {
			this.DealStatus = item.value;
			this.getListData(1);
		},
		handleListData(rawList) {
			if (!Array.isArray(rawList)) return [];
			const wfConfigMap = [
			  {
			    name: "用车申请",
			    icon: "/static/ims_images/<EMAIL>",
			    urlTemplate: "/ims/car/carDetail?Code={boCode}&type=audit&taskCode={taskCode}",
			  },
			  {
			    name: "请假申请",
			    icon: "/static/ims_images/<EMAIL>",
			    urlTemplate: "/ims/leave/leaveDetail?Code={boCode}&type=audit&taskCode={taskCode}",
			  },
			  {
			    name: "加班申请",
			    icon: "/static/ims_images/<EMAIL>",
			    urlTemplate: "/ims/overtim/overtimeDetail?Code={boCode}&type=audit&taskCode={taskCode}",
			  },
			  {
			    name: "换班申请",
			    icon: "/static/ims_images/<EMAIL>",
			    urlTemplate: "/ims/shiftChange/shiftChangeDetail?Code={boCode}&type=audit&taskCode={taskCode}",
			  },
			  {
			    name: "外出申请",
			    icon: "/static/ims_images/<EMAIL>",
			    urlTemplate: "/ims/goOut/goOutDetail?Code={boCode}&type=audit&taskCode={taskCode}",
			  },
			  {
			    name: "报销申请",
			    icon: "/static/ims_images/<EMAIL>",
			    urlTemplate: "/ims/reimburse/reimburseDetail?Code={boCode}&type=audit&taskCode={taskCode}",
			  },
			  {
			    name: "物资申购",
			    icon: "/static/ims_images/<EMAIL>",
			    urlTemplate: "/ims/purchase/purchaseDetail?Code={boCode}&type=audit&taskCode={taskCode}",
			  },
			  {
			    name: "付款申请",
			    icon: "/static/ims_images/<EMAIL>",
			    urlTemplate: "/ims/Payment/PaymentDetail?Code={boCode}&type=audit&taskCode={taskCode}",
			  },
			  {
			    name: "开票申请",
			    icon: "/static/ims_images/<EMAIL>",
			    urlTemplate: "/ims/invoice/invoiceDetail?Code={boCode}&type=audit&taskCode={taskCode}",
			  },
			];
			
			const defaultWfConfig = {
			  icon: "/static/ims_images/<EMAIL>",
			  urlTemplate: "/ims/other/otherDetail?Code={boCode}&type=audit&taskCode={taskCode}",
			};
			
			const dealStatusConfig = [
			  { value: 0, name: "待审批", type: "primary" },
			  { value: 1, name: "已通过", type: "success" },
			  { value: -1, name: "未通过", type: "error" }
			];
			
			// 2. 列表项格式化处理
			return rawList.map((item) => {
			  // 格式化时间
			  const formattedTime = item.SendDatetime 
			    ? uni.$uv.timeFormat(item.SendDatetime, "yyyy/mm/dd hh:MM") 
			    : "";
			
			  // 创建新对象（Immutable处理）
			  const newItem = { ...item, SendDatetime: formattedTime };
			
			  // 处理流程图标和URL
			  const config = wfConfigMap.find((conf) => conf.name === newItem.WfName) || defaultWfConfig;
			  newItem.WfNameIcon = config.icon;
			  newItem.Url = config.urlTemplate
			    .replace("{boCode}", newItem.BoCode)
			    .replace("{taskCode}", newItem.Code);
			
			  // 修复：获取状态配置（解决statusConfig未定义问题）
			  const statusConfig = dealStatusConfig.find(
			    (s) => s.value === newItem.DealStatus || s.name === newItem.DealStatusName
			  ) || { type: "warning" }; // 未知状态默认警告色
			
			  // 赋值状态类型
			  newItem.WfStatusType = statusConfig.type;
			  newItem.DealStatusName =statusConfig.name;
			
			  return newItem;
			});
		},

		getListData(page) {
			this.$apis
				.getApproveList(
					{
						GlobalUserCode: uni.getStorageSync("ImsUserCode"), //登录人
						DealStatus: this.DealStatus, //0待审核，-1不通过，1通过
						KeyName: this.KeyName, //申请标题或申请人查询
						PageSize: 10,
						PageIndex: page,
					},
					{ custom: { loading: false } }
				)
				.then((res) => {
					const processedList = this.handleListData(res.data); // 调用统一数据处理
					if (page === 1) {
						// 第一页：覆盖原有列表
						this.list = processedList;
					} else {
						// 分页加载：追加到原有列表
						this.list = [...this.list, ...processedList];
					}
					// 更新分页状态
					this.page = page;
					// 判断是否加载完毕
					if (processedList.length < 20) {
						this.loadmore.status = "nomore";
					} else {
						this.loadmore.status = "loadmore";
					}
				});
		},
		goDetail(item) {
			console.log(item);
			uni.navigateTo({ url:item.Url });
		},
	},
	onReachBottom() {
		if (this.loadmore.status == "nomore") {
			return;
		}
		var list = this.list;
		this.getListData(this.page + 1).then((res) => {
			for (let index in res) {
				list.push(res[index]);
			}
			if (res.length > 0) {
				this.list = list;
				this.page++;
				if (res.length < 10) {
					this.loadmore.status = "nomore";
				}
			}
		});
	},
	onPageScroll(e) {
		this.scrollTop = e.scrollTop;
	},
};
</script>

<style></style>
