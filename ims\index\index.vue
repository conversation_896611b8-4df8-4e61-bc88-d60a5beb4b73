<template>
	<view class="ims">
		<view class="home-header">
			<view class="item active">综合管理</view>
			<view class="item" @click="pms">物业管理</view>
		</view>
		<view class="home-main">
			<view class="home-userinfo">			
					<view class="item">
						{{ UserInfo.OrgName }}
					</view>
					<view class="item text-right">
						{{ UserInfo.UserName }},您好！
					</view>
				</u-row>
			</view>
			<view class="home-link">
			
						<view class="item">
							<image src="/static/ims_images/<EMAIL>" mode="widthFix"></image>
						</view>
				
						<view class="item">
							<image src="/static/ims_images/<EMAIL>" mode="widthFix"></image>
						</view>
					
				</u-row>
			</view>
			<view class="card">
				<view class="card-title">申请</view>
				<view class="card-body">
					<uv-grid :border="false" col="4" @click="link">
						<uv-grid-item v-for="(item, index) in indexList" :key="index">
							<view class="icon">
								<image :src="item.iconUrl" mode="widthFix"></image>
							</view>
							<text class="grid-text">{{ item.title }}</text>
						</uv-grid-item>
					</uv-grid>
				</view>
			</view>
			
		</view>
		<view>
			<common-tabbar  :initialValue="0"  :role="userRole" ></common-tabbar>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			UserInfo: "",
			userRole:"",
			indexList: [
				{
					iconUrl: "/static/ims_images/<EMAIL>",
					title: "用车申请",
					url: "/ims/car/carList",
				},
				{
					iconUrl: "/static/ims_images/<EMAIL>",
					title: "请假申请",
					url: "/ims/leave/leaveList",
				},
				{
					iconUrl: "/static/ims_images/<EMAIL>",
					title: "加班申请",
					url: "/ims/overtim/overtimeList",
				},
				{
					iconUrl: "/static/ims_images/<EMAIL>",
					title: "换班申请",
					url: "/ims/shiftChange/shiftChangeList",
				},
				{
					iconUrl: "/static/ims_images/<EMAIL>",
					title: "外出申请",
					url: "/ims/goOut/goOutList",
				},
				{
					iconUrl: "/static/ims_images/<EMAIL>",
					title: "报销申请",
					url: "/ims/reimburse/reimburseList",
				},
				{
					iconUrl: "/static/ims_images/<EMAIL>",
					title: "物资申购",
					url: "/ims/purchase/purchaseList",
				},
				{
					iconUrl: "/static/ims_images/<EMAIL>",
					title: "付款申请",
					url: "/ims/Payment/PaymentList",
				},
				{
					iconUrl: "/static/ims_images/<EMAIL>",
					title: "开票申请",
					url: "/ims/invoice/invoiceList",
				},
				{
					iconUrl: "/static/ims_images/<EMAIL>",
					title: "其他申请",
					url: "/ims/other/otherList",
				},
			],
		};
	},
	onLoad() {
		this.UserInfo = uni.getStorageSync("ImsUserInfo");
		this.userRole=uni.getStorageSync("ImsUserRole")
	},
	methods: {
		//跳转物业系统
		pms() {
			uni.navigateTo({
				url: "/pages/login/login",
			});
		},
		//九宫格跳转
		link(index) {
			//console.log(this.indexList[index].url);
			uni.navigateTo({
				url:this.indexList[index].url ,
			});
		},
		
	},
};
</script>
<style></style>
