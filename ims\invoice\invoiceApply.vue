<template>
	<view class="ims">
		<uv-navbar :title="title" :fixed="true" :placeholder="true" :autoBack="true" bgColor="#f8f9fb"></uv-navbar>
		<view class="page-wrap">
			<view class="page-tab">
				<uv-tabs :current="tabCurrent" :list="tabList" :lineColor="$c.themeColor()" @click="tabClick" itemStyle="width:50%;height:80rpx;font-size:28rpx" lineWidth="40"></uv-tabs>
			</view>
			<view class="form-pannel">
				<uv-form labelWidth="200" labelPosition="top">
					<view class="form-number" v-if="type === 'edit'">单据号：{{ form.BillNo }}</view>
					<uv-form-item label="申请标题" required>
						<uv-input border="none" v-model="form.ApplyTitle" type="text" fontSize="15px" placeholder="请输入申请标题" />
					</uv-form-item>
					<uv-form-item label="申请人">
						<uv-input border="none" v-model="form.ApplyUserName" type="text" fontSize="15px" disabledColor="#fff" disabled />
					</uv-form-item>
					<uv-form-item required label="申请日期">
						<view class="input-picker" @click="openCalendar('ApplyDate')">
							<view class="text">{{ form.ApplyDate || "请选择日期" }}</view>
							<view class="icon">
								<uv-icon name="arrow-right" color="#909193" size="14"></uv-icon>
							</view>
						</view>
					</uv-form-item>
					<uv-form-item required label="抬头类型">
						<view class="input-picker" @click="openPicker('headTypeColumns', 'HeadType', form.HeadType - 1)">
							<view class="text">{{ getHeadTypeLabel() || "请选择" }}</view>
							<view class="icon">
								<uv-icon name="arrow-right" color="#909193" size="14"></uv-icon>
							</view>
						</view>
					</uv-form-item>
					<uv-form-item label="抬头名称" required>
						<uv-input border="none" v-model="form.HeadName" type="text" fontSize="15px" placeholder="请输入发票抬头名称" />
					</uv-form-item>
					<uv-form-item v-if="form.HeadType == 1" label="纳税人识别号" required>
						<uv-input border="none" v-model="form.TaxNumber" type="text" fontSize="15px" placeholder="请输入纳税人识别号" />
					</uv-form-item>
					<uv-form-item label="税点（%）">
						<uv-input border="none" v-model="form.TaxPoint" type="text" fontSize="15px" placeholder="请输入税点" @input="handleAmountInput($event, 'TaxPoint')" @blur="handleAmountBlur('TaxPoint')" />
					</uv-form-item>
					<uv-form-item label="开票金额" required prop="InvoiceAmount">
						<uv-input border="none" v-model="form.InvoiceAmount" type="text" fontSize="15px" placeholder="请输入开票金额" @input="handleAmountInput($event, 'InvoiceAmount')" @blur="handleAmountBlur('InvoiceAmount')">
							<template v-slot:prefix>
								<uv-text text="¥" type="text"></uv-text>
							</template>
						</uv-input>
					</uv-form-item>
					<uv-form-item label="开票日期" required>
						<view class="input-picker" @click="openCalendar('InvoiceDate')">
							<view class="text">{{ form.InvoiceDate || "请选择日期" }}</view>
							<view class="icon">
								<uv-icon name="arrow-right" color="#909193" size="14"></uv-icon>
							</view>
						</view>
					</uv-form-item>
					<uv-form-item label="开票事由" required>
						<uv-textarea border="none" v-model="form.Reason" count placeholder="请输入开票事由"></uv-textarea>
					</uv-form-item>
					
					<uv-form-item label="附件">
						<common-upload :width="60" :height="60" :maxCount="20" :fileList="fileList" :code="form.Code"></common-upload>
					</uv-form-item>
				</uv-form>
			</view>
		</view>
		<view class="fixed-footer">
			<view class="fixed-footer-box">
				<view class="button-box">
					<view class="item">
						<uv-button type="primary" color="#94A5FF" shape="circle" @click="save">
							<uv-icon name="baocun" custom-prefix="custom-icon" size="20" color="#fff"></uv-icon>
							暂存
						</uv-button>
					</view>
					<view class="item">
						<uv-button type="primary" shape="circle" @click="submit">
							<uv-icon name="plane" custom-prefix="custom-icon" size="20" color="#fff"></uv-icon>
							提交
						</uv-button>
					</view>
				</view>
			</view>
		</view>
		<uv-calendars ref="calendar" :color="$c.themeColor()" :confirmColor="$c.themeColor()" :date="calendarDate" @confirm="confirmCalendar"></uv-calendars>

		<uv-picker ref="picker" :confirmColor="$c.themeColor()" :columns="pickerColumns" keyName="label" :defaultIndex="pickerDefaultIndex" @confirm="confirmPicker"></uv-picker>
	</view>
</template>

<script>
// 导入封装的mixin
import amountInputMixin from "@/utils/amountInputMixin.js";

export default {
	mixins: [amountInputMixin],
	data() {
		return {
			title: "开票申请",
			userInfo: {},
			tabList: [
				{ name: "发起申请", url: "/ims/invoice/invoiceApply" },
				{ name: "历史记录", url: "/ims/invoice/invoiceList" },
			],
			tabCurrent: 0,
			type: "", // 操作类型--新增，编辑
			newDate: "",
			form: {
				BillNo: "", // 单据号
				ApplyTitle: "", // 申请标题（必填）
				ApplyUserCode: "", // 申请人（必填）
				ApplyDeptCode: "", // 申请部门（必填）
				ApplyDate: "", // 申请日期（必填）
				HeadType: 1, // 抬头类型(必填) 1单位 2个人，设置默认值避免初始为空
				HeadName: "", // 抬头名称(必填)
				TaxNumber: "", // 纳税人识别号
				TaxPoint: "", // 税点
				InvoiceAmount: "", // 开票金额（必填）
				InvoiceDate: "", // 开票日期（必填）
				Reason: "", // 开票事由（必填）
				LoginUserCode: uni.getStorageSync("ImsUserCode"), // 登录人
				Code: "",
				ApplyUserName: "",
			},
			fileList: [],
			calendarDate: "",
			currentDateField: "", // 统一使用驼峰命名
			currentPickerField: "", // 修正变量名大小写，保持驼峰一致性
			pickerColumns: [],
			pickerDefaultIndex: [0], // 默认为数组类型
			// 抬头类型选择列表
			headTypeColumns: [
				[
					{ label: "单位", value: 1 },
					{ label: "个人", value: 2 },
				],
			],
		};
	},
	onLoad(options) {
		this.type = options.type || "add"; // 默认为新增类型
		// 设置默认日期
		this.newDate = uni.$uv.timeFormat(new Date().getTime(), "yyyy-mm-dd");
		this.form.ApplyDate = this.newDate;

		// 获取用户信息（添加容错处理）
		this.userInfo = uni.getStorageSync("ImsUserInfo") || {};
		this.form.ApplyUserName = this.userInfo.UserName || "";
		this.form.ApplyUserCode = uni.getStorageSync("ImsUserCode") || "";
		this.form.ApplyDeptCode = this.userInfo.OrgCode || "";

		// 编辑模式加载详情
		if (options.type === "edit" && options.Code) {
			this.form.Code = options.Code;
			this.getInvoiceDetail();
		} else {
			this.getGuid();
		}
	},
	onUnload() {
		// 页面卸载时移除监听
		uni.$off("refreshList", this.refreshListener);
	},
	methods: {
		getGuid() {
			this.$apis
				.getGuid({})
				.then((res) => {
					this.form.Code = res.data;
				})
				.catch((err) => {
					console.error("获取Guid失败", err);
					this.$uv.toast("获取Guid失败，请重试");
				});
		},
		getInvoiceDetail() {
			this.$apis.getInvoiceApplyDetail({ Code: this.form.Code }).then((res) => {
				this.form = res.data.InvoiceApply;
				this.form.ApplyDate = uni.$uv.date(this.form.ApplyDate, "yyyy/mm/dd");
				this.form.InvoiceDate = uni.$uv.date(this.form.InvoiceDate, "yyyy/mm/dd");
				this.form.LoginUserCode = uni.getStorageSync("ImsUserCode");
			});
		},
		getFileList(){
			this.$apis
				.getPhotoAttachmentList({
						Code:this.form.Code
					})
				.then((res) => {
					this.fileList = res.data
						 //console.log(this.innerFileList)
				})
				.catch((err) => {
					console.error("获取图片失败", err);
					this.$uv.toast("获取图片失败，请重试");
				});
		},
		tabClick(item) {
			uni.navigateTo({ url: item.url });
		},

		// 打开日历选择器
		openCalendar(field) {
			this.currentDateField = field;
			// 处理日期格式，确保日历正确显示
			this.calendarDate = this.form[field] || this.newDate;
			this.$refs.calendar?.open(); // 使用可选链避免未定义错误
		},

		// 确认选择日期
		confirmCalendar(e) {
			if (this.currentDateField && e.fulldate) {
				this.form[this.currentDateField] = uni.$uv.date(e.fulldate, "yyyy/mm/dd");
				this.$refs.calendar?.close(); // 手动关闭日历
			}
		},

		// 打开选择器
		openPicker(columnsKey, field, index) {
			// 记录当前操作的字段
			this.currentPickerField = field;
			// 设置选择器数据
			this.pickerColumns = this[columnsKey] || [];
			// 设置默认索引（确保是数组类型）
			if (index !== undefined && index >= 0) {
				this.pickerDefaultIndex = [index]; // 正确设置为数组
			} else {
				this.pickerDefaultIndex = [0]; // 默认选中第一项
			}
			this.$refs.picker?.open(); // 使用可选链避免错误
		},

		// 确认选择器结果
		confirmPicker(e) {
			if (this.currentPickerField && e.indexs && e.indexs.length > 0) {
				// 计算选中值（索引+1）
				this.form[this.currentPickerField] = Number(e.indexs[0]) + 1;
				this.$refs.picker?.close(); // 手动关闭选择器
			}
		},

		// 获取抬头类型显示文本
		getHeadTypeLabel() {
			// 容错处理：避免数组为空导致的错误
			if (!this.headTypeColumns[0]?.length) return "";
			const item = this.headTypeColumns[0].find((item) => item.value === this.form.HeadType);
			return item?.label || "";
		},

		formRules() {
			if (!this.form.ApplyTitle) {
				return uni.$uv.toast("请输入申请标题");
			}
			if (!this.form.ApplyDate) {
				return uni.$uv.toast("请选择申请时间");
			}
			if (!this.form.HeadType) {
				return uni.$uv.toast("请选择发票抬头类型");
			}
			if (!this.form.HeadName) {
				return uni.$uv.toast("请输入发票抬头名称");
			}
			if (this.form.HeadType == 1 && !this.form.TaxNumber) {
				return uni.$uv.toast("请输入纳税人识别号");
			}
			if (!this.form.InvoiceAmount) {
				return uni.$uv.toast("请输入开票金额");
			}
			// 金额格式验证（使用正则，不依赖框架内置方法，确保可靠）
			const amountReg = /^[1-9]\d*(\.\d{1,2})?$|^0(\.\d{1,2})?$/;
			if (!amountReg.test(this.form.InvoiceAmount)) {
				uni.$uv.toast("请输入正确的金额（正数字，小数点后最多2位）");
				return false;
			}
			if (!this.form.InvoiceDate) {
				return uni.$uv.toast("请选择开票日期");
			}
			if (!this.form.Reason) {
				return uni.$uv.toast("请选择开票事由");
			}
			// 所有验证通过
			return true;
		},

		//暂存
		save() {
			if (!this.formRules()) {
				return; // 验证失败，终止保存
			}
			// 所有验证通过，调用保存接口
			this.$apis[this.type == "add" ? "addInvoiceApply" : "editInvoiceApply"](this.form).then((res) => {
				if (res.code === 100) {
					uni.$uv.toast("保存成功");
					setTimeout(() => {
						uni.navigateBack();
						if (this.type == "add") {
							uni.$emit("refreshList");
						} else {
							uni.$emit("refreshDetail");
						}
					}, 1500);
				} else {
					uni.$uv.toast(res.msg || "保存失败");
				}
			});
		},
		//提交
		submit() {
			if (!this.formRules()) {
				return; // 验证失败，终止保存
			}
			// 所有验证通过，调用提交接口
			this.$apis.submitInvoiceApply(this.form).then((res) => {
				if (res.code === 100) {
					uni.$uv.toast("提交成功");
					setTimeout(() => {
						uni.navigateBack();
						if (this.type == "add") {
							uni.$emit("refreshList");
						} else {
							uni.$emit("refreshDetail");
						}
					}, 1500);
				} else {
					uni.$uv.toast(res.msg || "提交失败");
				}
			});
		},
	},
};
</script>

<style></style>
