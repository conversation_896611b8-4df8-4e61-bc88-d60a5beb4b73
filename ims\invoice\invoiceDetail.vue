<template>
	<view class="ims">
		<uv-navbar :title="title" :fixed="true" :placeholder="true" :autoBack="true" bgColor="#f8f9fb"></uv-navbar>
		<view class="page-wrap">
			<common-audit-progress-top :code="Code" v-if="detail.WfStatus > -1"></common-audit-progress-top>
			<view class="detail-cell">
				<view class="title-h1">单据号：{{ detail.BillNo }}</view>
				<view><uv-tags :text="detail.WfStatusCaption" :type="detail.WfStatusType" shape="circle" size="mini"></uv-tags></view>
			</view>
			<view class="card">
				<view class="card-header bor-b">
					{{ detail.ApplyTitle }}
				</view>
				<view class="card-body">
					<view class="field-list">
						<view class="field-list-item">
							<view class="label">申请人</view>
							<view class="text">
								{{ detail.ApplyUserName }}
							</view>
						</view>
						<view class="field-list-item">
							<view class="label">申请时间</view>
							<view class="text">
								{{ formattedDate(detail.ApplyDate) }}
							</view>
						</view>
						<view class="field-list-item">
							<view class="label">抬头类型</view>
							<view class="text">
								{{ detail.HeadTypeName }}
							</view>
						</view>
						<view class="field-list-item">
							<view class="label">抬头名称</view>
							<view class="text">
								{{ detail.HeadName }}
							</view>
						</view>
						<view class="field-list-item">
							<view class="label">纳税人识别号</view>
							<view class="text">
								{{ detail.TaxNumber }}
							</view>
						</view>
						<view class="field-list-item">
							<view class="label">税点</view>
							<view class="text">{{ detail.TaxPoint }}%</view>
						</view>
						<view class="field-list-item">
							<view class="label">开票金额</view>
							<view class="text">¥{{ detail.InvoiceAmount }}</view>
						</view>
						<view class="field-list-item">
							<view class="label">开票日期</view>
							<view class="text">
								{{ formattedDate(detail.InvoiceDate) }}
							</view>
						</view>
						<view class="field-list-item">
							<view class="label">开票事由</view>
							<view class="text">
								{{ detail.Reason }}
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="card" >
				<view class="card-header bor-b">附件</view>
				<view class="card-body">
					<common-upload :width="60" :height="60" :maxCount="10" :fileList="fileList" :code="Code"></common-upload>
				</view>
			</view>
			<common-approve :code="Code" :taskCode="taskCode" api="approveInvoiceApply" v-if="shouldShowApprovalForm" commentPlaceholder="请输入发票审批意见" :requireCommentWhenReject="true" @approvalSuccess="handleApprovalSuccess" />
			
			
			<common-audit-progress :code="Code" v-if="detail.WfStatus > -1"></common-audit-progress>
		</view>
		<view class="fixed-footer" v-if="userInfo.Code == detail.ApplyUserCode && (detail.WfStatus === -1 || detail.WfStatus === 2)">
			<view class="fixed-footer-box">
				<view class="button-box">
					<view class="item">
						<uv-button type="primary" color="#94A5FF" shape="circle" @click="$c.navigateTo('../invoice/invoiceApply?type=edit&Code=' + Code)">
							<uv-icon name="baocun" custom-prefix="custom-icon" size="20" color="#fff"></uv-icon>
							编辑
						</uv-button>
					</view>
					<view class="item">
						<uv-button type="primary" shape="circle" @click="submit">
							<uv-icon name="plane" custom-prefix="custom-icon" size="20" color="#fff"></uv-icon>
							提交
						</uv-button>
					</view>
					<view class="item">
						<uv-button type="error" shape="circle" @click="">
							<uv-icon name="shanchu1" custom-prefix="custom-icon" size="20" color="#fff"></uv-icon>
							删除
						</uv-button>
					</view>
				</view>
			</view>
		</view>
		<uv-picker ref="picker" :confirmColor="$c.themeColor()" :columns="pickerColumns" keyName="label" :defaultIndex="pickerDefaultIndex" @confirm="confirmPicker"></uv-picker>
	</view>
</template>

<script>
// 引入日期格式化工具
import { formatDate } from "@/utils/dateFormat.js";
export default {
	data() {
		return {
			title: "开票申请",
			type: "",
			Code: "",
			LoginUserCode: "",
			taskCode: "",
			userInfo: {},
			detail: [],
			fileLength: 0,
			fileList: [],
			currentPickerField: "", // 修正变量名大小写，保持驼峰一致性
			pickerColumns: [],
			pickerDefaultIndex: [0], // 默认为数组类型
		};
	},
	onLoad(options) {
		this.type = options.type; // ""为详情，approve为审批
		// 获取用户信息（添加容错处理）
		this.userInfo = uni.getStorageSync("ImsUserInfo") || {};
		this.LoginUserCode = uni.getStorageSync("ImsUserCode");
		this.Code = options.Code;
		this.taskCode = options.taskCode;
		this.getDetail();
	},
	
	onUnload() {
		// 页面卸载时移除监听
		uni.$off("refreshList", this.refreshListener);
	},
	computed: {
		shouldShowApprovalForm() {
			// 用常量替代魔术数字，提高可维护性（建议在常量文件中定义）
			const WF_STATUS_PENDING = 0; // 待审批状态
			return this.detail.WfStatus === WF_STATUS_PENDING && this.type === "audit";
		},
	},
	methods: {
		//提交
		submit() {
			// 所有验证通过，调用提交接口
			this.$apis
				.submitInvoiceApply({
					Code: this.Code,
					LoginUserCode: this.LoginUserCode,
				})
				.then((res) => {
					if (res.code === 100) {
						uni.$uv.toast("提交成功");
						setTimeout(() => {
							uni.navigateBack();
							if (this.type == "add") {
								uni.$emit("refreshList");
							} else {
								uni.$emit("refreshDetail");
							}
						}, 1500);
					} else {
						uni.$uv.toast(res.msg || "提交失败");
					}
				});
		},
		// 获取审批状态显示文本
		getApproveColumnsLabel() {
			// 容错处理：避免数组为空导致的错误
			if (!this.ApproveColumns[0]?.length) return "";
			const item = this.ApproveColumns[0].find((item) => item.value === this.approve.DealStatus);
			return item?.label || "";
		},
		// 打开选择器
		openPicker(columnsKey, field, index) {
			// 记录当前操作的字段
			this.currentPickerField = field;
			// 设置选择器数据
			this.pickerColumns = this[columnsKey] || [];
			// 设置默认索引（确保是数组类型）
			if (index !== undefined && index >= 0) {
				this.pickerDefaultIndex = [index]; // 正确设置为数组
			} else {
				this.pickerDefaultIndex = [0]; // 默认选中第一项
			}
			this.$refs.picker?.open(); // 使用可选链避免错误
		},

		// 确认选择器结果
		confirmPicker(e) {
			if (this.currentPickerField && e.indexs && e.indexs.length > 0) {
				// 计算选中值（索引+1）
				this.approve[this.currentPickerField] = Number(e.indexs[0]);
				this.$refs.picker?.close(); // 手动关闭选择器
			}
		},
		// 修正详情数据处理函数
		handleDetailData(rawDetail) {
			// 详情数据通常是单个对象（非数组），调整判断逻辑
			if (!rawDetail || typeof rawDetail !== "object") return {};
			// 复制原对象，避免直接修改
			const newItem = { ...rawDetail };

			// 处理HeadType -> HeadTypeName
			switch (newItem.HeadType) {
				case 1:
					newItem.HeadTypeName = "单位";
					break;
				case 2:
					newItem.HeadTypeName = "个人";
					break;
				default:
					newItem.HeadTypeName = "未知";
			}

			// 处理WfStatus -> WfStatusType
			switch (newItem.WfStatus) {
				case -1:
					newItem.WfStatusType = "warning";
					break;
				case 0:
					newItem.WfStatusType = "success";
					break;
				case 1:
					newItem.WfStatusType = "primary";
					break;
				default:
					newItem.WfStatusType = "error";
			}
			return newItem;
		},

		// 获取详情数据
		getDetail() {
			this.$apis
				.getInvoiceApplyDetail({ Code: this.Code })
				.then((res) => {
					// 假设res.data.InvoiceApply是单个详情对象（非数组）
					const processedDetail = this.handleDetailData(res.data.InvoiceApply);
					this.detail = processedDetail; // 赋值为处理后的对象
				})
				.catch((err) => {
					console.error("获取详情数据失败", err);
					this.$uv.toast("获取详情失败，请重试");
				});
		},
		//获取附件列表
		getAttachmentList() {
			apis.getPhotoAttachmentList({
				Code: this.Code,
			})
				.then((res) => {
					const rawData = res.data || []; // 兜底：若接口返回null/undefined，默认空数组
					this.fileList = Array.isArray(rawData)
						? rawData.map((item) => ({
								url: item.FileUrl || "", // 兜底：避免url为undefined导致预览异常
								name: item.FileName || "未知文件名", // 文件名兜底
								id: item.Id || "", // ID兜底（避免删除时无标识）
								status: "success",
						  }))
						: []; // 若rawData不是数组，直接设为空数组

					this.fileLength = this.fileList.length; // 同步记录长度
				})
				.catch((err) => {
					console.error("获取附件列表失败", err);
					this.$uv.toast("获取附件失败，请重试");
				});
		},

		formattedDate(dateStr) {
			return formatDate(dateStr); // 工具函数已处理空值，无需额外判断
		},
	},
};
</script>

<style></style>
