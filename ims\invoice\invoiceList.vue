<template>
	<view class="ims">
		<uv-navbar :title="title" :fixed="true" :placeholder="true" :autoBack="true" bgColor="#f8f9fb"></uv-navbar>
		<view class="page-wrap">
			<!-- 标签栏 -->
			<view class="page-tab">
				<uv-tabs :current="tabCurrent" :list="tabList" :lineColor="$c.themeColor()" @click="tabClick" itemStyle="width:50%;height:80rpx;font-size:28rpx" lineWidth="40"></uv-tabs>
			</view>
			<!-- 搜索框 -->
			<view class="search-container">
				<uv-search placeholder="请输入单据号或申请标题" v-model="keyWord" bgColor="#fff" :showAction="false" @search="search" @confirm="search"></uv-search>
			</view>
			<!-- 列表区域 -->
			<view class="list">
				<view class="list-item" v-for="(item, index) in list" :key="index" @click="$c.navigateTo('../invoice/invoiceDetail?Code=' + item.Code)">
					<view class="list-item-header">
						<view class="title-h1">{{ item.BillNo }}</view>
						<view><uv-tags :text="item.WfStatusCaption" :type="item.WfStatusType" shape="circle" size="mini"></uv-tags></view>
					</view>
					<view class="list-item-body">
						<view class="li-title">{{ item.ApplyTitle }}</view>
						<!-- 日期格式化：已有处理，保留 -->
						<view class="li-item">申请时间：{{ formattedDate(item.ApplyDate) }}</view>
						<!-- 关键修改：显示HeadTypeName，加默认值避免异常 -->
						<view class="li-item">开票抬头：[{{ item.HeadTypeName }}]{{ item.HeadName }}</view>
						<view class="li-item">
							开票金额：
							<text class="primary-color">¥{{ item.InvoiceAmount }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 加载更多 -->
		<view class="load-more">
			<uv-load-more fontSize="12" color="#999" :status="loadmore.status" :loading-text="loadmore.loadingText" :loadmore-text="loadmore.loadmoreText" :nomore-text="loadmore.nomoreText" />
		</view>
		<!-- 回到顶部 -->
		<uv-back-top :duration="0" :scroll-top="scrollTop" mode="square" :icon-style="{ fontSize: '32rpx', color: '#fff' }" :custom-style="'background-color:' + $c.themeColor()"></uv-back-top>
	</view>
</template>
<script>
// 引入日期格式化工具
import { formatDate } from "@/utils/dateFormat.js";

export default {
	data() {
		return {
			title: "开票申请",
			loadmore: {
				status: "loading",
				loadingText: "努力加载中",
				loadmoreText: "轻轻上拉",
				nomoreText: "没有更多了",
			},
			page: 1,
			scrollTop: 0,
			tabList: [
				{ name: "发起申请", url: "/ims/invoice/invoiceApply" },
				{ name: "历史记录", url: "/ims/invoice/invoiceList" },
			],
			tabCurrent: 1, // 默认选中"历史记录"
			keyWord: "", // 搜索关键词
			list: [], // 列表数据
		};
	},

	onLoad() {
		// 页面加载时获取第一页数据
		this.getListData(1);
		// 注册全局事件监听
		this.refreshListener = uni.$on("refreshList", () => {
			// 监听到事件后，重新加载第一页数据
			this.page = 1; // 重置页码
			this.loadmore.status = "loading"; // 重置加载状态
			this.getListData(1); // 重新请求数据
		});
	},
	onUnload() {
		// 页面卸载时移除监听
		uni.$off("refreshList", this.refreshListener);
	},
	methods: {
		handleListData(rawList) {
			if (!Array.isArray(rawList)) return [];
			return rawList.map((item) => {
				// 复制原对象，避免直接修改（ Immutable 思想）
				const newItem = { ...item };
				// 根据HeadType设置HeadTypeName（可扩展其他类型）
				switch (newItem.HeadType) {
					case 1:
						newItem.HeadTypeName = "单位";
						break;
					case 2:
						newItem.HeadTypeName = "个人"; // 示例：若有其他类型可添加
						break;
					default:
						newItem.HeadTypeName = "未知"; // 默认值，避免显示空/undefined
				}
				switch (newItem.WfStatus) {
					case -1:
						newItem.WfStatusType = "warning";
						break;
					case 0:
						newItem.WfStatusType = "success"; // 示例：若有其他类型可添加
						break;
					case 1:
						newItem.WfStatusType = "primary"; // 示例：若有其他类型可添加
						break;
					default:
						newItem.WfStatusType = "error"; // 默认值，避免显示空/undefined
				}
				return newItem;
			});
		},
		getListData(page) {
			this.$apis
				.getInvoiceApplyList(
					{
						PageIndex: page,
						PageSize: 20,
						LoginUserCode: uni.getStorageSync("ImsUserCode"),
						KeyWord: this.keyword,
					},
					{ custom: { loading: false } }
				)
				.then((res) => {
					const processedList = this.handleListData(res.data); // 调用统一数据处理
					if (page === 1) {
						// 第一页：覆盖原有列表
						this.list = processedList;
					} else {
						// 分页加载：追加到原有列表
						this.list = [...this.list, ...processedList];
					}
					// 更新分页状态
					this.page = page;
					// 判断是否加载完毕
					if (processedList.length < 20) {
						this.loadmore.status = "nomore";
					} else {
						this.loadmore.status = "loadmore";
					}
				});
		},
		formattedDate(dateStr) {
			return formatDate(dateStr); // 工具函数已处理空值，无需额外判断
		},
		tabClick(item) {
			uni.navigateTo({ url: item.url });
		},
		search(e) {
			this.page = 1; // 搜索时从第一页开始
			this.loadmore.status = "loading"; // 重置加载状态
			this.getListData(1); // 调用封装的请求方法
			// 搜索后回到顶部
			if (e) {
				uni.pageScrollTo({ scrollTop: 0 });
			}
		},
	},
	onReachBottom() {
		// 若已无更多数据，直接返回
		if (this.loadmore.status === "nomore") return;
		// 请求下一页数据
		this.getListData(this.page + 1);
	},
	onPageScroll(e) {
		this.scrollTop = e.scrollTop;
	},
};
</script>

<style></style>
