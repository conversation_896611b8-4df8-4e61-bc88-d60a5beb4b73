<template>
  <view class="ims">
    <uv-navbar title="修改手机号" :fixed="true" :placeholder="true" :autoBack="true" bgColor="#f8f9fb"></uv-navbar>
    <view class="page-wrap">
      <view class="form-pannel">
        <uv-form labelWidth="200" labelPosition="top">
          <view class="form-number">修改手机号</view>
          <uv-form-item label="原手机号">
            <uv-input border="none" v-model="form.CellPhone" type="text" fontSize="15px" placeholder="请输入原手机号" disabled disabledColor="#fff" />
          </uv-form-item>
          <uv-form-item label="短信验证码" required>
            <uv-input border="none" v-model="form.VerifyCode" type="text" fontSize="15px" placeholder="请输入短信验证码" />
            <uv-button type="primary" shape="circle" customStyle="height:60rpx;line-height:60rpx;background: #94A5FF;border-color:#94A5FF" @click="getVerifyCode">{{ tips }}</uv-button>
          </uv-form-item>
          <uv-form-item label="新手机号" required>
            <uv-input border="none" v-model="form.CellPhoneNew" type="text" fontSize="15px" placeholder="请输入新手机号" />
          </uv-form-item>
          <uv-button type="primary" size="large" :customStyle="'height:42px;line-height:42px;margin-top: 20px;width:100%;background:' + $c.themeColor()" shape="circle" @click="submit">
            <uv-icon name="plane" custom-prefix="custom-icon" size="20" color="#fff"></uv-icon>
            确认修改
          </uv-button>
        </uv-form>
      </view>
    </view>
    <uv-code seconds="10" ref="uCode" @change="codeChange"></uv-code>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        CellPhone: uni.getStorageSync("ImsUserInfo").CellPhone,
        CellPhoneNew: "",
        LoginName: uni.getStorageSync("ImsUserInfo").CellPhone,
        VerifyCode: "",
      },
      tips: "获取验证码",
      timer: "",
    };
  },
  methods: {
    codeChange(text) {
      this.tips = text;
    },
    //获取验证码
    getVerifyCode() {
      if (this.$refs.uCode.canGetCode) {
        // 模拟向后端请求验证码
        uni.showLoading({
          title: "正在获取验证码",
        });
        this.$apis
          .getSms(
            {
              CellPhone: this.form.CellPhone,
            },
            { custom: { loading: false } }
          )
          .then((res) => {
            uni.hideLoading();
            if (res.code == 100) {
              uni.$uv.toast("验证码已发送");
              this.$refs.uCode.start();
            } else {
              uni.$uv.toast(res.msg);
            }
          });
      } else {
        uni.$uv.toast("倒计时结束后再发送");
      }
    },
    submit() {
      if (!this.form.VerifyCode) {
        uni.$uv.toast("请输入短信验证码");
        return;
      }
      if (!this.form.CellPhoneNew) {
        uni.$uv.toast("请输入新手机号");
        return;
      }
      if (!uni.$uv.test.mobile(this.form.CellPhoneNew)) {
        uni.$uv.toast("请输入正确的手机号");
        return;
      }
      this.$apis.modifyPhone(this.form).then((res) => {
        if (res.code === 100) {
          uni.showToast({
            title: "修改成功",
            icon: "success",
            duration: 1500,
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } else {
          uni.$uv.toast(res.msg);
        }
      });
    },
  },
};
</script>

<style></style>
