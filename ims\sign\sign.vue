<template>
  <view>
    <uv-navbar title="外勤打卡" :fixed="true" :placeholder="true" :autoBack="true" bgColor="#f8f9fb"></uv-navbar>
    <view class="sign-top flex m30">
      <view class="avatar">
        <image src="/static/ims_images/default_avatar.png" mode="widthFix"></image>
      </view>
      <view class="flex-bd" v-if="UserInfo">
        <view class="name">
          <text>{{ UserInfo.UserName }}</text>
          <text class="code">{{ UserInfo.UserCode }}</text>
        </view>
        <view class="dept">
          <text>{{ UserInfo.DeptName }}</text>
        </view>
      </view>
      <view class="btn">
        <image src="/static/ims_images/icon-sign.png" mode="widthFix"></image>
        <view>选择申请单</view>
      </view>
    </view>
    <view class="sign-info m30">
      <view class="no">单据号8898898</view>
      <view class="title">现场处理</view>
      <view class="info">
        <view>申请时间：2025-08-26 10:00:00</view>
        <view>外出时间：2025-08-26 10:00:00~2025-08-26 10:00:00</view>
      </view>
    </view>
    <view class="sign-btn m30">
      <view class="title">外勤打卡</view>
      <view class="btn">
        <view class="t">
          <view class="t1">到场打卡</view>
          <view class="t2">09:12:30</view>
        </view>
      </view>
      <view class="addr">资料科技城111112223123</view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      UserInfo: {},
    };
  },
  onLoad() {
    this.UserInfo = uni.getStorageSync("ImsUserInfo");
  },
  methods: {},
};
</script>

<style></style>
