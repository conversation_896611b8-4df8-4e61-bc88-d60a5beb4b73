<template>
  <view class="ims">
    <uv-navbar title="个⼈信息" :fixed="true" :placeholder="true" :autoBack="true" bgColor="#f8f9fb"></uv-navbar>
    <view class="page-wrap">
      <view class="card">
        <view class="card-header bor-b">基本信息</view>
        <view class="card-body">
          <view class="field-list">
            <view class="field-list-item">
              <view class="label">姓名</view>
              <view class="text">
                {{ UserInfo.UserName }}
              </view>
            </view>
            <view class="field-list-item">
              <view class="label">申请时间</view>
              <view class="text"></view>
            </view>
            <view class="field-list-item">
              <view class="label">抬头类型</view>
              <view class="text">
                {{ UserInfo.HeadTypeName }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      UserInfo: "",
    };
  },
  methods: {},
  onLoad() {
    this.UserInfo = uni.getStorageSync("ImsUserInfo");
  },
};
</script>

<style>
.ims .page-wrap .card .card-body {
  padding-bottom: 0;
}
</style>
