<template>
  <view class="ims">
    <uv-navbar title="个⼈信息" :fixed="true" :placeholder="true" :autoBack="true" bgColor="#f8f9fb"></uv-navbar>
    <view class="page-wrap">
      <!-- 基本信息 -->
      <view class="card">
        <view class="card-header bor-b">基本信息</view>
        <view class="card-body">
          <view class="field-list">
            <view class="field-list-item">
              <view class="label">姓名</view>
              <view class="text">{{ UserInfo.UserName || '张亮' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">工号</view>
              <view class="text">{{ UserInfo.UserNumber || '28888' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">身份证号</view>
              <view class="text">{{ UserInfo.IdCard || '320602200501011111' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">性别</view>
              <view class="text">{{ formatSex(UserInfo.Sex) || '男' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">出生日期</view>
              <view class="text">{{ formatDate(UserInfo.BirthDate) || '2005/08/20' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">民族</view>
              <view class="text">{{ UserInfo.Nationality || '汉族' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">籍贯</view>
              <view class="text">{{ UserInfo.NativePlace || '江苏南通' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">政治面貌</view>
              <view class="text">{{ UserInfo.Political || '共青团员' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">学历</view>
              <view class="text">{{ UserInfo.Education || '研究生' }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="card">
        <view class="card-header bor-b">联系方式</view>
        <view class="card-body">
          <view class="field-list">
            <view class="field-list-item">
              <view class="label">联系电话</view>
              <view class="text">{{ UserInfo.CellPhone || '18888888888' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">联系地址</view>
              <view class="text">{{ UserInfo.Address || '江苏南通启东新城中心999999' }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 薪资信息 -->
      <view class="card">
        <view class="card-header bor-b">薪资信息</view>
        <view class="card-body">
          <view class="field-list">
            <view class="field-list-item">
              <view class="label">项目点</view>
              <view class="text">{{ UserInfo.OrgName || '行政中心' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">岗位</view>
              <view class="text">{{ UserInfo.JobName || '销售' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">入职日期</view>
              <view class="text">{{ formatDate(UserInfo.EntryDate) || '2005/08/20' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">工龄</view>
              <view class="text">{{ calculateWorkYears(UserInfo.EntryDate) || '5' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">持有证件</view>
              <view class="text">{{ UserInfo.Certificate || '驾照' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">试用期限（月）</view>
              <view class="text">{{ UserInfo.OnTrialMonth || '3' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">社保开始日期</view>
              <view class="text">{{ formatDate(UserInfo.SocialSecurity) || '2020/01' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">合同类型</view>
              <view class="text">{{ UserInfo.ContractType || '合同' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">期限类型</view>
              <view class="text">{{ UserInfo.DeadlineType || '3年' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">开始日期</view>
              <view class="text">{{ formatDate(UserInfo.StartDate) || '2020/01/01' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">截止日期</view>
              <view class="text">{{ formatDate(UserInfo.EndDate) || '2023/01/01' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">转正日期</view>
              <view class="text">{{ formatDate(UserInfo.RegularDate) || '2020/03/01' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">银行信息</view>
              <view class="text">{{ UserInfo.BankInfo || '中国银行' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">薪资卡号</view>
              <view class="text">{{ UserInfo.BankAccount || '6568 9068 8888 444' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">人员等级</view>
              <view class="text">{{ UserInfo.UserLevel || '一级' }}</view>
            </view>
            <view class="field-list-item">
              <view class="label">在职状态</view>
              <view class="text">{{ formatOnDutyStatus(UserInfo.OnDutyStatus) || '在职' }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      UserInfo: "",
    };
  },
  methods: {
    // 格式化性别
    formatSex(sex) {
      if (!sex) return '';
      return sex === '1' || sex === 1 ? '男' : '女';
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      if (isNaN(date.getTime())) return dateStr;

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}/${month}/${day}`;
    },

    // 计算工龄
    calculateWorkYears(entryDate) {
      if (!entryDate) return '';
      const entry = new Date(entryDate);
      const now = new Date();
      if (isNaN(entry.getTime())) return '';

      const years = Math.floor((now - entry) / (365.25 * 24 * 60 * 60 * 1000));
      return years > 0 ? `${years}年` : '不足1年';
    },

    // 格式化在职状态
    formatOnDutyStatus(status) {
      if (!status) return '';
      const statusMap = {
        '1': '在职',
        '0': '离职',
        '2': '试用期',
        '3': '停薪留职'
      };
      return statusMap[status] || status;
    }
  },
  onLoad() {
    this.UserInfo = uni.getStorageSync("ImsUserInfo");
    console.log('UserInfo:', this.UserInfo);
  },
};
</script>

<style>
.ims .page-wrap .card .card-body {
  padding-bottom: 0;
}

.ims .field-list .field-list-item .text {
  word-break: break-all;
  text-align: right;
}

.ims .field-list .field-list-item .label {
  color: #767a82;
  font-weight: 500;
}
</style>
