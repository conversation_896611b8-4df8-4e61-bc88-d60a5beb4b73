<template>
  <view class="ims">
    <uv-navbar title="我的" :fixed="true" :placeholder="true" :autoBack="false" bgColor="#f8f9fb">
      <template v-slot:left><view></view></template>
    </uv-navbar>
    <view class="user">
      <view class="user-info">
        <view class="bg-img">
          <image src="/static/ims_images/<EMAIL>" mode="widthFix"></image>
        </view>
        <view class="wrap flex">
          <view class="avatar">
            <image src="/static/ims_images/default_avatar.png" mode="widthFix"></image>
          </view>
          <view class="flex-bd" v-if="UserInfo">
            <view class="name">
              <text>{{ UserInfo.UserName }}</text>
              <text class="code">工号 {{ UserInfo.UserNumber }}</text>
            </view>
            <view class="dept">
              <text>{{ UserInfo.OrgName }}</text>
            </view>
          </view>
          <view class="flex-bd" v-else @click="navTo('/pages/imsLogin/imsLogin')">
            <view class="name">
              <text>请先登录</text>
            </view>
          </view>
        </view>
      </view>
      <view class="card-list">
        <uv-list>
          <uv-list-item clickable @click="navToC('/ims/userInfo/userInfo')" title="个人信息" thumb-size="lg" thumb="/static/ims_images/<EMAIL>" :border="false" :show-arrow="true"></uv-list-item>
          <uv-list-item clickable @click="navToC('/ims/modifyPhone/modifyPhone')" title="修改手机号" thumb-size="lg" thumb="/static/ims_images/<EMAIL>" :border="true" :show-arrow="true"></uv-list-item>
          <uv-list-item clickable @click="navToC('/ims/attendance/attendance')" title="我的考勤" thumb-size="lg" thumb="/static/ims_images/<EMAIL>" :border="true" :show-arrow="true"></uv-list-item>
          <uv-list-item clickable @click="navToC('/ims/sign/sign')" title="外勤打卡" thumb-size="lg" thumb="/static/ims_images/<EMAIL>" :border="true" :show-arrow="true"></uv-list-item>
          <uv-list-item clickable @click="pms()" title="物业管理系统" thumb-size="lg" thumb="/static/ims_images/<EMAIL>" :border="true" :show-arrow="true"></uv-list-item>
        </uv-list>
      </view>
      <view class="mr30">
        <uv-button type="primary" size="large" :customStyle="'height:42px;line-height:42px;width:100%;background:' + $c.themeColor()" shape="circle" @click="submit">退出登录</uv-button>
      </view>
    </view>
    <view>
      <common-tabbar :initialValue="3" :role="userRole"></common-tabbar>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      UserInfo: "",
      userRole: "",
    };
  },
  methods: {
    //跳转物业系统
    pms() {
      uni.navigateTo({
        url: "/pages/login/login",
      });
    },
    navTo(url) {
      uni.navigateTo({
        url: url,
      });
    },
    navToC(url) {
      if (!uni.getStorageSync("ImsUserCode")) {
        uni.showToast({
          title: "请先登录",
          icon: "none",
        });
        setTimeout(() => {
          uni.navigateTo({
            url: "/pages/imsLogin/imsLogin",
          });
        }, 1000);
      } else {
        uni.navigateTo({
          url: url,
        });
      }
    },
  },
  onLoad() {
    this.UserInfo = uni.getStorageSync("ImsUserInfo");
    this.userRole = uni.getStorageSync("ImsUserRole");
  },
};
</script>

<style></style>
