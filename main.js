import Vue from 'vue'
import App from './App'
import uView from "uview-ui"
import http from './common/http/index.js';
import uvUI from '@/uni_modules/uv-ui-tools';
import common from "./common/common.js";
import imsHttp from './common/http/ims-index.js';
import apis from "./common/apis.js";

//组件
import commonTabbar from "@/components/common-tabbar/common-tabbar.vue";
import commonApprove from "@/components/common-approve/common-approve.vue";
import commonAuditProgressTop from "@/components/common-audit-progress-top/common-audit-progress-top.vue";
import commonAuditProgress from "@/components/common-audit-progress/common-audit-progress.vue";
import commonPicker from "@/components/common-picker/common-picker.vue";
import commonUpload from "@/components/common-upload/common-upload.vue";

// #ifdef H5
import Vconsole from "vconsole";
// 仅在开发或测试环境启用vconsole
let vConsole = null;
if (process.env.NODE_ENV === "development" || process.env.VUE_APP_TITLE === "test") {
  //vConsole = new Vconsole();
}
// #endif

Vue.use(uView);
Vue.use(uvUI);
Vue.prototype.$c = common;
Vue.prototype.$http = http;
Vue.prototype.$imsHttp = imsHttp; // ims 相关 http 实例
Vue.prototype.$apis = apis;       // 挂载接口集合
Vue.config.productionTip = false;

Vue.component("common-tabbar", commonTabbar);
Vue.component("common-approve", commonApprove);
Vue.component("common-audit-progress-tp", commonAuditProgressTop);
Vue.component("common-audit-progress", commonAuditProgress);
Vue.component("common-picker", commonPicker);
Vue.component("common-upload", commonUpload); 



App.mpType = 'app'

const app = new Vue({
    ...App
})
app.$mount()
export default http;