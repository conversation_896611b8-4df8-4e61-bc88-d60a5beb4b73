{
	"easycom": {
		"^u-(.*)": "@/uview-ui/components/u-$1/u-$1.vue"
	},

	"pages": [{
			"path": "pages/imsLogin/imsLogin",
			"style": {
				"navigationStyle": "custom",
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "欢迎登录",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "妙洁物业",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/selectProject/selectProject",
			"style": {
				"navigationBarTitleText": "选择项目",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/user",
			"style": {
				"navigationBarTitleText": "我的",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/auth/auth",
			"style": {
				"navigationBarTitleText": "员工认证",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/add-book/add-book",
			"style": {
				"navigationBarTitleText": "通讯录",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/project-index/project-index",
			"style": {
				"navigationBarTitleText": "项目详情",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/fzr-foodList/fzr-foodList",
			"style": {
				"navigationBarTitleText": "卤菜外卖",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/fzr-foodDetail/fzr-foodDetail",
			"style": {
				"navigationBarTitleText": "卤菜外卖",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/fzr-eatDetail/fzr-eatDetail",
			"style": {
				"navigationBarTitleText": "餐食预留",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/fzr-roomDetail/fzr-roomDetail",
			"style": {
				"navigationBarTitleText": "包厢预定",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/fzr-workList/fzr-workList",
			"style": {
				"navigationBarTitleText": "工单中心",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/fzr-workDetail/fzr-workDetail",
			"style": {
				"navigationBarTitleText": "工单详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user-info/user-info",
			"style": {
				"navigationBarTitleText": "个人信息",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user-inform-detail/user-inform-detail",
			"style": {
				"navigationBarTitleText": "详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user-inform/user-inform",
			"style": {
				"navigationBarTitleText": "消息中心",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user-changetel/user-changetel",
			"style": {
				"navigationBarTitleText": "更换手机",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/selectPeople/selectPeople",
			"style": {
				"navigationBarTitleText": "选择员工"
			}
		},
		{
			"path": "pages/user-card/user-card",
			"style": {
				"navigationBarTitleText": "我的名片",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/fzr-meetingDetail/fzr-meetingDetail",
			"style": {
				"navigationBarTitleText": "预约会议详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/fzr-meetingDetail/fzr-meetingDetailzg",
			"style": {
				"navigationBarTitleText": "预约会议详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/fzr-reportList/fzr-reportList",
			"style": {
				"navigationBarTitleText": "日报列表",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/fzr-reportListMy/fzr-reportListMy",
			"style": {
				"navigationBarTitleText": "日报列表",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/fzr-reportAdd/fzr-reportAdd",
			"style": {
				"navigationBarTitleText": "填写日报"
			}
		},
		{
			"path": "pages/user-wechart/user-wechart",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/fzr-checkList/fzr-checkList",
			"style": {
				"navigationBarTitleText": "巡检管理",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/fzr-checkDetail/fzr-checkDetail",
			"style": {
				"navigationBarTitleText": "巡检管理",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/yg-meetingDetail/yg-meetingDetail",
			"style": {
				"navigationBarTitleText": "预约会议详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/yg-workDetail/yg-workDetail",
			"style": {
				"navigationBarTitleText": "工单详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/yg-checkAdd/yg-checkAdd",
			"style": {
				"navigationBarTitleText": "巡检管理",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/404/404",
			"style": {
				"navigationBarTitleText": "正在建设中",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/yz-user/yz-user",
			"style": {
				"navigationBarTitleText": "我的",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/yz-home/yz-home",
			"style": {
				"navigationBarTitleText": "妙洁物业",
				"enablePullDownRefresh": true
			}
		},
		{
			"path": "pages/user-notice/user-notice",
			"style": {
				"navigationBarTitleText": "信息发布",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/fzr-meetingList/fzr-meetingList",
			"style": {
				"navigationBarTitleText": "会议预约",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/noticeDetail/noticeDetail",
			"style": {
				"navigationBarTitleText": "详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "uview-ui/components/u-avatar-cropper/u-avatar-cropper",
			"style": {
				"navigationBarTitleText": "头像裁剪",
				"navigationBarBackgroundColor": "#000000"
			}
		},
		{
			"path": "pages/nfc/nfc",
			"style": {
				"navigationBarTitleText": "NFC",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/test/test",
			"style": {}
		},
		{
			"path": "pages/fzr-project/fzr-project",
			"style": {
				"navigationBarTitleText": "我的项目",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/singature/singature",
			"style": {
				"navigationBarTitleText": "签名",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/yg-checkDetail/yg-checkDetail",
			"style": {
				"navigationBarTitleText": "巡检详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/equipmentList/equipmentList",
			"style": {
				"navigationBarTitleText": "设备分类",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/safeList/safeList",
			"style": {
				"navigationBarTitleText": "安防巡逻",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/safeCheckList/safeCheckList",
			"style": {
				"navigationBarTitleText": "安防巡逻",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/safeCheckDetail/safeCheckDetail",
			"style": {
				"navigationBarTitleText": "安防巡逻",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/safeCheckHisDetail/safeCheckHisDetail",
			"style": {
				"navigationBarTitleText": "记录详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/cleanList/cleanList",
			"style": {
				"navigationBarTitleText": "保洁监管",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/cleanCheckList/cleanCheckList",
			"style": {
				"navigationBarTitleText": "保洁监管",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/cleanCheckDetail/cleanCheckDetail",
			"style": {
				"navigationBarTitleText": "保洁监管",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/cleanCheckHisDetail/cleanCheckHisDetail",
			"style": {
				"navigationBarTitleText": "记录详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/secureList/secureList",
			"style": {
				"navigationBarTitleText": "安全检查",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/secureCheckList/secureCheckList",
			"style": {
				"navigationBarTitleText": "安全检查",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/secureCheckDetail/secureCheckDetail",
			"style": {
				"navigationBarTitleText": "安全检查",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/secureCheckHisDetail/secureCheckHisDetail",
			"style": {
				"navigationBarTitleText": "记录详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/qualityList/qualityList",
			"style": {
				"navigationBarTitleText": "质量检查",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/qualityCheckList/qualityCheckList",
			"style": {
				"navigationBarTitleText": "质量检查",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/qualityCheckDetail/qualityCheckDetail",
			"style": {
				"navigationBarTitleText": "质量检查",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/qualityCheckHisDetail/qualityCheckHisDetail",
			"style": {
				"navigationBarTitleText": "记录详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/energyList/energyList",
			"style": {
				"navigationBarTitleText": "能耗管理",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/energyCheckList/energyCheckList",
			"style": {
				"navigationBarTitleText": "能耗管理",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/energyCheckDetail/energyCheckDetail",
			"style": {
				"navigationBarTitleText": "能耗管理",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/energyCheckHisDetail/energyCheckHisDetail",
			"style": {
				"navigationBarTitleText": "记录详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/filesList/filesList",
			"style": {
				"navigationBarTitleText": "档案管理",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/filesCheckList/filesCheckList",
			"style": {
				"navigationBarTitleText": "档案管理",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/filesCheckDetail/filesCheckDetail",
			"style": {
				"navigationBarTitleText": "档案管理",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/filesCheckHisDetail/filesCheckHisDetail",
			"style": {
				"navigationBarTitleText": "记录详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/assetList/assetList",
			"style": {
				"navigationBarTitleText": "资产管理",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/assetDetail/assetDetail",
			"style": {
				"navigationBarTitleText": "资产详情",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/assetAdd/assetAdd",
			"style": {
				"navigationBarTitleText": "录入资产",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/qualityIndex/qualityIndex",
			"style": {
				"navigationBarTitleText": "品质巡检",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/safeCheckAdd/safeCheckAdd",
			"style": {
				"navigationBarTitleText": "安防巡逻",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/cleanCheckAdd/cleanCheckAdd",
			"style": {
				"navigationBarTitleText": "保洁监管",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/secureCheckAdd/secureCheckAdd",
			"style": {
				"navigationBarTitleText": "安全检查",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/qualityCheckAdd/qualityCheckAdd",
			"style": {
				"navigationBarTitleText": "质量检查",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/filesCheckAdd/filesCheckAdd",
			"style": {
				"navigationBarTitleText": "档案管理",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/energyCheckAdd/energyCheckAdd",
			"style": {
				"navigationBarTitleText": "能耗管理",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/messageList/messageList",
			"style": {
				"navigationBarTitleText": "在线客服"
			}
		},
		{
			"path": "pages/messageAdd/messageAdd",
			"style": {
				"navigationBarTitleText": "在线客服"
			}
		},
		{
			"path": "pages/gridList/gridList",
			"style": {
				"navigationBarTitleText": "网格巡检"
			}
		},
		{
			"path": "pages/gridCheckList/gridCheckList",
			"style": {
				"navigationBarTitleText": "网格巡检"
			}
		},
		{
			"path": "pages/gridCheckHisDetail/gridCheckHisDetail",
			"style": {
				"navigationBarTitleText": "网格巡检"
			}
		},
		{
			"path": "pages/gridCheckDetail/gridCheckDetail",
			"style": {
				"navigationBarTitleText": "网格巡检"
			}
		},
		{
			"path": "pages/gridCheckAdd/gridCheckAdd",
			"style": {
				"navigationBarTitleText": "网格巡检"
			}
		},
		{
			"path": "pages/docList/docList",
			"style": {
				"navigationBarTitleText": "档案管理"
			}
		}


	],
	"subPackages": [{
		"root": "yezhu",
		"pages": [{
				"path": "yz-repairDetail/yz-repairDetail",
				"style": {
					"navigationBarTitleText": "我的报修",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-orderList/yz-orderList",
				"style": {
					"navigationBarTitleText": "我的预约",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-foodSelect/yz-foodSelect",
				"style": {
					"navigationBarTitleText": "选择菜品",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-comment/yz-comment",
				"style": {
					"navigationBarTitleText": "我要评价",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-repairAdd/yz-repairAdd",
				"style": {
					"navigationBarTitleText": "报修申请",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-meetingAdd/yz-meetingAdd",
				"style": {
					"navigationBarTitleText": "会议预约申请",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-meetingService/yz-meetingService",
				"style": {
					"navigationBarTitleText": "选择服务",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-auth/yz-auth",
				"style": {
					"navigationBarTitleText": "业主认证",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-meetingDetail/yz-meetingDetail",
				"style": {
					"navigationBarTitleText": "我的预约",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-eatAdd/yz-eatAdd",
				"style": {
					"navigationBarTitleText": "餐食预留",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-eatDetail/yz-eatDetail",
				"style": {
					"navigationBarTitleText": "我的预约",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-roomAdd/yz-roomAdd",
				"style": {
					"navigationBarTitleText": "包厢预定",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-roomDetail/yz-roomDetail",
				"style": {
					"navigationBarTitleText": "包厢预定",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-foodAdd/yz-foodAdd",
				"style": {
					"navigationBarTitleText": "卤菜外卖申请",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-project/yz-project",
				"style": {
					"navigationBarTitleText": "我的项目",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-repairList/yz-repairList",
				"style": {
					"navigationBarTitleText": "我的报修",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-foodDetail/yz-foodDetail",
				"style": {
					"navigationBarTitleText": "卤菜外卖",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "yz-repairComment/yz-repairComment",
				"style": {
					"navigationBarTitleText": "我要评价",
					"enablePullDownRefresh": false
				}
			}
		]
	}, {
		"root": "ims",
		"pages": [{
				"path": "index/index",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTitleText": "综合管理平台",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "invoice/invoiceApply",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTitleText": "开票申请",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "invoice/invoiceList",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTitleText": "开票申请记录",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "invoice/invoiceDetail",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTitleText": "开票申请记录",
					"enablePullDownRefresh": false
				}
			},
			{
				"path": "audit/audit",
				"style": {
					"navigationStyle": "custom",
					"navigationBarTitleText": "审批",
					"enablePullDownRefresh": false
				}
			},
			{
				"path" : "user/user",
				"style" : 
				{
					"navigationStyle": "custom"
				}
			},
			{
				"path" : "userInfo/userInfo",
				"style" : 
				{
					"navigationStyle": "custom"
				}
			},
			{
				"path" : "modifyPhone/modifyPhone",
				"style" : 
				{
					"navigationStyle": "custom"
				}
			},
			{
				"path" : "attendance/attendance",
				"style" : 
				{
					"navigationStyle": "custom"
				}
			},
			{
				"path" : "attendance/goOutList",
				"style" : 
				{
					"navigationStyle": "custom"
				}
			},
			{
				"path" : "sign/sign",
				"style" : 
				{
					"navigationStyle": "custom"
				}
			}
		]
	}],

	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#fff",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		"color": "#ffffff",
		"selectedColor": "#ffffff",
		"borderStyle": "white",
		"backgroundColor": "#ffffff",
		"list": [{
				"pagePath": "pages/index/index",
				"text": "首页"
			},
			{
				"pagePath": "pages/yz-home/yz-home",
				"text": "首页"
			},
			{
				"pagePath": "pages/user/user",
				"text": "我的"
			},
			{
				"pagePath": "pages/yz-user/yz-user",
				"text": "我的"
			},
			{
				"pagePath": "pages/add-book/add-book",
				"text": "通讯录"
			}
		]
	},
	"condition": {
		//模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [{
			"name": "", //模式名称
			"path": "", //启动页面，必选
			"query": "" //启动参数，在页面的onLoad函数里面得到
		}]
	}
}