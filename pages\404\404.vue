<template>
	<view>
		<view class="logo">
			<image mode="widthFix" src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/user_logo.png"></image>
		</view>
		<view class="info">
			<image mode="widthFix" src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/404.png"></image>
			<view class="t">敬请期待！</view>
			<view class="desc">功能筹划中，还未开始哦~</view>
		</view>
		<view class="btn" @click="goBack">返回</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			goBack(){
				uni.navigateBack({
					delta:1
				})
			}
		}
	}
</script>

<style>
	page{
		background: #f6f6f6;
	}
	.logo{
		text-align: center;
	}
	.logo image{
		margin-top: 30%;
		width:240rpx ;
	}
	.info{
		position: relative;
		text-align: center;
		margin: 50rpx 0;
	}
	.info image{
		
	}
	.info .t{
		position: absolute;
		color: #fd3325;
		top: 100rpx;
		width: 100%;
		font-size: 36rpx;
	}
	.info .desc{
		position: absolute;
		color: #fd3325;
		top: 150rpx;
		text-align: center;
		width: 100%;
	}
	.btn{
		margin: 0 auto;
		background: #ff464c;
		color: #fff;
		width: 200rpx;
		height: 70rpx;
		line-height: 70rpx;
		text-align: center;
		border-radius: 100px;
	}
</style>
