<template>
	<view class="content">
		<view class="u-page bg-w">
			<view class="fixed-box-100">
				<view class="bg-w u-p-l-30 u-p-r-30 u-p-t-20 u-p-b-20 search-fixed">
					<u-search placeholder="请输入姓名" v-model="keyword" :show-action="false" @blur="search" @clear="reset"></u-search>
				</view>
			</view>
			<u-index-list :show="listshow"  :scrollTop="scrollTop" :margin-top="150" :sticky="false" :index-list="indexList" active-color="#ff3300">
				<view v-for="(item, index) in list" :key="index" class="u-m-30 ">
					<u-index-anchor :index="item.letter" />
					<view class="list-cell u-p-30" v-for="(item1, index1) in item.data" :key="index1">
						<view class="u-flex">
							<view class="list-name u-flex-4">
								{{ item1.UserName }}
								<text class="u-font-12">工号:{{ item1.JobNumber }}</text>
							</view>
							<view class="list-mobile u-flex-5">{{ item1.CellPhone }}</view>
							<view class="list-right  u-flex-3">
								<u-icon class="u-m-l-20" name="lujingbeifen3" custom-prefix="custom-icon" size="80" color="#666" @click="PhoneCall({ mobile: item1.CellPhone })" />
							</view>
						</view>
					</view>
				</view>
			</u-index-list>
			
		</view>
		<u-tabbar v-model="current" :list="tabbar" :inactive-color="inactiveColor" :activeColor="ThemeColor" @change="change"></u-tabbar>
	</view>
</template>
<script scoped>

export default {
	data() {
		return {
			ThemeColor: '',
			inactiveColor: '#909399',
			scrollTop: -65,
			indexList: '',
			list: '',
			listData:'',
			tabbar: [
				{
					iconPath: 'shouye',
					selectedIconPath: 'tabbar-icon01_h',
					text: '首页',
					customIcon: true,
					pagePath: '/pages/index/index'
				},
				{
					iconPath: 'nfc',
					selectedIconPath: 'nfc1',
					text: 'NFC',
					customIcon: true,
					pagePath: '/pages/nfc/nfc'
				},
				{
					iconPath: 'tongxunlu',
					selectedIconPath: 'renyuantongxunlu',
					text: '通讯录',
					customIcon: true,
					pagePath: '/pages/add-book/add-book'
				},
				{
					iconPath: 'yonghu',
					selectedIconPath: 'wode',
					text: '我的',
					customIcon: true,
					pagePath: '/pages/user/user'
				}
			]
		};
	},
	onPageScroll(e) {
		this.scrollTop = e.scrollTop - 65;
		//console.log(this.scrollTop)
	},
	onLoad() {
		//主题色
		this.ThemeColor = getApp().globalData.ThemeColor;
		this.$http
			.post('/LoginMng/GetMailList', {
				searchtext: '',
				usercode:uni.getStorageSync('UserCode')
			})
			.then(res => {
				var booklist = [],
				 letterArr = [];
				res.Data.sort((a, b) => a.UserRemark.charCodeAt(0) - b.UserRemark.charCodeAt(0));
				for (var i = 0; i < 26; i++) {
					var letter = String.fromCharCode(65 + i);
					if (letter) {
						var newlist = res.Data.filter(function(x) {
							if (x.UserRemark == letter) {
								return x;
							}
						});						
					}			
					letterArr.push(letter);
					booklist.push({letter: letter, data: newlist });
				};				
				var booklist2=[];
				booklist.forEach((item,index) => {
					if(item.data.length>0){						
						booklist2.push(item);
					}
				});
				this.list = booklist2;
				this.listData=booklist2;
				this.indexList = letterArr;
			})
			.catch(err => {});
	},
	methods: {
		change(e){
			console.log(e)
			if(e==1){
				uni.navigateTo({
					url:"../nfc/nfc"
				})
			}
		},
		PhoneCall: function(mobile) {
			uni.makePhoneCall({
				phoneNumber: mobile.mobile,

				// 成功回调
				success: res => {
					console.log('调用成功!');
				},

				// 失败回调
				fail: res => {
					console.log('调用失败!');
				}
			});
		},
		search(keyword) {
			this.$http
				.post('/LoginMng/GetMailList', {
					searchtext: keyword
				})
				.then(res => {
					var booklist = [],
					 letterArr = [];
					res.Data.sort((a, b) => a.UserRemark.charCodeAt(0) - b.UserRemark.charCodeAt(0));
					for (var i = 0; i < 26; i++) {
						var letter = String.fromCharCode(65 + i);
						if (letter) {
							var newlist = res.Data.filter(function(x) {
								if (x.UserRemark == letter) {
									return x;
								}
							});						
						}			
						
						booklist.push({letter: letter, data: newlist });
					}
					var booklist2=[];
					booklist.forEach((item,index) => {
						if(item.data.length>0){							
							booklist2.push(item);
						}
					});					
					this.list = booklist2;
				})
				.catch(err => {});
		},
		reset(){
			this.list=this.listData
		}
	}
};
</script>
<style>
.u-index-anchor {
	background-color: #ffffff !important;
	color: #93a6cc !important;
	font-size: 34rpx;
	font-weight: bold;
	position: relative;
}
.u-index-anchor::after {
	content: ' ';
	position: absolute;
	left: 80rpx;
	top: 28rpx;
	pointer-events: none;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-transform-origin: 0 0;
	transform-origin: 0 0;
	width: 80%;
	-webkit-transform: scale(1, 0.5);
	transform: scale(1, 0.5);
	border: 1px solid #eee;
	z-index: 2;
}
.list-name {
	line-height: 40rpx;
}
.list-name text {
	display: block;
	color: #999;
	font-size: 24rpx;
}
.list-mobile {
	font-size: 30rpx;
}
.u-index-bar__sidebar {
	z-index: 1000 !important;
	background-color: #f6f6f6 !important;
	width: 60rpx;
	right: 20rpx !important;
}
.list-right image {
	width: 10vw !important;
	height: 10vw !important;
}
</style>
