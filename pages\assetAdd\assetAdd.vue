<template>
	<view class="border-top">
		<view class="pd-info" style="background: #fff;">
			<view class="pannel">
				<view class="pd030">
					<u-form :model="form" ref="uForm">
						<u-form-item label="资产名称" label-width="150rpx" :border-bottom="false">
							<u-input :border="true" v-model="form.AssetName" />
						</u-form-item>	
						<u-form-item label="资产类型" label-width="150rpx" :border-bottom="false">
							<u-input :border="true" v-model="form.AssetCate" />
						</u-form-item>	
						<u-form-item label="品牌" label-width="150rpx" :border-bottom="false">
							<u-input :border="true" v-model="form.Brand" />
						</u-form-item>
						<u-form-item label="规格" label-width="150rpx" :border-bottom="false">
							<u-input  :border="true" v-model="form.Specification" />
						</u-form-item>
						<u-form-item label="购买日期" label-width="150rpx" :border-bottom="false">
							<u-input :border="true" @click="startShow = true" type="select"  v-model="form.BuyDate" />
						</u-form-item>
						<u-form-item label="状态" label-width="150rpx" :border-bottom="false">
							<u-radio-group v-model="form.IsUseText">
								<u-radio
									v-for="(i, idx) in radioList" :key="idx" 
									:name="i.name"
									:disabled="i.disabled"
								>
									{{i.name}}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="备注" label-width="150rpx" :border-bottom="false">
							<u-input type="textarea" v-model="form.Remark" :border="true" />
						</u-form-item>
						<u-form-item label="上传图片" label-width="150rpx" :border-bottom="false">
							<u-upload max-count="1" @on-remove="remove" ref="uUpload1" :action="action" :header="uploadHeader" @on-success="uploadOk" :file-list="fileList" ></u-upload>
						</u-form-item>
						<!-- <u-form-item label="签名" label-width="150rpx" :border-bottom="false">
							<view class="singature" v-if="SignImageUrl">
								<image class="singature-images" :src="SignImageUrl"></image>
							</view>
							<navigator class="sign" url="../singature/singature">请点击进行签名</navigator>
						</u-form-item> -->
					</u-form>
					
				</view>
				
			</view>
		</view>
		<u-picker mode="time" v-model="startShow" :params="params" @confirm="startDate"></u-picker>
		<view class="mr30">
			<u-button type="primary" @click="submit">确认提交</u-button>
		</view>
	</view>
</template>

<script>
	import utils from '../../components/utils.js'
	const app = getApp();
	export default {
		data() {
			return {
				uploadHeader:{
					Authorization: 'Bearer '+ uni.getStorageSync('access_token')
				},
				action:app.globalData.uploadUrl,
				form:{
					"ProjectCode":"",//项目编码
					"AssetName":"",//资产名称
					"AssetCate":"",//资产类型
					"Brand":"",//品牌
					"Specification":"",//规格型号
					"BuyDate":"",//购置日期
					"IsUse":1,//使用状态：1在用，-1不在用
					"IsUseText":"",
					"AssetPic":"",//图片
					"Remark":"",//备注
					"UserCode":uni.getStorageSync('UserCode')//当前登录人
				},
				startShow:false,
				params: {
					year: true,
					month: true,
					day: true,
					hour: false,
					minute: false,
					second: false
				},
				radioList:[{
					name: '在用'
				},
				{
					name: '不在用'
				}],
				photoList:[],
				fileList:[],
				sfrom:''
			}
		},
		onLoad(e) {
			this.form.ProjectCode =e.ProjectCode
			this.sfrom = e.sfrom
			if(e.sfrom=='modify'){
				this.$http.get('/AssetApi/GetAssetByCode', {
					params:{
						code:e.code
					}
				}).then(res => {
					if(res.Data.AssetPic){
						var AssetPic = res.Data.AssetPic
						this.fileList = [{
							url:this.$http.config.staticURL +AssetPic
						}]
						this.photoList = [AssetPic]
						res.Data.AssetPic = this.$http.config.staticURL + AssetPic
					}
					res.Data.BuyDate = res.Data.BuyDateText
					this.form = res.Data
					
				})
			}
		},
		methods: {
			remove(index){
				console.log(index)
				this.photoList.splice(index,1)
			},
			startDate(e){
				this.form.BuyDate = e.year+'-'+e.month+'-'+e.day
			},
			uploadOk(data,index,lists){
				console.log(data)
				this.photoList.push(data.RetValue)
			},
			submit(){
				if(this.form.IsUseText=='在用'){
					this.form.IsUse = 1
				}else{
					this.form.IsUse = -1
				}
				if(this.sfrom=='modify'){
					var photoList1=[]
					for(let index in this.photoList){
						photoList1.push(this.photoList[index])
					}	
					this.form.AssetPic=photoList1.toString()
					this.$http.post('/AssetApi/Execute?docmd=modify', this.form).then(res => {
						this.$u.toast(res.ErrMsg);
						if(res.ErrCode==100){
							setTimeout(function(){
								uni.navigateBack({
									delta:1
								})
								uni.$emit('update',{update:true})
							},1000)
						}
					})
				}
				else{
					var photoList1=[]
					var files1 = this.$refs.uUpload1.lists;
					for(let index in files1){
						photoList1.push(files1[index].response.RetValue)
					}	
					this.form.AssetPic=photoList1.toString()
					this.$http.post('/AssetApi/Execute?docmd=add', this.form).then(res => {
						this.$u.toast(res.ErrMsg);
						if(res.ErrCode==100){
							setTimeout(function(){
								uni.navigateBack({
									delta:1
								})
								uni.$emit('update',{update:true})
							},1000)
						}
					})
				}
			},
		}
	}
</script>

<style>

</style>
