<template>
	<view class="border-top">
		<view class="pd-info">
			<view class="top flext" style="background: #fff;">
				<view class="flex-bd">
					<view class="name">{{info.AssetName}}</view>
					<view class="no">编号:<view class="inline">{{info.AssetNumber}}</view></view>
				</view>
				<image class="cover" v-if="info.AssetPic" :src="info.AssetPic"></image>
				<image class="cover" v-else src="../../static/sbnopic.png"></image>
			</view>
			<view class="hr"></view>
			
			<!-- <view class="swiper-box">
				<u-card padding="20" class="card-readyonly">
					<view slot="head">
						<view class="u-flex u-col-top u-row-between">
							<view class="u-flex-nowrap u-item-title u-font-28">资产信息</view>
						</view>
					</view>
					<view slot="body">
						<view class="u-text">
							资产类型：电子产品
						</view>
						<view class="u-text">
							品牌：联想
						</view>
						<view class="u-text">
							规格：台
						</view>
						<view class="u-text">
							购买日期：2023-10-01
						</view>
						<view class="u-text">
							状态：在用
						</view>
						<view class="u-text">
							备注：无备注
						</view>
					</view>
				</u-card>
			</view> -->
			<view class="pannel">
				<view class="pd" style="padding: 20rpx 0;background: #fff;margin-top: 20rpx;"><view class="tit">资产信息</view></view>
				<view class="">
					<u-cell-group>
						<u-cell-item title="资产类型" :value="info.AssetCate" :arrow="false"></u-cell-item>
						<u-cell-item title="品牌" :value="info.Brand" :arrow="false"></u-cell-item>
						<u-cell-item title="规格" :value="info.Specification" :arrow="false"></u-cell-item>
						<u-cell-item title="购买日期" :value="info.BuyDateText" :arrow="false"></u-cell-item>
						<u-cell-item title="状态" :value="info.IsUseText" :arrow="false"></u-cell-item>
						<u-cell-item title="备注" :value="info.Remark" :arrow="false" :border="false"></u-cell-item>
					</u-cell-group>
				</view>
			</view>
		</view>
		<view class="mr30">
			<u-button type="primary" @click="edit">修改</u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				info:{},
				code:''
			}
		},
		onShow() {
			var that=this
			uni.$on('update',function(data){
				if(data.update){
					that.load()
				}
			})
		},
		onLoad(e) {
			this.code = e.code
			this.load()
		},
		methods: {
			load(){
				this.$http.get('/AssetApi/GetAssetByCode', {
					params:{
						code:this.code
					}
				}).then(res => {
					console.log(res)
					if(res.Data.AssetPic){
						res.Data.AssetPic = this.$http.config.staticURL + res.Data.AssetPic
					}
					this.info = res.Data
				})
			},
			edit(){
				uni.navigateTo({
					url:'../assetAdd/assetAdd?sfrom=modify&code='+this.info.Code+'&ProjectCode='+this.info.ProjectCode
				})
			}
		}
	}
</script>

<style>

</style>
