<template>
	<view class="border-top">
		<view style="background: #fff;padding: 10rpx 20rpx;">
			<u-search v-model="searchText" @custom="search" @search="search"></u-search>
		</view>
		<view class="panD-list">
			<view class="li flex" @click="goDetail(item)" v-for="(item,index) in list">
				<view class="img">
					<image v-if="item.AssetPic" mode="aspectFill" class="cover" :src="item.AssetPic"></image>
					<image v-else class="cover" src="../../static/sbnopic.png"></image>
				</view>
				<view class="flex-bd">
					<view class="name">{{item.AssetName}}</view>
					<view class="no">编号:{{item.AssetNumber}}</view>
				</view>
				<view class="cell-ft">
					<u-icon name="arrow-right" color="#999" :size="28"></u-icon>
				</view>
			</view>
		</view>
		<view style="padding: 30rpx;">
			<u-loadmore :status="loadmore.status" :icon-type="loadmore.iconType" :load-text="loadmore.loadText" />
		</view>
		<view class="fix-add"><i class="custom-icon custom-icon-jia" @click="gotoUrl('/pages/assetAdd/assetAdd?ProjectCode='+ProjectCode)"></i></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				page:1,
				loadmore:{
					status: 'loading',
					iconType: 'flower',
					loadText: {
						loading: '努力加载中',
						nomore: '已无更多'
					}
				},
				list:[],
				searchText:'',
				ProjectCode:''
			}
		},
		onLoad(e) {
			this.ProjectCode = e.projectCode
			this.renderList(1).then(res=>{
				this.list=res
			})
		},
		onShow() {
			var that=this
			uni.$on('update',function(data){
				if(data.update){
					that.loadmore.status='loading'
					that.page = 1
					that.renderList(1).then(res=>{
						that.list=res
					})
				}
			})
		},
		methods: {
			search(){
				this.loadmore.status='loading'
				this.page = 1
				this.renderList(1).then(res=>{
					this.list=res
				})
			},
			renderList(page){
				return new Promise((resolve)=>{
					this.$http.get('/AssetApi/GetAssetList?', {
						params:{
							p:page,
							ps:10,
							projectCode:this.ProjectCode,
							searchText:this.searchText?this.searchText:''
						}
					}).then(res => {
						if(res.Data.length<10){
							this.loadmore.status='nomore'
						}
						if(res.Data.length>0){
							for(let i in res.Data){
								if(res.Data[i].AssetPic){
									res.Data[i].AssetPic = this.$http.config.staticURL + res.Data[i].AssetPic
								}
							}
						}
						resolve(res.Data)
					})
				})
			},
			goDetail(item){
				uni.navigateTo({
					url:'../assetDetail/assetDetail?code='+item.Code
				})
			},
			gotoUrl(e) {
				uni.navigateTo({
					url: e
				});
			},
		},
		onreachBottom() {
			if(this.loadmore.status=='nomore'){
				return
			}
			var orderList=this.orderList
			this.renderList(this.page+1).then(res=>{
				for(let index in res){
					orderList.push(res[index])
				}
				if(res.length>0){
					this.orderList=orderList
					this.page++
				}
				else{
					this.loadmore.status='nomore'
				}
			})
		},
	}
</script>

<style>
	.fix-add{
		position: fixed;
		right: 30rpx;
		bottom: 50rpx;
		width: 100rpx;
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
		border-radius: 50%;
		box-shadow: 1px 1px 10px rgba(0,0,0,0.1);
		background: #35318f;
	}
	.fix-add i{
		color: #fff;
		font-size: 48rpx;
	}
</style>
