<template>
	<view class="border-top">
		<view class="report-top">
			<view class="tab flex">
				<view class="i flex-bd active">保洁监管</view>
				<view class="flex-hd">
					<u-button type="error" plain size="mini" v-if="info.IsOverdue==1">超期</u-button>
				</view>
			</view>
		</view>
		<u-card :foot-border-top="false" sub-title="2020-10-20" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">编号：{{info.PlaceNumber}}</view>
					<view><u-button type="error" size="mini" pain v-if="info.IsOverdue==1">超期</u-button></view>
				</view>
			</view>
			<view slot="body">
				<view class="u-text">
					检查周期：{{info.SuperviseTimeUnit}}
				</view>
				<view class="u-text">
					检查位置：{{info.CateText==null?'':info.CateText}}
				</view>
				<view class="u-text">
					最近一次检查：{{info.LastTimeText}}
				</view>
				<view class="detail-phone" v-if="info.InspectorCellphone" @click="callPhone(info.InspectorCellphone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		<view class="mr30">
			<u-row gutter="16">
				<u-button type="primary" @click="gotoUrl">增加记录</u-button>
			</u-row>
		</view>
		<view class="report-top">
			<view class="tab">
				<view class="i active">检查记录</view>
			</view>
			<view class="select flex">
				<view class="flex-bd">
					<u-form-item label="起始日期" label-width="150rpx" :border-bottom="false">
						<u-input height="30" @click="startShow = true" type="select" v-model="StartTime" :border="true" />
					</u-form-item>
					<u-form-item label="截止日期" label-width="150rpx" :border-bottom="false">
						<u-input height="30" @click="endShow = true" type="select" v-model="EndTime" :border="true" />
					</u-form-item>
				</view>
				<view class="flex-hd">
					<view class="btn" @click="search">确认</view>
					<view class="btn btn2" @click="searchEmpty">重置</view>
				</view>
			</view>
		</view>
		<u-picker mode="time" v-model="startShow" :params="params" @confirm="startDate"></u-picker>
		<u-picker mode="time" v-model="endShow" :params="params" @confirm="endDate"></u-picker>
		<u-card padding="20" class="card-readyonly" v-for="(item,index) in list" :key="index">
			<!-- <view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">{{item.InspectionTime.replace('T',' ')}}</view>
					<view class="time">巡检人：{{item.inspectorusername}}</view>
				</view>
			</view>
			<view slot="body" @click="">
				<view class="u-text" v-if="item.InspectionContent">
					{{item.InspectionContent}}
				</view>
				<view class="detail-img-list" v-if="item.Images">
					<image v-for="(img,index) in item.Images" :key="index" :src="img" mode="aspectFill" @click="lookImg(index,item.Images)"></image>
				</view>
				<view class="" style="margin: 30rpx 0;">查看详情</view>
			</view> -->
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">检查：{{item.SuperviseUserName}}</view>
				</view>
			</view>
			<view slot="body">
				<view class="u-text">
					检查时间：{{item.ThisTimeText}}
				</view>
				<view class="u-text">
					备注内容：{{item.BeizhuText}}
				</view>
				<!-- <view class="detail-img-list" v-if="item.Images">
					<image v-for="(img,index) in item.Images" :key="index" :src="img" mode="aspectFill" @click="lookImg(index,item.Images)"></image>
				</view> -->
			</view>
			<view slot="foot">
				<navigator class="u-text-right" :url="'../cleanCheckHisDetail/cleanCheckHisDetail?type=detail&info='+JSON.stringify(item)">
					<view class="u-padding-right-10">查看详情 <i class="inline custom-icon custom-icon-right"></i></view>
				</navigator>
			</view>
		</u-card>
		<!-- <view class="mr30" v-if="!empty">
			<u-loadmore :status="loadmore.status" :icon-type="loadmore.iconType" :load-text="loadmore.loadText" />
		</view> -->
		<u-empty class="u-text-center" v-if="empty" text="暂无内容" margin-top="100"></u-empty>
	</view>
</template>

<script>
	const app = getApp();
	import utils from '../../components/utils.js'
	export default {
		data() {
			return {
				list:'',
				empty:false,
				page:1,
				loadmore:{
					status: 'loading',
					iconType: 'flower',
					loadText: {
						loading: '努力加载中',
						nomore: '已无更多'
					}
				},
				EquipmentCode:'',
				info:{					
				},
				params: {
					year: true,
					month: true,
					day: true,
					hour: false,
					minute: false,
					second: false
				},
				startShow:false,
				endShow:false,
				StartTime:'',
				EndTime:'',
				hostUrl:app.globalData.hostUrl,
				roleGroup:'',
				UserRemark:'',
				placeNumber:'',
				code:''
			}
		},
		onShow() {
			var that=this
			uni.$on('update',function(data){
				if(data.update){
					that.renderList(1).then(res=>{
						that.list=res
						if(res.length<10){
							that.loadmore.status='nomore'
						}
					})
				}
			})
		},
		onUnload(){
		    uni.$off('update')
		},
		onLoad(e) {
			var roleGroup=utils.getRoleGroup(uni.getStorageSync('UserRemark'))
			var UserRemark = uni.getStorageSync('UserRemark')
			this.roleGroup=roleGroup
			this.UserRemark = UserRemark
			this.placeNumber=e.placeNumber?e.placeNumber:''
			this.code=e.code?e.code:''
			this.$http.get('/SuperviseApi/GetSuperviseAreaByCodeOrNumber', {
				params:{
					code:e.code?e.code:'',
					placeNumber:e.placeNumber?e.placeNumber:''
				}
			}).then(res => {
				this.info=res.Data.SuperviseArea
			})
			
			this.renderList(1).then(res=>{
				this.list=res
				if(res.length<10){
					this.loadmore.status='nomore'
				}
			})
			
			
			
		},
		methods: {
			lookImg(index,imgList){
				uni.previewImage({
					current:index,
					urls: imgList
				})
			},
			gotoUrl(){
				uni.navigateTo({
					url:'../cleanCheckAdd/cleanCheckAdd?sfrom=add&code='+this.code+'&placeNumber='+this.placeNumber
				})
			},
			searchEmpty(){
				this.StartTime=''
				this.EndTime=''
				this.renderList(1).then(res=>{
					this.list=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.page=1
			},
			search(){
				this.renderList(1).then(res=>{
					this.list=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
			},
			startDate(e){
				this.StartTime=e.year+'-'+e.month+'-'+e.day
			},
			endDate(e){
				this.EndTime=e.year+'-'+e.month+'-'+e.day
			},
			//获取列表
			renderList(page){
				return new Promise((resolve)=>{
					this.$http.get('/SuperviseApi/GetSuperviseList', {
						params:{
							p:page,
							ps:10,
							superviseAreaCode:this.code,
							startTime:this.StartTime?this.StartTime+' 00:00:00':'',
							endTime:this.EndTime?this.EndTime+' 23:59:59':''
						}
					}).then(res => {
						if(page==1&res.Data.length==0){
							this.empty=true
						}
						else{
							this.empty=false
							
						}
						for(let index in res.Data){
							if(res.Data[index].Images){
								var images=res.Data[index].Images.split(',')
								for(let j in images){
									images[j]=this.hostUrl+images[j]
								}
								res.Data[index].Images=images
							}
							
						}
						resolve(res.Data)
					})
				})		
			}
		},
		onReachBottom() {
			if(this.loadmore.status=='nomore'){
				return
			}
			var list=this.list
			this.renderList(this.page+1,this.type).then(res=>{
				for(let index in res){
					list.push(res[index])
				}
				if(res.length>0){
					this.list=list
					this.page++
				}
				else{
					this.loadmore.status='nomore'
				}
			})
		}
	}
</script>

<style>
	@import url("../fzr-reportList/style.css");
	.report-top .u-btn{
		margin-right: 30rpx;
	}
	.report-top .btn{
		font-size: 24rpx;
		padding: 8rpx 20rpx;
		text-align: center;
		margin-top: 15rpx;
		margin-bottom: 15rpx;
	}
	.btn2{
		background: none !important;
		border: 1px solid #ddd !important;
		color: #666 !important;
	}
</style>
