<template>
	<view class="content border-top">
		<view class="path">
			<view class="i active" @click="get1">
				<i class="custom-icon custom-icon-xuanzhongshangcheng" style="color: #27246C;"></i>{{projectname}}
			</view>
			<view class="i active" v-for="(item,index) in cateList" @click="get2(item,index)"><i class="custom-icon custom-icon-right"></i>{{item.name}}</view>
		</view>
		<view class="path_blank"></view>
		<view class="action-btn" v-if="nowLevel==0">
			<navigator :url="'../cleanCheckList/cleanCheckList?isWarn=1&projectcode='+projectcode" class="i">隐患数量：{{WarnSbnum}}</navigator>
			<navigator :url="'../cleanCheckList/cleanCheckList?isoverdue=1&projectcode='+projectcode" class="i">待处理数量：{{Xjnum}}</navigator>
		</view>
		<view class="list">
			<view class="flex i" v-for="(item ,index) in list" @click="getList(item)">
				<view class="flex-bd">
					{{item.CateName}}
				</view>
				<view class="flex-hd">
					<i class="custom-icon custom-icon-right"></i>
				</view>
			</view>
		</view>
		<view class="qrcode" @click="qrcode">
			<i class="custom-icon custom-icon-weibiaoti--"></i>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list:'',
				nowLevel:0,
				projectcode:'',
				projectname:'',
				UserRemark:'',
				Xjnum:0,
				WarnSbnum:0,
				cateList:[]
			}
		},
		onLoad(e) {
			console.log(e)
			this.projectcode = e.projectCode
			this.projectname = e.projectname
			
			var UserRemark=uni.getStorageSync('UserRemark')
			this.UserRemark=UserRemark
			this.$http.get('/PatrolApi/GetSystemCateList', {
				params:{
					parentCode:e.projectCode,
					cateType:'保洁监管'
				}
			}).then(res => {
				console.log(res)
				this.list = res.Data
			})
			
			this.$http.get('/SuperviseApi/GetCountForSupervise', {
				params:{
					projectCode:e.projectCode
				}
			}).then(res => {
				this.Xjnum = res.Data.OverdueCount
				this.WarnSbnum = res.Data.DangerCount
			})
			
		},
		methods: {
			qrcode(){
				var that=this
				uni.scanCode({
					onlyFromCamera: false,
				    scanType: ['barCode', 'qrCode', 'datamatrix','pdf417'],
				    success: res => {
				        console.log(res);
						var code=res.result
						that.checkCode(code)
				    },
				    fail: res => {
						return this.$u.toast('扫码失败');
				    }
				})
			},
			checkCode(placeNumber){
				this.$http.get('/PatrolApi/GetPatrolPointByCodeOrNumber', {
					params:{
						placeNumber:placeNumber,
						code:""
					}
				}).then(res => {
					console.log(res)
					if(res.Data.PatrolPoint){
						uni.navigateTo({
							url:'../cleanCheckDetail/cleanCheckDetail?placeNumber='+placeNumber
						})
					}else{
						return this.$u.toast(res.ErrMsg);
					}
				})
			},
			get1(){
				this.nowLevel = 0
				this.$http.get('/PatrolApi/GetSystemCateList', {
					params:{
						parentCode:this.projectcode,
						cateType:'保洁监管'
					}
				}).then(res => {
					this.list = res.Data
				})
				this.cateList = []
			},
			get2(item,index){
				this.nowLevel = index+1
				this.$http.get('/PatrolApi/GetSystemCateList', {
					params:{
						parentCode:item.code,
						cateType:'保洁监管'
					}
				}).then(res => {
					this.list = res.Data
				})
				this.cateList = this.cateList.slice(0, (index+1));
			},
			getList(item){
				
				
				if(item.IsParent){
					this.nowLevel++
					this.cateList.push({
						name:item.CateName,
						code:item.Code
					})
					this.$http.get('/PatrolApi/GetSystemCateList', {
						params:{
							parentCode:item.Code,
							cateType:'保洁监管'
						}
					}).then(res => {
						console.log(res)
						this.list = res.Data
					})
				}else{
					uni.navigateTo({
						url:'../cleanCheckList/cleanCheckList?code='+item.Code+'&projectcode='+this.projectcode
					})
				}
				
			}
		}
	}
</script>

<style>
	.path{
		height: 100rpx;
		line-height: 60rpx;
		background: #fff;
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		padding: 20rpx;
		z-index: 88;
	}
	/* #ifdef H5 */
	.path{
		top: 44px;
	}
	/* #endif */
	.path::after{
		content: " ";
		position: absolute;
		left: 0;
		bottom: 0;
		right: 0;
		height: 1px;
		border-bottom: 1px solid rgba(0,0,0,0.1);
		color: rgba(0,0,0,0.1);
		-webkit-transform-origin: 0 100%;
		transform-origin: 0 100%;
		-webkit-transform: scaleY(0.5);
		transform: scaleY(0.5);
	}
	.path_blank{
		height: 100rpx;
		line-height: 100rpx;
	}
	.path .i{
		display: inline-block;
		font-size: 28rpx;
	}
	.path .i i{
		display: inline-block;
		color: #999;
		font-size: 24rpx;
		margin: 0 10rpx;
	}
	.path .i.active{
		color: #35318f;
		font-weight: bold;
	}
	.list{
		background: #fff;
		margin-top: 20rpx;
	}
	
	.list .i{
		padding: 30rpx;
		position: relative;
	}
	.list .i::after{
		content: " ";
		position: absolute;
		left: 0;
		bottom: 0;
		right: 0;
		height: 1px;
		border-bottom: 1px solid rgba(0,0,0,0.1);
		color: rgba(0,0,0,0.1);
		-webkit-transform-origin: 0 100%;
		transform-origin: 0 100%;
		-webkit-transform: scaleY(0.5);
		transform: scaleY(0.5);
	}
	.list .i:last-child:after{
		height: 0;
		border: 0;
	}
	.qrcode{
		position: fixed;
		width: 100rpx;
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
		background: #27246C;
		color: #fff;
		right: 30rpx;
		bottom: 30rpx;
		border-radius: 50%;
		z-index: 999;
	}
	.qrcode i{
		font-size: 48rpx;
	}
	
	.action-btn{
		margin-left: 30rpx;
		margin-top: 20rpx;
	}
	.action-btn .i{
		display: inline-block;
		background: #35318f;
		color: #fff;
		padding: 10rpx 30rpx;
		margin-right: 30rpx;
		border-radius: 100px;
		font-size: 24rpx;
	}
</style>
