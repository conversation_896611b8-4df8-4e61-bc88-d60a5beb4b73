::-webkit-scrollbar {
  width: 0;
  height: 0;
  background-color: transparent;
}
.navigator-hover {
  background: none;
}
.flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}
.flex-hd {
}
.flex-bd {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  min-width: 0;
}
.flex__ft {
  text-align: right;
  color: rgba(0, 0, 0, 0.5);
  padding-right: 22px;
  position: relative;
}

.flex__ft:after {
  content: " ";
  width: 12px;
  height: 24px;
  -webkit-mask-position: 0 0;
  mask-position: 0 0;
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100%;
  mask-size: 100%;
  background-color: currentColor;
  color: rgba(0, 0, 0, 0.3);
  -webkit-mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  mask-image: url(data:image/svg+xml,%3Csvg%20width%3D%2212%22%20height%3D%2224%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M2.454%206.58l1.06-1.06%205.78%205.779a.996.996%200%20010%201.413l-5.78%205.779-1.06-1.061%205.425-5.425-5.425-5.424z%22%20fill%3D%22%23B2B2B2%22%20fill-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E);
  position: absolute;
  top: 50%;
  right: 0;
  margin-top: -12px;
}

.inline {
  display: inline-block;
}

.border-top {
  position: relative;
}
.border-top:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.1);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  z-index: 100;
}
.border-all:after {
  content: "  ";
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  width: 200%;
  height: 200%;
  border: 1px solid #ddd;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scale(0.5, 0.5);
  transform: scale(0.5, 0.5);
  border-radius: 10px;
}

.mr30 {
  margin: 30rpx;
}

.work {
  text-align: center;
  margin-bottom: 30rpx;
}
.work .w-no {
  font-size: 24rpx;
  color: #666;
}
.work .w-name {
  color: #000;
  font-size: 24rpx;
}
.work scroll-view {
  white-space: nowrap;
}
.work scroll-view .i {
  display: inline-block;
  margin: 0 20rpx;
}
.detail-top {
  padding: 20rpx 30rpx;
  background: #fff;
}
.detail-top .flex-bd {
  font-size: 28rpx;
}
.detail-top .flex-bd .desc {
  font-size: 32rpx;
  margin-top: 10rpx;
}
.detail-top .flex-hd image {
  width: 90rpx;
  height: 90rpx;
  margin-right: 20rpx;
}

.detail-top .flex-hd .btn {
  display: inline-block;
  border: 1px solid #00a2ff;
  color: #00a2ff;
  font-size: 12px;
  padding: 8rpx 20rpx;
  border-radius: 4px;
}
.detail-img-list image {
  width: 140rpx;
  height: 140rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}
.detail-finish {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  z-index: 9;
  border: 2px solid #f45b50;
  border-radius: 50%;
  width: 150rpx;
  height: 150rpx;
  box-sizing: content-box;
}
.detail-finish .q {
  margin: 10rpx;
  z-index: 9;
  border: 2px solid #f45b50;
  border-radius: 50%;
  width: 130rpx;
  height: 130rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
.detail-finish .q-t {
  font-size: 28rpx;
  color: #f45b50;
  font-weight: bold;
  transform: rotate(-15deg);
  width: 130rpx;
}
.detail-finish image {
  width: 160rpx;
  height: 160rpx;
}
.detail-phone {
  width: 80rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  position: absolute;
  top: 100rpx;
  right: 30rpx;
}
.detail-phone .custom-icon {
  font-size: 70rpx;
}
.detail-his-list {
  margin-top: 20rpx;
}
.detail-his-list .t {
  position: relative;
  height: 32rpx;
  line-height: 32rpx;
  margin: 20rpx 0;
}
.detail-his-list .t:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 16rpx;
  right: 0;
  height: 1px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.1);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  z-index: 2;
}
.detail-his-list .span {
  font-size: 28rpx;
  display: inline-block;
  background: #fff;
  color: #305282;
  position: relative;
  z-index: 2;
  padding-right: 30rpx;
}

.detail-more-btn {
  color: #3d72b2;
  border: 1px solid #3d72b2;
  width: 240rpx;
  height: 60rpx;
  line-height: 60rpx;
  margin: 30rpx auto;
  text-align: center;
  background: #dfedff;
  font-size: 24rpx;
  border-radius: 4px;
}

.detail-more-btn i {
  margin-left: 10rpx;
  display: inline-block;
  font-size: 30rpx;
}
.swiper-wrap {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  flex-direction: column;
  height: calc(100vh - var(--window-top));
  width: 100%;
}
.swiper-box {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  box-sizing: inherit;
  height: 150px;
}

.project-list .i {
  padding: 20rpx;
  margin: 30rpx;
  background: #fff;
  border-radius: 4px;
  position: relative;
}
.project-list .i:active {
  background: #f6f6f6;
}
.project-list .i .flex-hd image {
  width: 140rpx;
  height: 140rpx;
  margin-right: 20rpx;
  border-radius: 50%;
}
.project-list .i .name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.project-list .i .info {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.project-list .i .count {
  font-size: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}
.project-list .i .count .f {
  font-size: 24rpx;
  color: #35318f;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
}
.project-list .i .count .f .inline {
  color: #f84d1f;
}

.project-index-status {
  padding: 0 15rpx 15rpx;
  display: flex;
  flex-wrap: wrap;
}
.project-index-status .i {
  position: relative;
  flex: 0 0 25%;
  box-sizing: border-box;
  height: 140rpx;
  padding: 10rpx;
}
.project-index-status .i .wrap {
  border-radius: 6px;
  padding: 15rpx;
  background-image: url(https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/index-statusbg.png);

  background-size: cover;
}
.project-index-status .i1 .wrap {
  background-color: #fe9e91;
}
.project-index-status .i2 .wrap {
  background-color: #fec791;
}
.project-index-status .i3 .wrap {
  background-color: #d6d793;
}
.project-index-status .i4 .wrap {
  background-color: #85b0d7;
}
.project-index-status .i5 .wrap {
  background-color: #85d7bd;
}
.project-index-status .i6 .wrap {
  background-color: #a985d7;
}
.project-index-status .i7 .wrap {
  background-color: #d78590;
}
.project-index-status .i .t {
  font-size: 24rpx;
}
.project-index-status .i .t1 {
  color: #a5392b;
}
.project-index-status .i .t2 {
  color: #ba7736;
}
.project-index-status .i .t3 {
  color: #6b7d23;
}
.project-index-status .i .t4 {
  color: #285091;
}
.project-index-status .i .t5 {
  color: #037f57;
}
.project-index-status .i .t6 {
  color: #642bad;
}
.project-index-status .i .t7 {
  color: #8d1223;
}
.project-index-status .i .s {
  position: absolute;
  font-size: 20rpx;
  right: 20rpx;
  top: 20rpx;
  color: #666666;
  background: #fff;
  border-radius: 100px;
  padding: 6rpx 10rpx;
  display: none;
}

.project-index-status .i .num {
  font-size: 36rpx;
  color: #fff;
  margin-top: 10rpx;
  font-weight: bold;
}

.project-index-list {
}
.project-index-list .tab-hd {
  text-align: center;
  font-size: 0;
  height: 36rpx;
  line-height: 36rpx;
  margin: 50rpx 0 30rpx 0;
  white-space: nowrap;
}
.project-index-list .tab-hd .i {
  display: inline-block;
  color: #666;
  position: relative;
  padding: 0 30rpx;
  font-size: 32rpx;
}
.project-index-list .tab-hd .i.active {
  color: #000;
}
.project-index-list .tab-hd .i:before {
  position: absolute;
  content: "";
  width: 1px;
  height: 100%;
  border-left: 1px solid #ddd;
  top: 0;
  left: 0;
}
.project-index-list .tab-hd .i:first-child:before {
  border: 0;
}
.project-index-list .list .i {
  background: #fff;
  margin: 0 30rpx 30rpx;
  padding: 30rpx;
  line-height: 1.8;
  position: relative;
  z-index: 3;
  border-radius: 4px;
  font-size: 28rpx;
}
.project-index-list .list .i .flex-hd {
  font-size: 20rpx;
}
.project-index-list .list .i .flex-hd.image {
  margin-right: 30rpx;
}
.project-index-list .list .i .flex-hd image {
  width: 80rpx;
  height: 80rpx;
}

.project-index-list .list .i .t {
  font-size: 28rpx;
  color: #333;
}
.project-index-list .list .i .info {
  font-size: 20rpx;
  color: #666;
}

.project-index-info {
  padding: 30rpx;
  border-radius: 4px;
  position: relative;
}
.project-index-info .flex-hd image {
  width: 140rpx;
  height: 140rpx;
  margin-right: 20rpx;
}
.project-index-info .name {
  font-size: 32rpx;
  color: #000;
  margin-bottom: 10rpx;
}
.project-index-info .info {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.project-index-tools {
  white-space: nowrap;
  padding: 30rpx 0;
  position: relative;
}
.project-index-tools:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.1);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  z-index: 2;
}
.project-index-tools .i {
  width: 160rpx;
  padding: 30rpx 0;
  position: relative;
  text-align: center;
  display: inline-block;
  border: 1px solid #eee;
  margin: 0 10rpx 10rpx;
  border-radius: 4px;
  box-shadow: 0px 2px 5px #ddd;
}
.project-index-tools .name {
  font-size: 24rpx;
  padding-top: 20rpx;
}
