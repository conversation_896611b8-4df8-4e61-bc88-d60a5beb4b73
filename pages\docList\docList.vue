<template>
	<view>
		<view class="list">
			<view class="li flex" v-for="(item,index) in list" @click="viewFile(item)">
				<view class="image">
					<image :src="getCover(item)"></image>
				</view>
				<view class="top flex-bd">
					<view class="name">{{item.FileName}}</view>
					<view class="time">{{item.CreateDatetime.replace('T',' ')}}</view>
				</view>
				<view class="btn">查看文档</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: []
			}
		},
		onLoad(e) {
			this.$http.get('/ArchivesCheckApi/GetFilesByProjectCode', {
				params: {
					projectCode: e.projectCode
				}
			}).then(res => {
				this.list = res.Data
			})
		},
		methods: {
			getCover(e){
				if(e.ServerFileName.indexOf('pdf')>-1){
					return '../../static/icon-pdf.png'
				}
				else{
					return '../../static/icon-docx.png'
				}
			},
			viewFile(e) {
				uni.showLoading({
					title: '加载中'
				})
				uni.downloadFile({
					url: this.$http.config.staticURL + e.ServerFileName,
					success: function(res) {
						uni.hideLoading()
						var filePath = res.tempFilePath;
						uni.openDocument({
							filePath: filePath,
							showMenu: true,
							success: function(res) {
								console.log('打开文档成功');
							}
						});
					},fail() {
						this.$u.toast('网络错误，加载失败')
					}
				});
			}
		}
	}
</script>

<style>
	page{
		background: #fff;
	}
	.list{
		background: #fff;
	}
	.li{
		margin: 0 30rpx;
		padding: 30rpx 0;
		position: relative; 
		border-bottom: 1px solid #ddd;
	}
	.li .name{
		font-weight: bold;
		font-size: 32rpx;
	}
	.li .image image{
		width: 70rpx;
		height: 70rpx;
		margin-right: 10rpx;
	}
	.li .time{
		font-size: 24rpx;
		color: #999;
	}
	.li .btn{
		background: #35318f;
		color: #fff;
		border-radius: 100rpx;
		padding: 10rpx 20rpx;
		font-size: 24rpx;
		margin-left: 30rpx;
	}
</style>