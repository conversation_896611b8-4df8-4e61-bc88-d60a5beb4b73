<template>
	<view class="swiper-wrap border-top">
		<view class="report-top">
			<!-- <view class="select flex" style="padding-top: 30rpx;">
				<view class="flex-bd">
					<u-form-item label="编号" label-width="110rpx" :border-bottom="false">
						<u-input height="30" type="text" placeholder="请输入编号" v-model="placeNumber" :border="true" />
					</u-form-item>
				</view>
				<view class="flex-hd u-m-l-30" @click="searchEmpty">
					清空
				</view>
			</view> -->
			<view class="select flex" style="padding:20rpx 30rpx">
				<view class="flex-bd">
					<u-form-item label="状态" label-width="110rpx" :border-bottom="false">
						<u-radio-group :size="30" v-model="statusValue" @change="radioGroupChange">
							<u-radio 
								v-for="(item, index) in statusList" :key="index" 
								:name="item.name"
								:disabled="item.disabled"
								:label-size="28"
								active-color="#35318f"
							>
								{{item.name}}
							</u-radio>
						</u-radio-group>
					</u-form-item>
				</view>
				<view class="flex-hd" @click="search">
					<view class="btn">查询</view>
				</view>
			</view>
		</view>
		<view class="swiper-box">
			<scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="onreachBottom">
				<u-card v-for="(item,index) in orderList" :key="index" padding="20" class="card-readyonly">
					<view slot="head">
						<view class="u-flex u-col-top u-row-between">
							<view class="u-flex-nowrap u-item-title u-font-28">名称：{{item.CheckName}}</view>
							<view><u-button type="error" size="mini" pain v-if="item.IsOverdue==1">超期</u-button></view>
						</view>
					</view>
					<view slot="body">
						<view class="u-text">
							检查周期：{{item.CheckTimeUnit}}
						</view>
						<view class="u-text">
							类型：{{item.CateText==null?'':item.CateText}}
						</view>
						<view class="u-text">
							最近一次检查：{{item.LastTimeText}}
						</view>
						<view class="detail-phone" v-if="item.InspectorCellphone" @click="callPhone(item.InspectorCellphone)">
							<i class="custom-icon custom-icon-lujingbeifen3"></i>
						</view>
					</view>
					<view slot="foot">
						<navigator class="u-text-right" :url="'../filesCheckDetail/filesCheckDetail?code='+item.Code">
							<view class="u-padding-right-10">查看记录 <i class="inline custom-icon custom-icon-right"></i></view>
						</navigator>
					</view>
				</u-card>
				<!-- <view class="mr30" v-if="!empty">
					<u-loadmore :status="loadmore.status" :icon-type="loadmore.iconType" :load-text="loadmore.loadText" />
				</view> -->
				<u-empty class="u-text-center" v-if="empty" text="暂无内容" margin-top="100"></u-empty>
				
				<view style="height: 1px;"></view>
			</scroll-view>
		</view>
		<!-- <view class="qrcode" @click="qrcode">
			<i class="custom-icon custom-icon-weibiaoti--"></i>
		</view> -->
		<u-picker mode="selector" range-key="label" v-model="peopleShow"  :default-selector="[0]" :range="checkPeople" @confirm="peopleOk"></u-picker>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				projectCode:'',
				orderList:'',
				empty:false,
				page:1,
				loadmore:{
					status: 'loading',
					iconType: 'flower',
					loadText: {
						loading: '努力加载中',
						nomore: '已无更多'
					}
				},
				checkUserName:'',
				placeNumber:'',
				InspectorUserCode:'',
				peopleShow:false,
				checkPeople:[],
				UserRemark:'',
				statusList: [
					{
						name: '全部',
						disabled: false
					},
					{
						name: '已超期',
						disabled: false
					},
					{
						name: '未超期',
						disabled: false
					}
				],
				// u-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中
				statusValue: '全部',
				isoverdue:'',
				cateCode:''
			}
		},
		onLoad(e) {
			console.log(e)
			this.cateCode = e.code
			var projectCode=e.projectcode
			this.projectCode=projectCode
			var UserRemark=uni.getStorageSync('UserRemark')
			this.UserRemark=UserRemark
			this.isWarn = e.isWarn
			this.isoverdue = e.isoverdue
			
			if(e.isoverdue==1){
				this.renderList(1,1,'').then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.statusValue='已超期'
			}
			else if(e.isWarn==1){
				this.renderList(1,'',1).then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
			}
			else{
				this.renderList(1,'','').then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
			}
			
			if(UserRemark=='009'){
				this.InspectorUserCode=uni.getStorageSync('UserCode')
			}
			
			
		},
		
		methods: {
			radioGroupChange(e) {
				console.log(e);
				this.statusValue = e
			},
			qrcode(){
				var that=this
				uni.scanCode({
					onlyFromCamera: false,
				    scanType: ['barCode', 'qrCode', 'datamatrix','pdf417'],
				    success: res => {
				        console.log(res);
						var code=res.result
						that.checkCode(code)
				    },
				    fail: res => {
						return this.$u.toast('扫码失败');
				    }
				})
			},
			checkCode(placeNumber){
				this.$http.get('/SafetyCheckApi/GetSafetyCheckByCode', {
					params:{
						placeNumber:placeNumber,
						code:""
					}
				}).then(res => {
					console.log(res)
					if(res.Data.PatrolPoint){
						uni.navigateTo({
							url:'../secureCheckDetail/secureCheckDetail?placeNumber='+placeNumber
						})
					}else{
						return this.$u.toast(res.ErrMsg);
					}
				})
			},
			searchEmpty(){
				this.loadmore.status='loading'
				this.placeNumber=''
				this.checkUserName=''
				this.statusValue='全部'
				this.isWarn = ''
				this.isoverdue = ''
				this.renderList(1,'','').then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.page=1
			},
			search(){
				if(this.statusValue=='已超期'){
					var isoverdue='1'
				}
				else if(this.statusValue=='未超期'){
					var isoverdue='0'
				}
				else{
					var isoverdue=''
				}
				this.renderList(1,isoverdue,'').then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.page=1
			},
			peopleOk(e){
				console.log(e)
				this.checkUserName=this.checkPeople[e[0]].label
				this.InspectorUserCode=this.checkPeople[e[0]].value
			},
			callPhone(e){
				uni.showModal({
				    title: '是否拨打电话？',
				    content: e,
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber:e
							})
				        } 
				    }
				});
			},
			//获取列表
			renderList(page,isoverdue,isWarn){
				
				return new Promise((resolve)=>{
					this.$http.get('/ArchivesCheckApi/GetArchivesCheckList', {
						params:{
							p:page,
							ps:10,
							placeNumber:this.placeNumber,
							projectCode:this.projectCode,
							isoverdue:isoverdue,
							isWarn:isWarn,
							cateCode:this.cateCode
						}
					}).then(res => {
						if(page==1&res.Data.length==0){
							this.empty=true
						}
						else{
							this.empty=false
							for(let index in res.Data){
								if(res.Data[index].LastInspectionTime){
									if(res.Data[index].LastInspectionTime.indexOf('0001')==-1){
										res.Data[index].LastInspectionTime=res.Data[index].LastInspectionTime.replace('T',' ')
									}
									else{
										res.Data[index].LastInspectionTime=''
									}
								}
								
							}
						}
						resolve(res.Data)
					})
				})		
			},
			onreachBottom() {
				if(this.loadmore.status=='nomore'){
					return
				}
				var orderList=this.orderList
				this.renderList(this.page+1,this.isoverdue,this.isWarn).then(res=>{
					console.log(res)
					for(let index in res){
						if(res[index].LastInspectionTime.indexOf('0001')==-1){
							res[index].LastInspectionTime=res[index].LastInspectionTime.replace('T',' ')
						}
						else{
							res[index].LastInspectionTime=''
						}
						orderList.push(res[index])
					}
					if(res.length>0){
						this.orderList=orderList
						this.page++
					}
					else{
						this.loadmore.status='nomore'
					}
				})
			}
		},
		
	}
</script>

<style>
	@import url("../fzr-reportList/style.css");
	.report-top .btn{
		padding: 6rpx 0;
		text-align: center;
	}
	.report-top u-button{
		margin-right: 30rpx;
	}
	.qrcode{
		position: fixed;
		width: 100rpx;
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
		background: #27246C;
		color: #fff;
		right: 30rpx;
		bottom: 30rpx;
		border-radius: 50%;
		z-index: 999;
	}
	.qrcode i{
		font-size: 48rpx;
	}
</style>
