<template>
	<view class="border-top">
		<!-- 基础信息 -->
		<view class="detail-top flex">
			<view class="flex-hd">
				<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_cs.png"></image>
			</view>
			<view class="flex-bd">
				<view class="t">工单号：{{info.OrderNo}}</view>
				<view class="desc">餐食预留</view>
			</view>
		</view>
		<view class="detail-finish">
			<view class="q">
				<view class="q-t">{{info.ApplyStatusName}}</view>
			</view>
		</view>
		<!-- 预约信息 -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">预约信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="申请人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.ApplyUser.UserName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="申请时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ApplyTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="用餐时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ReserveTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="用餐人数" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.Number}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.Remark==null"></text>
						<text v-else>{{info.Remark}}</text>
					</u-cell-item>					
				</u-cell-group>
				<view class="detail-his-list" v-if="showAddHis">
					<view class="i" v-for="(item,index) in addHisList" :key="index">
						<view class="t">
							<view class="span">{{item.ChangeTime}}</view>
						</view>
						<u-cell-group :border="flase">
							<u-cell-item  class="cell-item-reset" title="用餐时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.ReserveTime}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="用餐人数" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.Number}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.Remark}}</text>
							</u-cell-item>
						</u-cell-group>
					</view>
				</view>
				<view class="detail-more-btn" v-if="addHisList" @click="showAddHis=!showAddHis">
					历史预约记录<i :class="'custom-icon '+(showAddHis?'custom-icon-up-copy':'custom-icon-down')"></i>
				</view>
				<view class="detail-phone" @click="callPhone(info.CellPhone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		
		<!-- 信息审核 -->
		<block v-if="info.ApplyStatusName=='待审核'&&roleGroup!='admin'">
			<u-card :foot-border-top="false" padding="20" class="card-readyonly">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">审核信息</view>
					</view>
				</view>
				<view slot="body">
					<u-form :model="form" ref="uForm">
						<u-form-item label="预约结果" label-width="150rpx" :border-bottom="flase">
							<u-radio-group active-color="#27246c" v-model="value">
								<u-radio 
									@change="radioChange" 
									v-for="(item, index) in list" :key="index" 
									:name="item.name"
									:disabled="item.disabled"
								>
									{{item.name}}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						<u-form-item label="说明原因" label-width="150rpx" :border-bottom="flase">
							<u-input v-model="CheckRemark" type="textarea" :border="true" />
						</u-form-item>	
					</u-form>
				</view>
			</u-card>
			
			<view class="mr30">
				<u-button type="primary" @click="submit">确认预约</u-button>
			</view>
		</block>
		
		<!-- 审核信息 -->
		<block>
			<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatusName!='待审核'">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">审核信息</view>
					</view>
				</view>
				<view slot="body">
					<u-cell-group :border="flase">
						<u-cell-item class="cell-item-reset" title="审核时间" :border-bottom="flase" :arrow="flase" title-width="150">
							<text>{{info.CheckTime}}</text>
						</u-cell-item>
						<u-cell-item  class="cell-item-reset" title="联系人" :border-bottom="flase" :arrow="flase"  title-width="150" >
							<text>{{info.CheckUser.UserName}}</text>
						</u-cell-item>
						<u-cell-item class="cell-item-reset" title="审核结果" :border-bottom="flase" :arrow="flase" title-width="150">
							<text v-if="info.ApplyStatus==1||info.ApplyStatus>2">审核通过</text>
							<text v-if="info.ApplyStatus==2">审核未通过</text>
						</u-cell-item>
						<u-cell-item  class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="150" >
							<text v-if="info.CheckRemark==null"></text>
							<text v-else>{{info.CheckRemark}}</text>
						</u-cell-item>					
					</u-cell-group>
					<view class="detail-his-list" v-if="showCheckHis">
						<view class="i" v-for="(item,index) in checkHisList" :key="index">
							<view class="t">
								<view class="span">{{item.ChangeTime}}</view>
							</view>
							<u-cell-group :border="flase">
								<u-cell-item class="cell-item-reset" title="审核时间" :border-bottom="flase" :arrow="flase" title-width="150">
									<text>{{item.CheckTime}}</text>
								</u-cell-item>
								<u-cell-item class="cell-item-reset" title="审核结果" :border-bottom="flase" :arrow="flase" title-width="150">
									<text v-if="item.ApplyStatus==1">审核通过</text>
									<text v-if="item.ApplyStatus==2">审核未通过</text>
								</u-cell-item>
								<u-cell-item  class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="150" >
									<text v-if="item.Remark==null"></text>
									<text v-else>{{item.Remark}}</text>
								</u-cell-item>	
							</u-cell-group>
						</view>
					</view>
					<view class="detail-more-btn" v-if="checkHisList.length>0" @click="showCheckHis=!showCheckHis">
						历史审核记录<i :class="'custom-icon '+(showCheckHis?'custom-icon-up-copy':'custom-icon-down')"></i>
					</view>
				</view>
			</u-card>
			
			<view class="mr30" v-if="info.ApplyStatusName=='审核通过'&&roleGroup!='admin'">
				<u-button type="primary" @click="queren">确认取餐</u-button>
			</view>
		</block>
		
		<!-- 评价信息 -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatusName=='已完成'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">评价信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="评价人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.ApplyUser.UserName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价星级" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<u-rate :disabled="true" v-model="info.EvaluateLevel" :count="5" active-color="#eca805"></u-rate>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateContent}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.EvaluatePhotos" :key="index" :src="img" @click="lookImg(index,info.EvaluatePhotos)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
	</view>
</template>

<script>
	const app = getApp();
	import utils from '../../components/utils.js'
	export default {
		data() {
			return {
				hostUrl:app.globalData.hostUrl,
				list: [
					{
						name: '通过',
						disabled: false
					},
					{
						name: '不通过',
						disabled: false
					}
				],
				ReserveCode:'',
				addHisList:'',
				checkHisList:'',
				showCheckHis:'',
				showAddHis:'',
				info:'',
				canPass:'',
				CheckRemark:'',
				roleGroup:''
			}
		},
		methods: {
			queren(){
				var that=this
				uni.showModal({
				    title: '提示',
				    content: '是否确认取餐？',
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							that.$http.post('/ReserveMngApi/Execute?Docmd=complete', {
								ReserveCode:that.ReserveCode
							}).then(res => {
								that.$u.toast(res.ErrMsg);
								if(res.ErrCode==100){
									setTimeout(function(){
										that.load()
									},1000)
								}
							})
				        } 
				    }
				});
				
			},
			submit(){
				if(!this.canPass){
					return this.$u.toast('请选择审核结果');
				}
				if(this.canPass=='通过'){
					var ApplyStatus=1
				}
				else{
					var ApplyStatus=2
					if(!this.CheckRemark){
						return this.$u.toast('请填写原因');
					}
				}
				var params={
					ReserveCode:this.ReserveCode,
					ApplyStatus:ApplyStatus,
					CheckRemark:this.CheckRemark,
					CheckUserCode:uni.getStorageSync('UserCode')
				}
				var that=this
				this.$http.post('/ReserveMngApi/Execute?Docmd=check', params).then(res => {
					this.$u.toast(res.ErrMsg);
					if(res.ErrCode==100){
						setTimeout(function(){
							that.load()
						},1000)
					}
				})
			},
			radioChange(e){
				this.canPass=e
			},
			renderAddList(){
				return new Promise((resolve)=>{
					this.$http.get('/FoodReservationMngApi/GetDetail?Docmd=history', {
						params:{
							ReserveCode:this.ReserveCode
						}
					}).then(res => {
						resolve(res.Data)
					})
				})		
			},
			load(){
				this.$http.get('/FoodReservationMngApi/GetDetail?Docmd=main', {
					params:{
						ReserveCode:this.ReserveCode
					}
				}).then(res => {
					res.Data.ReserveTime=res.Data.ReserveTime?res.Data.ReserveTime.substring(0,19).replace('T',' '):''
					res.Data.ApplyTime=res.Data.ApplyTime?res.Data.ApplyTime.substring(0,19).replace('T',' '):''
					res.Data.CheckTime=res.Data.CheckTime?res.Data.CheckTime.substring(0,19).replace('T',' '):''
					res.Data.EvaluateTime=res.Data.EvaluateTime?res.Data.EvaluateTime.substring(0,19).replace('T',' '):''
					
					
					if(res.Data.EvaluatePhotos){
						res.Data.EvaluatePhotos=res.Data.EvaluatePhotos.split(',')
						for(let index in res.Data.EvaluatePhotos){
							res.Data.EvaluatePhotos[index]=this.hostUrl+res.Data.EvaluatePhotos[index]
						}
					}
					
					this.info=res.Data
				})
				
				
				this.renderAddList().then(res=>{
					if(res.length>0){
						var checkHisList=[]
						for(let index in res){
							res[index].ChangeTime=res[index].ChangeTime.replace('T',' ')
							res[index].ReserveTime=res[index].ReserveTime.replace('T',' ')
							res[index].CheckTime=res[index].CheckTime.replace('T',' ')
							if(res[index].ApplyStatus!=0){
								checkHisList.push(res[index])
							}
						}
						this.addHisList=res
						this.checkHisList=checkHisList
					}
				})
			}
		},
		onLoad(e) {
			
			console.log(e)
			var ReserveCode=e.code
			this.ReserveCode=ReserveCode
			this.load()
			
			var roleGroup=utils.getRoleGroup(uni.getStorageSync('UserRemark'))
			this.roleGroup=roleGroup
		},
	}
</script>

<style>
	page{
		background: #f6f6f6;
	}
</style>
