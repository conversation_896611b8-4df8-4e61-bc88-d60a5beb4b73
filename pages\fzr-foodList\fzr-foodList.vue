<template>
	<view class="swiper-wrap border-top">
		<view class="pd">
			<u-search placeholder="请输入单号" :show-action="true" v-model="keyword" shape="square" @custom="search"></u-search>
		</view>
		<u-tabs-swiper ref="uTabs" active-color="#35318f" :list="list" :current="current" @change="tabsChange" :is-scroll="false" swiperWidth="750"></u-tabs-swiper>
		<view class="swiper-box">
			<scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="onreachBottom">
				<view class="count">共{{orderCount}}条记录</view>
				<view class="list">
					<block v-for="(item,index) in orderList" :key="index">
						<view class="i flex border-all" v-if="nowType=='卤菜外卖'" @click="gotoUrl('../fzr-foodDetail/fzr-foodDetail?type=卤菜外卖&code='+item.OutCode+'&ProjectCode='+ProjectCode)">
							<view class="flex-hd">
								<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_wm.png"></image>
								<view>卤菜外卖</view>
							</view>
							<view class="flex-bd">
								<view class="t">单号：{{item.OrderNo}}</view>
								<view class="info">取餐时间：{{item.OutTime}}</view>
								<view class="info">申请时间：{{item.ApplyTime}}</view>
							</view>
							<view class="flex__ft"></view>
						</view>
						<view class="i flex border-all" v-else-if="nowType=='餐食预留'" @click="gotoUrl('../fzr-eatDetail/fzr-eatDetail?type=餐食预留&code='+item.ReserveCode+'&ProjectCode='+ProjectCode)">
							<view class="flex-hd">
								<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_cs.png"></image>
								<view>餐食预留</view>
							</view>
							<view class="flex-bd">
								<view class="t">单号：{{item.OrderNo}}</view>
								<view class="info">预留时间：{{item.ReserveTime}}</view>
								<view class="info">申请时间：{{item.ApplyTime}}</view>
							</view>
							<view class="flex__ft"></view>
						</view>
						<view class="i flex border-all" v-else @click="gotoUrl('../fzr-roomDetail/fzr-roomDetail?type=包厢预定&code='+item.BoxCode+'&ProjectCode='+ProjectCode)">
							<view class="flex-hd">
								<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_bx.png"></image>
								<view>包厢预定</view>
							</view>
							<view class="flex-bd">
								<view class="t">单号：{{item.OrderNo}}</view>
								<view class="info">预留时间：{{item.FromTime}}</view>
								<view class="info">申请时间：{{item.ApplyTime}}</view>
							</view>
							<view class="flex__ft"></view>
						</view>
					</block>
					<view class="mr30" v-if="!empty">
						<u-loadmore :status="loadmore.status" :icon-type="loadmore.iconType" :load-text="loadmore.loadText" />
					</view>
					<u-empty class="u-text-center" v-if="empty" text="暂无内容" margin-top="300"></u-empty>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [{
					name: '待处理',
					type:0
				}, {
					name: '待用餐',
					type:1
				}, {
					name: '已完成',
					type:2
				}],
				current: 0,
				keyword:'',
				orderList:'',
				empty:false,
				page:1,
				loadmore:{
					status: 'loading',
					iconType: 'flower',
					loadText: {
						loading: '努力加载中',
						nomore: '已无更多'
					}
				},
				orderCount:0,
				type:0,
				ProjectCode:'',
				nowType:''
			}
		},
		onLoad(e) {
			var ProjectCode=e.ProjectCode
			this.ProjectCode=ProjectCode
			if(e.type=='food'){
				
				var nowType='卤菜外卖'
				this.url='/VegetablesTakeoutMngApi/GetListByPorject'	
				var numApi ='/ProjectMngApi/GetTakeOutCount'
			}
			else if(e.type=='eat'){
				console.log('wwww')
				var nowType='餐食预留'
				this.url='/ReserveMngApi/GetListByPorject'
				var numApi ='/ProjectMngApi/GetReserveCount'
			}
			else{
				var nowType='包厢预定'
				this.url='/BoxMngApi/GetListByPorject'
				var numApi ='/ProjectMngApi/GetBoxCount'
			}
			this.nowType=nowType
			uni.setNavigationBarTitle({
			　　title:nowType
			})
			this.renderList(1,0).then(res=>{
				this.orderList=res
				if(res.length<10){
					this.loadmore.status='nomore'
				}
			})
			this.getNum()
			
			this.$http.get(numApi, {
				params:{
					UserCode:uni.getStorageSync('UserCode'),
					ProjectCode:ProjectCode,
					orderNo:'',
					applyStatusList:'0,1,2'
				}
			}).then(res => {
				var data =res.Data
				var list = [{
					name: '待处理',
					type:0,
					count:data.Count1
				}, {
					name: '待用餐',
					type:1,
					count:data.Count2
				}, {
					name: '已完成',
					type:2,
					count:data.Count3
				}]
				this.list = list
			})
		},
		methods: {
			gotoUrl(e){
				uni.navigateTo({
					url:e
				})
			},
			search(value) {
				this.renderList(1,this.type).then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.page=1
				this.getNum()
			},
			renderList(page,type=1){
				return new Promise((resolve)=>{
					this.$http.get(this.url, {
						params:{
							p:page,
							ps:10,
							UserCode:uni.getStorageSync('UserCode'),
							ProjectCode:this.ProjectCode,
							ApplyStatus:type,
							OrderNo:this.keyword
						}
					}).then(res => {
						if(page==1&res.Data.length==0){
							this.empty=true
						}
						else{
							this.empty=false
							for(let index in res.Data){
								res.Data[index].ApplyTime=res.Data[index].ApplyTime.replace('T',' ')
								res.Data[index].ReserveTime=res.Data[index].ReserveTime?res.Data[index].ReserveTime.replace('T',' '):''
								res.Data[index].FromTime=res.Data[index].FromTime?res.Data[index].FromTime.replace('T',' '):''
								res.Data[index].OutTime=res.Data[index].OutTime?res.Data[index].OutTime.replace('T',' '):''
							}
						}
						var list=[]
						resolve(res.Data)
					})
				})		
			},
			getNum(){
				this.$http.get('/UserApi/GetVariousNum', {
					params:{
						UserCode:uni.getStorageSync('UserCode'),
						ProjectCode:this.ProjectCode,
						ApplyStatus:this.type,
						OrderNo:this.keyword
					}
				}).then(res => {
					if(this.nowType=='卤菜外卖'){
						this.orderCount=res.Data.takeOutNum
					}
					else if(this.nowType=='餐食预留'){
						this.orderCount=res.Data.reserveNum
					}
					else{
						this.orderCount=res.Data.boxNum
					}
				})
			},
			tabsChange(item) {
				if(this.current == item.index){
					return
				}
				this.current = item.index;
				this.type=item.item.type
				this.loadmore.status='loading'
				this.renderList(1,item.item.type).then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.page=1
				this.getNum()
			},
			// scroll-view到底部加载更多
			onreachBottom() {
				if(this.loadmore.status=='nomore'){
					return
				}
				var orderList=this.orderList
				this.renderList(this.page+1,this.type).then(res=>{
					for(let index in res){
						orderList.push(res[index])
					}
					if(res.length>0){
						this.orderList=orderList
						this.page++
					}
					else{
						this.loadmore.status='nomore'
					}
				})
			}
		}
	}
</script>

<style>
	@import url("style.css");
</style>
