<template>
	<view class="border-top">
		<!-- 基础信息 -->
		<view class="detail-top flex">
			<view class="flex-hd">
				<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_hy.png"></image>
			</view>
			<view class="flex-bd">
				<view class="t">工单号：{{info.OrderNo}}</view>
				<view class="desc">会议预约</view>
			</view>
		</view>		
		<view class="detail-finish">
			<!-- <image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/finish.png"></image> -->
			<view class="q">
				<view class="q-t">{{info.ApplyStatusText}}</view>
			</view>
		</view>
		
		<!-- 预约信息 -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">预约信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="申请人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.ApplyUserName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="申请时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ApplyTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="预约时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.timeRange}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="会议名称" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.MeetingName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="申请会议室" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ApplyRoomName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="参会人数" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.PersonNumber}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="会议主持" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.MeetingHost}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="主席台名单" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.RostrumList}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="服务要求" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.DemandText">{{info.DemandText}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.Remark">{{info.Remark}}</text>
					</u-cell-item>			
				</u-cell-group>
				<view class="detail-his-list" v-if="showAddHis">
					<view class="i" v-for="(item,index) in addHisList" :key="index">
						<view class="t">
							<view class="span">{{item.ChangeTime}}</view>
						</view>
						<u-cell-group :border="flase">
							<u-cell-item  class="cell-item-reset" title="会议时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.ApplyFromTime}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="参会人数" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.PersonNumber}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.Remark}}</text>
							</u-cell-item>
						</u-cell-group>
					</view>
				</view>
				<view class="detail-more-btn" v-if="addHisList" @click="showAddHis=!showAddHis">
					历史预约记录<i :class="'custom-icon '+(showAddHis?'custom-icon-up-copy':'custom-icon-down')"></i>
				</view>
				<view class="detail-phone" @click="callPhone(info.CellPhone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		
		<!-- 安排会议 -->
		<block v-if="info.ApplyStatus==0&&!info.SecondCheckUserName&&roleGroup!='admin'">
			<u-card :foot-border-top="false" padding="20" class="card-readyonly">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">会议安排</view>
					</view>
				</view>
				<view slot="body">
					<u-form :model="form" ref="uForm">
						<u-form-item label="预约结果" label-width="150rpx" :border-bottom="flase">
							<u-radio-group active-color="#27246c" v-model="value">
								<u-radio 
									@change="radioChange" 
									v-for="(item, index) in checkList" :key="index" 
									:name="item.name"
									:disabled="item.disabled"
								>
									{{item.name}}
								</u-radio>
							</u-radio-group>
						</u-form-item>
						
						<block v-if="canPass=='通过'">
							<u-form-item label="备注" label-width="150rpx" :border-bottom="flase">
								<u-input v-model="CheckRemark" type="textarea" :border="true" />
							</u-form-item>
							<u-form-item label="会议室" label-width="150rpx" :border-bottom="flase">
								<u-input v-model="ApplyRoomName" @click="shiShow=true"  type="select" :border="true" />
							</u-form-item>
							<u-form-item label="服务组长" label-width="150rpx" :border-bottom="flase">
								<view slot="right">
									<u-button type="primary" size="mini" @click="selectPeople('',true,'')">选择</u-button>
								</view>
							</u-form-item>
							<view class="work" v-for="(item,index) in selectedlist1" :key="index">
								<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
								<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
								<view class="w-name">{{item.name}}</view>
							</view>
							<u-form-item label="服务人员" label-width="150rpx" :border-bottom="flase">
								<view slot="right">
									<u-button type="primary" size="mini" @click="selectPeople('duoxuan',false,'',true)">选择</u-button>
								</view>
							</u-form-item>
							<view class="work" v-if="selectedlist2">
								 <scroll-view scroll-x="true">
									<view class="i" v-for="(item,index) in selectedlist2" :key="index">
										<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
										<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
										<view class="w-name">{{item.name}}</view>
									</view>
							
								</scroll-view>
							</view>
							
							<u-form-item label="音响师" label-width="180rpx" :border-bottom="flase">
								<view slot="right">
									<u-button type="primary" size="mini" @click="selectPeople('',true,'',false,99)">选择</u-button>
								</view>
							</u-form-item>
							<view class="work sss" v-for="(item,index) in ysxPeople" :key="index" v-if="ysxPeople">
								<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
								<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
								<view class="w-name">{{item.name}}</view>
							</view>
							
							<u-form-item label="生成扭转单" label-width="150rpx" :border-bottom="flase">
								<u-switch v-model="nzdChecked" active-color="#27246c" slot="right" @change="nzd"></u-switch>
								
							</u-form-item>
						</block>
						<u-form-item v-else label="说明原因" label-width="150rpx" :border-bottom="flase">
							<u-input v-model="CheckRemark" type="textarea" :border="true" />
						</u-form-item>
					</u-form>
					
				</view>
			</u-card>
			<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="nzdShow">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">生成扭转单</view>
					</view>
				</view>
				<view slot="body">
					<u-form :model="form" ref="uForm">
						<u-form-item label="拍照" label-width="150rpx" :border-bottom="flase">
							<u-upload ref="uUpload1" :action="action" :header="uploadHeader" @on-remove="remove1" @on-success="uploadOk1"></u-upload>
						</u-form-item>
						<u-form-item label="设备部负责人" label-width="180rpx" :border-bottom="flase">
							<view slot="right">
								<u-button type="primary" size="mini" @click="selectPeople('',true,'',false,1)">选择</u-button>
							</view>
						</u-form-item>
						<view class="work" v-for="(item,index) in shebeiPeople" :key="index" v-if="shebeiPeople">
							<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
							<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
							<view class="w-name">{{item.name}}</view>
						</view>
						<u-form-item label="保洁部负责人" label-width="180rpx" :border-bottom="flase">
							<view slot="right">
								<u-button type="primary" size="mini" @click="selectPeople('',true,'',false,6)">选择</u-button>
							</view>
						</u-form-item>
						<view class="work" v-for="(item,index) in baojiePeople" :key="index" v-if="baojiePeople">
							<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
							<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
							<view class="w-name">{{item.name}}</view>
						</view>
						<u-form-item label="安保部负责人" label-width="180rpx" :border-bottom="flase">
							<view slot="right">
								<u-button type="primary" size="mini" @click="selectPeople('',true,'',false,7)">选择</u-button>
							</view>
						</u-form-item>
						<view class="work" v-for="(item,index) in anbaoPeople" :key="index" v-if="anbaoPeople">
							<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
							<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
							<view class="w-name">{{item.name}}</view>
						</view>
					</u-form>
					
				</view>	
			</u-card>
			<view class="mr30">
				<u-button type="primary" @click="submit">确定</u-button>
			</view>
		</block>
		
		<!-- 审核信息 -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatus>0||(info.ApplyStatus==0&&info.SecondCheckUserName)">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">审核信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item  class="cell-item-reset" title="审核时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.CheckTime}}</text>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="联系人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.CheckUserName}}</text>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="审核结果" :border-bottom="flase" :arrow="flase" title-width="150">
						<text v-if="info.ApplyStatus!=2">审核通过</text>
						<text v-else>审核不通过</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.CheckRemark">{{info.CheckRemark}}</text>
					</u-cell-item>					
				</u-cell-group>
				
				<view class="detail-his-list" v-if="showCheckHis">
					<view class="i" v-for="(item,index) in checkHisList" :key="index">
						<u-cell-group :border="flase">
							<u-cell-item  class="cell-item-reset" title="审核时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{info.CheckTime}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="联系人" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{info.CheckUserName}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="审核结果" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text v-if="info.ApplyStatus==1||info.ApplyStatus>2">审核通过</text>
								<text v-if="info.ApplyStatus==2">审核未通过</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text v-if="info.CheckRemark==null"></text>
								<text v-else>{{info.CheckRemark}}</text>
							</u-cell-item>
						</u-cell-group>
					</view>
				</view>
				<view class="detail-more-btn" v-if="checkHisList.length>0" @click="showCheckHis=!showCheckHis">
					历史审核记录<i :class="'custom-icon '+(showCheckHis?'custom-icon-up-copy':'custom-icon-down')"></i>
				</view>
				
				<view class="detail-phone" @click="callPhone(info.CheckUserCellPhone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		
		
		<!-- 会议安排 -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="(info.ApplyStatus>0||info.SecondCheckUserName)&&info.ApplyStatus!=2">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">会议安排</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="会议室" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.RoomName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="会议时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.timeRange}}</text>
					</u-cell-item>			
					<u-cell-item v-if="info.SecondCheckUserName" class="cell-item-reset" title="服务负责人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.SecondCheckUserName}}</text>
					</u-cell-item>
					<u-cell-item v-else class="cell-item-reset" title="服务人员" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.UserList.length}}</text>
					</u-cell-item>
				</u-cell-group>
				<view class="work">
					 <scroll-view scroll-x="true">
						<view class="i" v-for="(item,index) in info.UserList" :key="index">
							<u-avatar :show-sex="item.IsAccept==1" sex-icon="checkbox-mark" :show-level="item.IsLeader==1" level-icon="star-fill" :src="item.HeadImg?(hostUrl+item.HeadImg):'http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png'"></u-avatar>
							<view class="w-no" v-if="item.JobNumber">{{item.JobNumber}}</view>
							<view class="w-name" v-if="item.UserName">{{item.UserName}}</view>
						</view>						
					</scroll-view>					
				</view>
				<u-cell-group :border="flase">
					<u-cell-item v-if="info.SoundEngineer" class="cell-item-reset" title="音响师" :border-bottom="flase" :arrow="flase" title-width="150">
						<view class="work work2" style="text-align: left;">
							<view class="i">
								<u-avatar :src="info.SoundEngineerUserInfo.HeadImg?(hostUrl+info.SoundEngineerUserInfo.HeadImg):'http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png'"></u-avatar>
								<view class="w-no" v-if="info.SoundEngineerUserInfo.JobNumber">{{info.SoundEngineerUserInfo.JobNumber}}</view>
								<view class="w-name" v-if="info.SoundEngineerUserInfo.UserName">{{info.SoundEngineerUserInfo.UserName}}</view>
							</view>
						</view>
					</u-cell-item>
					
					
					
				</u-cell-group>
				<u-cell-item v-if="info.ReverseImage" class="cell-item-reset" title="扭转单" :border-bottom="flase" :arrow="flase" title-width="150">
					<view class="detail-img-list">
						<image v-for="(img,index) in info.ReverseImage" :key="index" :src="img" @click="lookImg(index,info.ReverseImage)"></image>
					</view>
				</u-cell-item>
				<u-cell-item class="cell-item-reset" title="其他人员" :border-bottom="flase" :arrow="flase" title-width="150">
					
				</u-cell-item>
				<view class="work work2">
					<view class="i" v-if="info.EquipmentChargeUser">
						
						<u-avatar :src="info.EquipmentChargeUserInfo.HeadImg?(hostUrl+info.EquipmentChargeUserInfo.HeadImg):'http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png'"></u-avatar>
						<view class="w-no" v-if="info.EquipmentChargeUserInfo.JobNumber">{{info.EquipmentChargeUserInfo.JobNumber}}</view>
						<view class="w-name" v-if="info.EquipmentChargeUserInfo.UserName">{{info.EquipmentChargeUserInfo.UserName}}</view>
						<view class="tag">设备部</view>
					</view>
					<view class="i" v-if="info.CleaningChargeUser">
						
						<u-avatar :src="info.CleaningChargeUserInfo.HeadImg?(hostUrl+info.CleaningChargeUserInfo.HeadImg):'http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png'"></u-avatar>
						<view class="w-no" v-if="info.CleaningChargeUserInfo.JobNumber">{{info.CleaningChargeUserInfo.JobNumber}}</view>
						<view class="w-name" v-if="info.CleaningChargeUserInfo.UserName">{{info.CleaningChargeUserInfo.UserName}}</view>
						<view class="tag">保洁部</view>
					</view>
					<view class="i" v-if="info.SecurityChargeUser">
						
						<u-avatar :src="info.SecurityChargeUserInfo.HeadImg?(hostUrl+info.SecurityChargeUserInfo.HeadImg):'http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png'"></u-avatar>
						<view class="w-no" v-if="info.SecurityChargeUserInfo.JobNumber">{{info.SecurityChargeUserInfo.JobNumber}}</view>
						<view class="w-name" v-if="info.SecurityChargeUserInfo.UserName">{{info.SecurityChargeUserInfo.UserName}}</view>
						<view class="tag">安保部</view>
					</view>
				</view>
			</view>
		</u-card>	
		
		<!-- 会议信息 -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.FactFromTimeText">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">会议信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="开始时间" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.FactFromTimeText}}</text>
					</u-cell-item>
					<u-cell-item v-if="info.FactEndTimeText" class="cell-item-reset" title="结束时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.FactEndTimeText}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.InspectRemark==null?'':info.InspectRemark}}</text>
					</u-cell-item>
					<u-form-item label="检查单" label-width="150rpx" :border-bottom="flase">
						<view class="detail-img-list">
							<image v-for="(img,index) in checkPhotos" :key="index" :src="img" @click="lookImg(index,checkPhotos)"></image>
						</view>
					</u-form-item>
					
				</u-cell-group>
			</view>
		</u-card>
		
		<!-- 检查信息 -->
		<block v-if="info.ApplyStatusText=='进行中'&&isZg">
			<u-card :foot-border-top="false" padding="20" class="card-readyonly">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">会议信息</view>
					</view>
				</view>
				<view slot="body">
					<u-form :model="form" ref="uForm">
						<u-form-item label="开始时间" label-width="150rpx" :border-bottom="flase" v-if="!info.FactFromTimeText">
							<u-input v-model="FactFromTime" @click="startShow=true" :border="true" disabled type="select" />
						</u-form-item>
						<u-form-item label="结束时间" label-width="150rpx" :border-bottom="flase" v-if="info.FactFromTimeText">
							<u-input v-model="FactEndTime" @click="endShow=true"  :border="true" disabled  type="select"/>
						</u-form-item>
						<u-form-item label="会前检查单" label-width="150rpx" :border-bottom="flase" v-if="!info.FactFromTimeText">
							<u-upload ref="uUpload2" :action="action" :header="uploadHeader" @on-remove="remove2" @on-success="uploadOk2" :file-list="info.InspectBeforeList" ></u-upload>
						</u-form-item>
						<u-form-item label="会后检查单" label-width="150rpx" :border-bottom="flase" v-if="info.FactFromTimeText">
							<u-upload ref="uUpload3" :action="action" :header="uploadHeader" @on-remove="remove3" @on-success="uploadOk3" :file-list="info.InspectAfterList" ></u-upload>
						</u-form-item>
						<u-form-item label="备注内容" label-width="150rpx" :border-bottom="flase">
							<u-input v-model="InspectRemark" type="textarea" :border="true" />
						</u-form-item>		
					</u-form>
				</view>
			</u-card>
			<u-picker mode="time" v-model="startShow" :params="params" @confirm="startDate"></u-picker>
			<u-picker mode="time" v-model="endShow" :params="params" @confirm="endDate">></u-picker>
			
			<view class="mr30">
				<u-row gutter="16">
					<!-- <u-col span="6">
						<u-button type="default" @click="submit(4)">暂存信息</u-button>
					</u-col> -->
					<u-col span="12">
						<u-button type="primary" @click="submitMeet()">提交信息</u-button>
					</u-col>
				</u-row>
			</view>
		</block>
		
		
		<!-- 反馈信息 -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.SoundEngineerRemark">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">音响师反馈</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item  class="cell-item-reset" title="内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.SoundEngineerRemark}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.SoundEngineerImage" :key="index" :src="img" @click="lookImg(index,info.SoundEngineerImage)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.CleaningRemark">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">保洁反馈</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item  class="cell-item-reset" title="内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.CleaningRemark}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.CleaningImage" :key="index" :src="img" @click="lookImg(index,info.CleaningImage)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.EquipmentRemark">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">设备反馈</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item  class="cell-item-reset" title="内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EquipmentRemark}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.EquipmentImage" :key="index" :src="img" @click="lookImg(index,info.EquipmentImage)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.SecurityRemark">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">安保反馈</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item  class="cell-item-reset" title="内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.SecurityRemark}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.SecurityImage" :key="index" :src="img" @click="lookImg(index,info.SecurityImage)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ReturnVisitRemark">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">跟踪回访</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item  class="cell-item-reset" title="内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ReturnVisitRemark}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.ReturnVisitImage" :key="index" :src="img" @click="lookImg(index,info.ReturnVisitImage)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		
		
		<block v-if="rePaidan">
			<u-card :foot-border-top="false" padding="20" class="card-readyonly">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">会议安排</view>
					</view>
				</view>
				<view slot="body">
					<u-form :model="form" ref="uForm">
						<u-form-item label="会议室" label-width="150rpx" :border-bottom="flase">
							<u-input v-model="ApplyRoomName" @click="shiShow=true"  type="select" :border="true" />
						</u-form-item>
						<u-form-item label="服务组长" label-width="150rpx" :border-bottom="flase">					
							<view slot="right">
								<u-button type="primary" size="mini" @click="selectPeople('',true,'notleader')">选择</u-button>
							</view>
						</u-form-item>
						<view class="work" v-for="(item,index) in selectedlist1" :key="index">
							<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
							<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
							<view class="w-name">{{item.name}}</view>
						</view>
						<block>
							<u-form-item label="服务人员" label-width="150rpx" :border-bottom="flase">
								<view slot="right">
									<u-button type="primary" size="mini" @click="selectPeople('duoxuan',false,'notleader',true)">选择</u-button>
								</view>
							</u-form-item>
							<view class="work" v-if="selectedlist2">
								 <scroll-view scroll-x="true">
									<view class="i" v-for="(item,index) in selectedlist2" :key="index">
										<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
										<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
										<view class="w-name">{{item.name}}</view>
									</view>
							
								</scroll-view>
								
							</view>
						</block>
						<u-form-item label="音响师" label-width="180rpx" :border-bottom="flase">
							<view slot="right">
								<u-button type="primary" size="mini" @click="selectPeople('',true,'',false,99)">选择</u-button>
							</view>
						</u-form-item>
						<view class="work sss" v-for="(item,index) in ysxPeople" :key="index" v-if="ysxPeople">
							<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
							<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
							<view class="w-name">{{item.name}}</view>
						</view>
						<u-form-item label="生成扭转单" label-width="150rpx" :border-bottom="flase">
							<u-switch v-model="nzdChecked" active-color="#27246c" slot="right" @change="nzd"></u-switch>
						</u-form-item>
					</u-form>
					
				</view>	
			</u-card>
			<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="renzdShow">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">生成扭转单</view>
					</view>
				</view>
				<view slot="body">
					<u-form :model="form" ref="uForm">
						<u-form-item label="拍照" label-width="150rpx" :border-bottom="flase">
							<u-upload ref="uUpload1" :action="action" :header="uploadHeader" @on-remove="remove1" @on-success="uploadOk1" ></u-upload>
						</u-form-item>
						<u-form-item label="设备部负责人" label-width="180rpx" :border-bottom="flase">
							<view slot="right">
								<u-button type="primary" size="mini" @click="selectPeople('',true,'',false,1)">选择</u-button>
							</view>
						</u-form-item>
						<view class="work" v-for="(item,index) in shebeiPeople" :key="index" v-if="shebeiPeople">
							<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
							<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
							<view class="w-name">{{item.name}}</view>
						</view>
						<u-form-item label="保洁部负责人" label-width="180rpx" :border-bottom="flase">
							<view slot="right">
								<u-button type="primary" size="mini" @click="selectPeople('',true,'',false,6)">选择</u-button>
							</view>
						</u-form-item>
						<view class="work" v-for="(item,index) in baojiePeople" :key="index" v-if="baojiePeople">
							<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
							<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
							<view class="w-name">{{item.name}}</view>
						</view>
						<u-form-item label="安保部负责人" label-width="180rpx" :border-bottom="flase">
							<view slot="right">
								<u-button type="primary" size="mini" @click="selectPeople('',true,'',false,7)">选择</u-button>
							</view>
						</u-form-item>
						<view class="work" v-for="(item,index) in anbaoPeople" :key="index" v-if="anbaoPeople">
							<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
							<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
							<view class="w-name">{{item.name}}</view>
						</view>
					</u-form>
					
				</view>	
			</u-card>
			<view class="mr30">
				<u-row gutter="16">
					<u-col span="6">
						<u-button type="erro" @click="rePaidan=!rePaidan,renzdShow=false">取消指派</u-button>
					</u-col>
					<u-col span="6">
						<u-button type="primary" @click="reSubmit">确定指派</u-button>
					</u-col>
				</u-row>
			</view>
		</block>
		
		<block v-if="roleGroup!='admin'">
			<block v-if="info.ApplyStatusText=='审核通过'||(info.ApplyStatusText=='待审核'&&info.SecondCheckUserName)">
				<view class="mr30" v-if="!rePaidan">
					<u-row gutter="16">
						<u-col span="6">
							<u-button type="erro" @click="cuicu">催促接单</u-button>
						</u-col>
						<u-col span="6">
							<u-button type="primary" @click="showRePaidan">重新指派</u-button>
						</u-col>
					</u-row>		
				</view>
			</block>
		</block>
		
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatus==6">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">评价信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="评价人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.ApplyUserName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价星级" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<u-rate :disabled="true" v-model="info.EvaluateLevel" :count="5" active-color="#eca805"></u-rate>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateContent}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.EvaluatePhotos" :key="index" :src="img" @click="lookImg(index,info.EvaluatePhotos)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		<!-- 派单人回访 -->
		<block v-if="info.ApplyStatusText!='待审核'&&info.ApplyStatusText!='审核通过'">
			<block v-if="info.CheckUserCode==DealUserCode&&!fankuiOk">
				<u-card :foot-border-top="false" padding="20" class="card-readyonly">
					<view slot="head">
						<view class="u-flex u-col-top u-row-between">
							<view class="u-flex-nowrap u-item-title u-font-28">跟踪回访</view>
						</view>
					</view>
					<view slot="body">
						<u-form :model="form" ref="uForm">
							<u-form-item label="内容" label-width="150rpx" :border-bottom="flase">
								<u-input v-model="FeedBackRemark" type="textarea" :border="true" />
							</u-form-item>
							<u-form-item label="照片" label-width="150rpx" :border-bottom="flase">
								<u-upload ref="uUpload4" :action="action" :header="uploadHeader" @on-remove="remove4" @on-success="uploadOk4"></u-upload>
							</u-form-item>						
						</u-form>
					</view>
				</u-card>
				<view class="mr30">
					<u-button type="primary" @click="nzdSubmit">提交</u-button>
				</view>
			</block>
		</block>
		<u-picker mode="selector" range-key="label" v-model="shiShow"  :default-selector="[0]" :range="roomList" @confirm="shi"></u-picker>
	</view>
</template>

<script>
	const app = getApp();
	import utils from '../../components/utils.js'
	export default {
		data() {
			return {
				ProjectCode:'',
				checkList: [
					{
						name: '通过',
						disabled: false
					},
					{
						name: '不通过',
						disabled: false
					}
				],
				params: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					second: false
				},
				MeetingCode:'',
				showAddHis:'',
				addHisList:'',
				checkHisList:'',
				showCheckHis:'',
				info:{
					OrderNo:''
				},
				canPass:'',
				shiShow:'',
				roomList:'',
				ApplyRoomCode:'',
				ApplyRoomName:'',
				selectedlist1:'',
				selectedlist2:'',
				limit:'',
				CheckRemark:'',
				selectLeader:true,
				isFzr:'',
				rePaidan:'',
				checkPhotos:'',
				uploadHeader:{
					Authorization: 'Bearer '+ uni.getStorageSync('access_token')
				},
				action:app.globalData.uploadUrl,
				hostUrl:app.globalData.hostUrl,
				roleGroup:'',
				renyuan:'',
				shebeiPeople:'',
				baojiePeople:'',
				anbaoPeople:'',
				ReverseImage:'',
				ysxPeople:'',
				photos1:[],
				photos2:[],
				photos3:[],
				nzdChecked:false,
				nzdShow:false,
				renzdShow:false,
				DealUserCode:'',
				nzdphoto:[],
				UserRemark:'',
				FeedBackRemark:'',
				fankuiOk:false,
				isZg:false,
				startShow:false,
				endShow:false,
				FactFromTime:'',
				FactEndTime:''
			}
		},
		methods: {
			startDate(e){
				console.log(e)
				this.FactFromTime=e.year+'-'+e.month+'-'+e.day+' '+e.hour+':'+e.minute+':00'
			},
			endDate(e){
				console.log(e)
				this.FactEndTime=e.year+'-'+e.month+'-'+e.day+' '+e.hour+':'+e.minute+':00'
			},
			submitMeet(e){
				var that=this
				var info = this.info
				var status = ''
				if(!info.FactFromTimeText){
					status = 4
					if(!this.photos2[0]){
						return that.$u.toast('上传会前检查！');
					}
					if(!that.FactFromTime){
						return that.$u.toast('请选择开始时间！');
					}
				}
				else{
					status = 5
					if(!this.photos3[0]){
						return that.$u.toast('上传会后检查！');
					}
					if(!that.FactEndTime){
						return that.$u.toast('请选择结束时间！');
					}
				}
				that.$http.post('/MeetingMngApi/Execute?Docmd=complete', {
					MeetingCode:that.MeetingCode,
					FactFromTime:that.FactFromTime,
					FactEndTime:that.FactEndTime,
					ApplyStatus:status,
					InspectBefore:that.photos2.toString(),
					InspectAfter:that.photos3.toString(),
					InspectRemark:that.InspectRemark
				}).then(res => {
					that.$u.toast(res.ErrMsg);
					if(res.ErrCode==100){
						setTimeout(function(){
							that.load()
						},1000)
					}
				})	
			},
			nzdSubmit(){
				var that = this
				var FeedBackRemark = this.FeedBackRemark
				var nzdphoto =this.nzdphoto
				var FeedBackImage =''
				if(nzdphoto.length>0){
					FeedBackImage = nzdphoto.toString()
				}
				var MeetingCode = this.MeetingCode
				var DealUserCode = uni.getStorageSync('UserCode')
				this.DealUserCode = DealUserCode
				var UserRemark=uni.getStorageSync('UserRemark')
				var Type=''
				if(UserRemark==108||UserRemark==109){
					Type=3
				}
				else if(UserRemark==112){
					Type=1
				}
				else if(UserRemark==110||UserRemark==111){
					Type=4
				}
				else if(UserRemark=='004'||UserRemark=='009'){
					Type=2
				}
				else{
					Type=5
				}
				if(!FeedBackRemark){
					return this.$u.toast('请填写内容！');
				}
				
				this.$http.post('/MeetingMngApi/Execute?doCmd=feedback', {
					MeetingCode,DealUserCode,Type,FeedBackRemark,FeedBackImage
				}).then(res => {
					that.$u.toast(res.ErrMsg);
					if(res.ErrCode==100){
						setTimeout(function(){
							that.FeedBackRemark =''
							that.nzdphoto = []
							that.$refs.uUpload4.clear();
							that.load()
						},1000)
					}
				})
			},
			lookImg(index,imgList){
				uni.previewImage({
					current:index,
					urls: imgList
				})
			},
			showRePaidan(){
				this.rePaidan= !this.rePaidan
				
			},
			nzd(status){
				console.log(status)
				if(status){
					this.nzdShow = true
					this.renzdShow = true
				}
				else{
					this.nzdShow = false
					this.renzdShow = false
				}
			},
			remove1(index){
				this.photos1.splice(index,1)
				console.log(index)
			},
			remove2(index){
				this.photos2.splice(index,1)
				console.log(index)
			},
			remove3(index){
				this.photos3.splice(index,1)
				console.log(index)
			},
			remove4(index){
				this.nzdphoto.splice(index,1)
				console.log(index)
			},
			uploadOk1(data,index,lists){
				console.log(data)
				this.photos1.push(data.RetValue)
			},
			uploadOk2(data,index,lists){
				console.log(data)
				this.photos2.push(data.RetValue)
			},
			uploadOk3(data,index,lists){
				console.log(data)
				this.photos3.push(data.RetValue)
			},
			uploadOk4(data,index,lists){
				console.log(data)
				this.nzdphoto.push(data.RetValue)
			},
			callPhone(e){
				uni.showModal({
				    title: '是否拨打电话？',
				    content: e,
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber:e
							})
				        } 
				    }
				});
			},
			gotoUrl(e){
				uni.navigateTo({
					url:'../yz-comment/yz-comment?RepairCode='+this.RepairCode+'&code='+this.info.RepairNo
				})
			},
			renderAddList(){
				return new Promise((resolve)=>{
					this.$http.get('/MeetingMngApi/GetDetail?Docmd=history', {
						params:{
							MeetingCode:this.MeetingCode
						}
					}).then(res => {
						resolve(res.Data)
					})
				})		
			},
			radioChange(e){
				this.canPass=e
			},
			shi(e){
				console.log(e)
				this.ApplyRoomName=this.roomList[e[0]].label
				this.ApplyRoomCode=this.roomList[e[0]].value
			},
			selectPeople(type='',selectFzr=false,range='',limit=false,renyuan=2){
				console.log(type)
				this.selectFzr=selectFzr
				this.renyuan=renyuan
				var arr=[]
				var list=this.info.UserList
				for(let index in list){
					if(list[index].IsAccept==1){
						arr.push(list[index].ServiceUserCode)
					}
				}
				if(limit){

					uni.navigateTo({
						url:'../selectPeople/selectPeople?ProjectCode='+this.ProjectCode+'&type='+type+'&renyuan='+renyuan+'&range='+range+'&limit='+JSON.stringify(arr)
					})
				}
				else{
					uni.navigateTo({
						url:'../selectPeople/selectPeople?ProjectCode='+this.ProjectCode+'&type='+type+'&renyuan='+renyuan+'&range='+range
					})
				}
				
			},
			cuicu(){
				this.$http.post('/MeetingMngApi/Execute?Docmd=urge', {
					MeetingCode:this.MeetingCode
				}).then(res => {
					this.$u.toast(res.ErrMsg);
				})
			},
			submit(){
				if(!this.canPass){
					return this.$u.toast('请选择预约结果');
				}
				if(this.canPass=='通过'){
					if(!this.ApplyRoomName){
						return this.$u.toast('请选择会议室');
					}
					if(!this.selectedlist1){
						return this.$u.toast('请选择服务组长');
					}
					// if(!this.selectedlist2){
					// 	return this.$u.toast('请选择服务人员');
					// }
					if(this.nzdChecked&&this.photos1.length==0){
						return this.$u.toast('请上传照片');
					}
					var codes=[]
					var selectedlist2=this.selectedlist2
					for(let index in selectedlist2){
						codes.push(selectedlist2[index].usercode)
					}
					var params={
						MeetingCode:this.MeetingCode,
						ApplyStatus:1,
						CheckRemark:this.CheckRemark,
						RoomCode:this.ApplyRoomCode,
						DealUserCode:uni.getStorageSync('UserCode'),
						LeaderUserCode:this.selectedlist1[0].usercode,
						ServiceUserCodes:codes.toString(),
						FromDateTime:this.info.ApplyFromTime,
						EndDateTime:this.info.ApplyEndTime,
						ProjectCode:this.ProjectCode,
						ReverseImage:this.photos1.toString(),
						EquipmentChargeUser:this.shebeiPeople?this.shebeiPeople[0].usercode:'',
						CleaningChargeUser:this.baojiePeople?this.baojiePeople[0].usercode:'',
						CleaningChargeUser:this.baojiePeople?this.baojiePeople[0].usercode:'',
						SecurityChargeUser:this.anbaoPeople?this.anbaoPeople[0].usercode:'',
						SoundEngineer:this.ysxPeople?this.ysxPeople[0].usercode:'',
					}
				}
				else{
					if(!this.CheckRemark){
						return this.$u.toast('请输入原因');
					}
					var params={
						MeetingCode:this.MeetingCode,
						ApplyStatus:2,
						CheckRemark:this.CheckRemark,
						RoomCode:'',
						DealUserCode:uni.getStorageSync('UserCode'),
						LeaderUserCode:'',
						ServiceUserCodes:'',
						FromDateTime:this.info.ApplyFromTime,
						EndDateTime:this.info.ApplyEndTime,
						ProjectCode:this.ProjectCode
					}
				}
				var that=this
				this.$http.post('/MeetingMngApi/Execute?Docmd=assign', params).then(res => {
					this.$u.toast(res.ErrMsg);
					if(res.ErrCode==100){
						setTimeout(function(){
							that.load()
							that.photos1 = []
						},1000)
					}
				})
			},
			reSubmit(){
				if(!this.selectedlist1){
					return this.$u.toast('请选择服务负责人');
				}
				if(!this.selectLeader){
					if(!this.selectedlist2){
						return this.$u.toast('请选择服务人员');
					}
				}	
				var codes=[]
				var selectedlist2=this.selectedlist2
				for(let index in selectedlist2){
					codes.push(selectedlist2[index].usercode)
				}
				var params={
					MeetingCode:this.MeetingCode,
					ApplyStatus:1,
					CheckRemark:'',
					RoomCode:this.ApplyRoomCode?this.ApplyRoomCode:this.info.ApplyRoomCode,
					DealUserCode:uni.getStorageSync('UserCode'),
					LeaderUserCode:this.selectedlist1[0].usercode,
					ServiceUserCodes:codes.toString(),
					FromDateTime:this.info.ApplyFromTime,
					EndDateTime:this.info.ApplyEndTime,
					ProjectCode:this.ProjectCode,
					ReverseImage:this.photos1.toString(),
					EquipmentChargeUser:this.shebeiPeople?this.shebeiPeople[0].usercode:'',
					CleaningChargeUser:this.baojiePeople?this.baojiePeople[0].usercode:'',
					CleaningChargeUser:this.baojiePeople?this.baojiePeople[0].usercode:'',
					SecurityChargeUser:this.anbaoPeople?this.anbaoPeople[0].usercode:'',
					SoundEngineer:this.ysxPeople?this.ysxPeople[0].usercode:''
				}
				var that=this
				this.$http.post('/MeetingMngApi/Execute?Docmd=assign', params).then(res => {
					this.$u.toast(res.ErrMsg);
					if(res.ErrCode==100){
						setTimeout(function(){
							that.load()
							that.photos1 = []
							that.rePaidan=!that.rePaidan
							that.selectedlist1=''
							that.selectLeader=true
							that.selectedlist2=''
							that.ysxPeople=''
							that.shebeiPeople=''
							that.baojiePeople=''
							that.anbaoPeople=''
						},1000)
					}
				})
			},
			load(){
				// this.selectedlist1=[]
				// this.selectedlist2=[]
				this.$http.get('/MeetingMngApi/GetDetail?Docmd=main', {
					params:{
						MeetingCode:this.MeetingCode
					}
				}).then(res => {
					var info=res.Data
					//格式化时间
					info.ApplyTime=info.ApplyTime?info.ApplyTime.replace('T',' '):''
					info.DispatchTime=info.DispatchTime?info.DispatchTime.replace('T',' '):''
					info.AcceptTime=info.AcceptTime?info.AcceptTime.replace('T',' '):''
					info.CompleteTime=info.CompleteTime?info.CompleteTime.replace('T',' '):''
					info.EvaluateTime=info.EvaluateTime?info.EvaluateTime.replace('T',' '):''
					info.CheckTime=info.CheckTime?info.CheckTime.replace('T',' '):''
					//格式化会议时间
					var ApplyFromTime=info.ApplyFromTime.split('T')
					var ApplyEndTime=info.ApplyEndTime.split('T')
					info.timeRange=ApplyFromTime[0]+' '+ApplyFromTime[1]+'至'+ApplyEndTime[1]					
					
					if(res.Data.EvaluatePhotos){
						res.Data.EvaluatePhotos=res.Data.EvaluatePhotos.split(',')
						for(let index in res.Data.EvaluatePhotos){
							res.Data.EvaluatePhotos[index]=this.hostUrl+res.Data.EvaluatePhotos[index]
						}
					}
					
					var checkPhotos=[]
					if(res.Data.InspectBefore){
						res.Data.InspectBefore=res.Data.InspectBefore.split(',')
						for(let index in res.Data.InspectBefore){
							checkPhotos.push(this.hostUrl+res.Data.InspectBefore[index])
						}
					}
					if(res.Data.InspectIn){
						res.Data.InspectIn=res.Data.InspectIn.split(',')
						for(let index in res.Data.InspectIn){
							checkPhotos.push(this.hostUrl+res.Data.InspectIn[index])
						}
					}
					if(res.Data.InspectAfter){
						res.Data.InspectAfter=res.Data.InspectAfter.split(',')
						for(let index in res.Data.InspectAfter){
							checkPhotos.push(this.hostUrl+res.Data.InspectAfter[index])
						}
					}
					
					if(res.Data.ReverseImage){
						res.Data.ReverseImage=res.Data.ReverseImage.split(',')
						for(let index in res.Data.ReverseImage){
							res.Data.ReverseImage[index]=this.hostUrl+res.Data.ReverseImage[index]
						}
					}
					
					
					if(res.Data.CleaningImage){
						res.Data.CleaningImage=utils.imgArray(res.Data.CleaningImage)
					}
					if(res.Data.SecurityImage){
						res.Data.SecurityImage=utils.imgArray(res.Data.SecurityImage)
					}
					if(res.Data.EquipmentImage){
						res.Data.EquipmentImage=utils.imgArray(res.Data.EquipmentImage)
					}
					if(res.Data.ReturnVisitImage){
						res.Data.ReturnVisitImage=utils.imgArray(res.Data.ReturnVisitImage)
					}
					if(res.Data.SoundEngineerImage){
						res.Data.SoundEngineerImage=utils.imgArray(res.Data.SoundEngineerImage)
					}
					
					this.checkPhotos=checkPhotos
					
					this.info=info
					
					var UserRemark=uni.getStorageSync('UserRemark')
					if(UserRemark=='002'){
						this.isFzr=true
					}
					
					//回访控制
					if((info.CheckUserCode == this.DealUserCode)&&info.ReturnVisitRemark!=null){
						this.fankuiOk = true
					}
					
					//检测主管是否为服务人员
					var UserList = info.UserList
					for(let i in UserList){
						if(UserList[i].ServiceUserCode==uni.getStorageSync('UserCode')){
							this.isZg = true
							break;
						}
					}
				})
				
				//申请记录
				this.renderAddList().then(res=>{
					if(res.length>0){
						var checkHisList=[]
						for(let index in res){
							res[index].ChangeTime=res[index].ChangeTime.replace('T',' ')
							res[index].CheckTime=res[index].CheckTime.replace('T',' ')
							res[index].ApplyTime=res[index].ApplyTime.replace('T',' ')
							
							//格式化会议时间
							var ApplyFromTime=res[index].ApplyFromTime.split('T')
							var ApplyEndTime=res[index].ApplyEndTime.split('T')
							var timeRange=ApplyFromTime[0]+' '+ApplyFromTime[1]+'至'+ApplyEndTime[1]							
							res[index].timeRange=timeRange
							
							if(res[index].ApplyStatus!=0){
								checkHisList.push(res[index])
							}
						}
						this.addHisList=res
						this.checkHisList=checkHisList
					}
				})
			}
		},
		onShow() {
			uni.$on('handleFun', res => {
				if(this.renyuan == 2){
					if(this.selectFzr==1){
						this.selectedlist1 = res.selectedlist;
						console.log(JSON.stringify(res.selectedlist))
						if(JSON.stringify(res.selectedlist).indexOf('是')==-1){
							this.selectLeader=false
						}
						else{
							this.selectLeader=true
						}
					}
					else{				
						this.selectedlist2 = res.selectedlist;
						
					}
				}
				
				else if(this.renyuan == 1){
					this.shebeiPeople = res.selectedlist;
				}
				else if(this.renyuan == 6){
					this.baojiePeople = res.selectedlist;
				}
				else if(this.renyuan == 7){
					this.anbaoPeople = res.selectedlist;
				}
				else{
					this.ysxPeople = res.selectedlist;
				}
				// 清除监听
				uni.$off('handleFun');
			});
		},
		onLoad(e) {
			//详情
			console.log(e)
			var UserRemark = uni.getStorageSync('UserRemark')
			var roleGroup=utils.getRoleGroup(UserRemark)
			this.roleGroup=roleGroup
			var MeetingCode=e.code
			this.MeetingCode=MeetingCode
			this.ProjectCode=e.ProjectCode			
			this.load()
			this.DealUserCode = uni.getStorageSync('UserCode')
			this.UserRemark = UserRemark
			//会议室
			this.$http.get('/MeetingMngApi/GetAllList', {
				params:{
					ProjectCode:uni.getStorageSync('projectCode'),
					p:1,
					ps:50
				}
			}).then(res => {
				var list=[]
				for(let index in res.Data){
					var item={
						label:res.Data[index].RoomName,
						value:res.Data[index].RoomCode
					}
					list.push(item)
				}
				this.roomList=list
			})
			
		}
	}
</script>

<style>
	page{
		background: #f6f6f6;
	}
	.u-avatar__sex{
		background: #35318f;
	}
	.work2{
	}
	.work2 .i{
		line-height: 1.4;
		display: inline-block;
		text-align: center;
		    margin: 0 20rpx;
			position: relative;
	}
	.work2 .i .tag{
		color: #ea6c23;
		top: 0;
		right:0;
		z-index: 2;
		font-size: 24rpx;
		margin-top: 4px;
	}
</style>
