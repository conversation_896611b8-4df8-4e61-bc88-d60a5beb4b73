<template>
	<view class="border-top">
		<view class="detail-top flex">
			<view class="flex-hd">
				<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_hy.png"></image>
			</view>
			<view class="flex-bd">
				<view class="t">工单号：{{info.OrderNo}}</view>
				<view class="desc">会议预约</view>
			</view>
			<view class="flex-hd">
				<view class="btn">{{info.ApplyStatusText}}</view>
			</view>
		</view>
		<view class="detail-finish" v-if="info.ApplyStatus==5||info.ApplyStatus==6">
			<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/finish.png"></image>
		</view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">预约信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="申请人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.ApplyUserName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="申请时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ApplyTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="预约时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.timeRange}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="会议名称" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.MeetingName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="申请会议室" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ApplyRoomName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="参会人数" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.PersonNumber}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="会议主持" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.MeetingHost}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="主席台名单" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.RostrumList}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="服务要求" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.DemandText">{{info.DemandText}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.Remark">{{info.Remark}}</text>
					</u-cell-item>				
				</u-cell-group>
				<view class="detail-his-list" v-if="showAddHis">
					<view class="i" v-for="(item,index) in addHisList" :key="index">
						<view class="t">
							<view class="span">{{item.ChangeTime}}</view>
						</view>
						<u-cell-group :border="flase">
							<u-cell-item  class="cell-item-reset" title="会议时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.ApplyFromTime}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="参会人数" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.PersonNumber}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.Remark}}</text>
							</u-cell-item>
						</u-cell-group>
					</view>
				</view>
				<view class="detail-more-btn" v-if="addHisList" @click="showAddHis=!showAddHis">
					历史预约记录<i :class="'custom-icon '+(showAddHis?'custom-icon-up-copy':'custom-icon-down')"></i>
				</view>
			</view>
		</u-card>
		
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatus>0">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">审核信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item  class="cell-item-reset" title="审核时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.CheckTime}}</text>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="联系人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.ProjectExt.ProjectLeaderUser.UserName}}</text>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="审核结果" :border-bottom="flase" :arrow="flase" title-width="150">
						<text v-if="info.ApplyStatus!=2">审核通过</text>
						<text v-else>审核不通过</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.CheckRemark">{{info.CheckRemark}}</text>
					</u-cell-item>
					
				</u-cell-group>			
				<view class="detail-his-list" v-if="showCheckHis">
					<view class="i" v-for="(item,index) in checkHisList" :key="index">
						<view class="t">
							<view class="span">{{item.ChangeTime}}</view>
						</view>
						<u-cell-group :border="flase">
							<u-cell-item  class="cell-item-reset" title="审核时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>2020/01/10 12:10:20</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="联系人" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>2020/01/10 12:10:20</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="审核结果" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>通过</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>报修内容报修内容报修内容报修内容报修内容报修内容</text>
							</u-cell-item>
						</u-cell-group>
					</view>
				</view>
				<view class="detail-more-btn" v-if="checkHisList.length>0" @click="showCheckHis=!showCheckHis">
					历史审核记录<i :class="'custom-icon '+(showCheckHis?'custom-icon-up-copy':'custom-icon-down')"></i>
				</view>
				
				<view class="detail-phone" @click="callPhone(info.ProjectExt.ProjectLeaderUser.CellPhone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		
		<block v-if="info.ApplyStatusText=='待审核'&&info.SecondCheckUserName">
			<u-card :foot-border-top="false" padding="20" class="card-readyonly">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">会议安排</view>
					</view>
				</view>
				<view slot="body">
					<u-form :model="form" ref="uForm">
						<block>
							<u-form-item label="服务负责人" label-width="150rpx" :border-bottom="flase">
								<view slot="right">
									<u-button type="primary" size="mini" @click="selectPeople('',true,'notleader')">选择</u-button>
								</view>
							</u-form-item>
							<view class="work" v-for="(item,index) in selectedlist1" :key="index">
								<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
								<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
								<view class="w-name">{{item.name}}</view>
							</view>
							<u-form-item label="服务人员" label-width="150rpx" :border-bottom="flase">
								<view slot="right">
									<u-button type="primary" size="mini" @click="selectPeople('duoxuan',false,'notleader')">选择</u-button>
								</view>
							</u-form-item>
							<view class="work" v-if="selectedlist2">
								 <scroll-view scroll-x="true">
									<view class="i" v-for="(item,index) in selectedlist2" :key="index">
										<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
										<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
										<view class="w-name">{{item.name}}</view>
									</view>
							
								</scroll-view>
								
							</view>
						</block>
							
					</u-form>
					
				</view>	
			</u-card>
			<view class="mr30">
				<u-row gutter="16">
					<u-button type="primary" @click="reSubmit">确定</u-button>
				</u-row>
			</view>
		</block>
		
		
		<block v-if="info.ApplyStatus>0&&info.ApplyStatus!=2">
			<u-card :foot-border-top="false" padding="20" class="card-readyonly">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">会议安排</view>
					</view>
				</view>
				<view slot="body">
					<u-cell-group :border="flase">
						<u-cell-item class="cell-item-reset" title="会议室" :border-bottom="flase" :arrow="flase" title-width="150">
							<text>{{info.ApplyRoomName}}</text>
						</u-cell-item>
						<u-cell-item  class="cell-item-reset" title="会议时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
							<text>{{info.timeRange}}</text>
						</u-cell-item>			
						<u-cell-item class="cell-item-reset" title="服务人员" :border-bottom="flase" :arrow="flase" title-width="150">
							<text>{{info.UserList.length}}</text>
						</u-cell-item>		
					</u-cell-group>
					<view class="work">
						 <scroll-view scroll-x="true">
							<view class="i" v-for="(item,index) in info.UserList" :key="index">
								<u-avatar :show-sex="item.IsAccept==1" sex-icon="checkbox-mark" :show-level="item.IsLeader==1" level-icon="star-fill" :src="item.HeadImg?'item.HeadImg':'http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png'"></u-avatar>
								<view class="w-name" v-if="item.UserName">{{item.UserName}}</view>
							</view>						
						</scroll-view>
						
					</view>
				</view>
			</u-card>	
			<block v-if="info.ApplyStatusText=='审核通过'">
				<view class="mr30" v-if="!rePaidan">
					<u-row gutter="16">
						<u-col span="6">
							<u-button type="erro" @click="cuicu">催促接单</u-button>
						</u-col>
						<u-col span="6">
							<u-button type="primary" @click="rePaidan=!rePaidan">重新指派</u-button>
						</u-col>
					</u-row>		
				</view>
			</block>
			<block v-if="rePaidan">
				<u-card :foot-border-top="false" padding="20" class="card-readyonly">
					<view slot="head">
						<view class="u-flex u-col-top u-row-between">
							<view class="u-flex-nowrap u-item-title u-font-28">会议安排</view>
						</view>
					</view>
					<view slot="body">
						<u-form :model="form" ref="uForm">
							<block>
								<u-form-item label="服务负责人" label-width="150rpx" :border-bottom="flase">
									<view slot="right">
										<u-button type="primary" size="mini" @click="selectPeople('',true,'notleader')">选择</u-button>
									</view>
								</u-form-item>
								<view class="work" v-for="(item,index) in selectedlist1" :key="index">
									<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
									<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
									<view class="w-name">{{item.name}}</view>
								</view>
								<u-form-item label="服务人员" label-width="150rpx" :border-bottom="flase">
									<view slot="right">
										<u-button type="primary" size="mini" @click="selectPeople('duoxuan',false,'notleader')">选择</u-button>
									</view>
								</u-form-item>
								<view class="work" v-if="selectedlist2">
									 <scroll-view scroll-x="true">
										<view class="i" v-for="(item,index) in selectedlist2" :key="index">
											<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
											<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
											<view class="w-name">{{item.name}}</view>
										</view>
								
									</scroll-view>
									
								</view>
							</block>
								
						</u-form>
						
					</view>	
				</u-card>
				<view class="mr30">
					<u-row gutter="16">
						<u-col span="6">
							<u-button type="erro" @click="rePaidan=!rePaidan">取消指派</u-button>
						</u-col>
						<u-col span="6">
							<u-button type="primary" @click="reSubmit">确定指派</u-button>
						</u-col>
					</u-row>
				</view>
			</block>
		</block>
		
		
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatus>4">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">会议信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="开始时间" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.FactFromTimeText}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="结束时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.FactEndTimeText}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.InspectRemark==null?'':info.InspectRemark}}</text>
					</u-cell-item>
					<u-form-item label="检查单" label-width="150rpx" :border-bottom="flase">
						<view class="detail-img-list">
							<image v-for="(img,index) in checkPhotos" :key="index" :src="img" @click="lookImg(index,checkPhotos)"></image>
						</view>
					</u-form-item>
					
				</u-cell-group>
			</view>
		</u-card>
		
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatus==6">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">评价信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="评价人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.applyusername}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价星级" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<u-rate :disabled="true" v-model="info.EvaluateLevel" :count="5" active-color="#eca805"></u-rate>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateContent}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.EvaluatePhotos" :key="index" :src="img" @click="lookImg(index,info.EvaluatePhotos)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				ProjectCode:'',
				checkList: [
					{
						name: '通过',
						disabled: false
					},
					{
						name: '不通过',
						disabled: false
					}
				],
				MeetingCode:'',
				showAddHis:'',
				addHisList:'',
				checkHisList:'',
				showCheckHis:'',
				info:'',
				canPass:'',
				shiShow:'',
				roomList:'',
				ApplyRoomCode:'',
				ApplyRoomName:'',
				selectedlist1:'',
				selectedlist2:'',
				limit:'',
				CheckRemark:'',
				selectLeader:true,
				isFzr:'',
				rePaidan:'',
				checkPhotos:'',
				hostUrl:app.globalData.hostUrl
			}
		},
		methods: {
			callPhone(e){
				uni.showModal({
				    title: '是否拨打电话？',
				    content: e,
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber:e
							})
				        } 
				    }
				});
			},
			cuicu(){
				this.$http.post('/MeetingMngApi/Execute?Docmd=urge', {
					MeetingCode:this.MeetingCode
				}).then(res => {
					this.$u.toast(res.ErrMsg);
				})
			},
			gotoUrl(e){
				uni.navigateTo({
					url:'../yz-comment/yz-comment?RepairCode='+this.RepairCode+'&code='+this.info.RepairNo
				})
			},
			renderAddList(){
				return new Promise((resolve)=>{
					this.$http.get('/MeetingMngApi/GetDetail?Docmd=history', {
						params:{
							MeetingCode:this.MeetingCode
						}
					}).then(res => {
						resolve(res.Data)
					})
				})		
			},
			radioChange(e){
				this.canPass=e
			},
			shi(e){
				console.log(e)
				this.ApplyRoomName=this.roomList[e[0]].label
				this.ApplyRoomCode=this.roomList[e[0]].value
			},
			selectPeople(type='',selectFzr=false,range='',limit=false){
				console.log(type)
				this.selectFzr=selectFzr
				var arr=[]
				var list=this.info.UserList
				for(let index in list){
					if(list[index].IsAccept==0){
						arr.push(list[index].ServiceUserCode)
					}
				}
				if(limit){

					uni.navigateTo({
						url:'../selectPeople/selectPeople?ProjectCode='+this.ProjectCode+'&type='+type+'&renyuan=2&range='+range+'&limit='+JSON.stringify(arr)
					})
				}
				else{
					uni.navigateTo({
						url:'../selectPeople/selectPeople?ProjectCode='+this.ProjectCode+'&type='+type+'&renyuan=2&range='+range
					})
				}
				
			},
			submit(){
				if(!this.canPass){
					return this.$u.toast('请选择预约结果');
				}
				if(this.canPass=='通过'){
					if(!this.ApplyRoomName){
						return this.$u.toast('请选择会议室');
					}
					if(!this.selectedlist1){
						return this.$u.toast('请选择服务负责人');
					}
					if(!this.selectedlist2){
						return this.$u.toast('请选择服务人员');
					}
					var codes=[]
					var selectedlist2=this.selectedlist2
					for(let index in selectedlist2){
						codes.push(selectedlist2[index].usercode)
					}
					var params={
						MeetingCode:this.MeetingCode,
						ApplyStatus:1,
						CheckRemark:'',
						RoomCode:this.ApplyRoomCode,
						DealUserCode:uni.getStorageSync('UserCode'),
						LeaderUserCode:this.selectedlist1[0].usercode,
						ServiceUserCodes:codes.toString(),
						FromDateTime:this.info.ApplyFromTime,
						EndDateTime:this.info.ApplyEndTime
					}
				}
				else{
					var params={
						MeetingCode:this.MeetingCode,
						ApplyStatus:2,
						CheckRemark:'',
						RoomCode:'',
						DealUserCode:uni.getStorageSync('UserCode'),
						LeaderUserCode:'',
						ServiceUserCodes:'',
						FromDateTime:this.info.ApplyFromTime,
						EndDateTime:this.info.ApplyEndTime
					}
				}
				var that=this
				this.$http.post('/MeetingMngApi/Execute?Docmd=assign', params).then(res => {
					this.$u.toast(res.ErrMsg);
					if(res.ErrCode==100){
						setTimeout(function(){
							that.load()
							that.selectedlist1=''
							that.selectedlist2=''
						},1000)
					}
				})
			},
			reSubmit(){
				if(!this.selectedlist2){
					return this.$u.toast('请选择服务人员');
				}
				var codes=[]
				var selectedlist2=this.selectedlist2
				for(let index in selectedlist2){
					codes.push(selectedlist2[index].usercode)
				}
				var params={
					MeetingCode:this.MeetingCode,
					ApplyStatus:1,
					CheckRemark:'',
					RoomCode:this.info.ApplyRoomCode,
					DealUserCode:uni.getStorageSync('UserCode'),
					LeaderUserCode:this.selectedlist1[0].usercode,
					ServiceUserCodes:codes.toString(),
					FromDateTime:this.info.ApplyFromTime,
					EndDateTime:this.info.ApplyEndTime
				}
				var that=this
				this.$http.post('/MeetingMngApi/Execute?Docmd=assign', params).then(res => {
					this.$u.toast(res.ErrMsg);
					if(res.ErrCode==100){
						setTimeout(function(){
							that.load()
							that.rePaidan=!that.rePaidan
							that.selectedlist1=''
							that.selectedlist2=''
						},1000)
					}
				})
			},
			load(){
				this.$http.get('/MeetingMngApi/GetDetail?Docmd=main', {
					params:{
						MeetingCode:this.MeetingCode
					}
				}).then(res => {
					var info=res.Data
					//格式化时间
					info.ApplyTime=info.ApplyTime?info.ApplyTime.replace('T',' '):''
					info.DispatchTime=info.DispatchTime?info.DispatchTime.replace('T',' '):''
					info.AcceptTime=info.AcceptTime?info.AcceptTime.replace('T',' '):''
					info.CompleteTime=info.CompleteTime?info.CompleteTime.replace('T',' '):''
					info.EvaluateTime=info.EvaluateTime?info.EvaluateTime.replace('T',' '):''
					info.CheckTime=info.CheckTime?info.CheckTime.replace('T',' '):''
					//格式化会议时间
					var ApplyFromTime=info.ApplyFromTime.split('T')
					var ApplyEndTime=info.ApplyEndTime.split('T')
					info.timeRange=ApplyFromTime[0]+' '+ApplyFromTime[1]+'至'+ApplyEndTime[1]
										
					if(res.Data.EvaluatePhotos){
						res.Data.EvaluatePhotos=res.Data.EvaluatePhotos.split(',')
						for(let index in res.Data.EvaluatePhotos){
							res.Data.EvaluatePhotos[index]=this.hostUrl+res.Data.EvaluatePhotos[index]
						}
					}
					
					var checkPhotos=[]
					if(res.Data.InspectBefore){
						res.Data.InspectBefore=res.Data.InspectBefore.split(',')
						for(let index in res.Data.InspectBefore){
							checkPhotos.push(this.hostUrl+res.Data.InspectBefore[index])
						}
					}
					if(res.Data.InspectIn){
						res.Data.InspectIn=res.Data.InspectIn.split(',')
						for(let index in res.Data.InspectIn){
							checkPhotos.push(this.hostUrl+res.Data.InspectIn[index])
						}
					}
					if(res.Data.InspectAfter){
						res.Data.InspectAfter=res.Data.InspectAfter.split(',')
						for(let index in res.Data.InspectAfter){
							checkPhotos.push(this.hostUrl+res.Data.InspectAfter[index])
						}
					}
					
					this.checkPhotos=checkPhotos
					
					this.info=info
					
					
					var UserRemark=uni.getStorageSync('UserRemark')
					if(UserRemark=='002'){
						this.isFzr=true
					}
				})
				
				//申请记录
				this.renderAddList().then(res=>{
					if(res.length>0){
						var checkHisList=[]
						for(let index in res){
							res[index].ChangeTime=res[index].ChangeTime.replace('T',' ')
							res[index].CheckTime=res[index].CheckTime.replace('T',' ')
							res[index].ApplyTime=res[index].ApplyTime.replace('T',' ')
							
							//格式化会议时间
							var ApplyFromTime=res[index].ApplyFromTime.split('T')
							var ApplyEndTime=res[index].ApplyEndTime.split('T')
							var timeRange=ApplyFromTime[0]+' '+ApplyFromTime[1]+'至'+ApplyEndTime[1]							
							res[index].timeRange=timeRange
							
							if(res[index].ApplyStatus!=0){
								checkHisList.push(res[index])
							}
						}
						this.addHisList=res
						this.checkHisList=checkHisList
					}
				})
			}
		},
		onShow() {
			uni.$on('handleFun', res => {
				if(this.selectFzr==1){
					this.selectedlist1 = res.selectedlist;
					console.log(JSON.stringify(res.selectedlist))
					if(JSON.stringify(res.selectedlist).indexOf('是')==-1){
						this.selectLeader=false
					}
					else{
						this.selectLeader=true
					}
				}
				else{				
					this.selectedlist2 = res.selectedlist;
					
				}
				// 清除监听
				uni.$off('handleFun');
			});
		},
		onLoad(e) {
			//详情
			console.log(e)
			var MeetingCode=e.code
			this.MeetingCode=MeetingCode
			this.ProjectCode=e.ProjectCode
			this.load()
			
			//会议室
			this.$http.get('/MeetingMngApi/GetAllList', {
				params:{
					ProjectCode:uni.getStorageSync('projectCode'),
					p:1,
					ps:50
				}
			}).then(res => {
				var list=[]
				for(let index in res.Data){
					var item={
						label:res.Data[index].RoomName,
						value:res.Data[index].RoomCode
					}
					list.push(item)
				}
				this.roomList=list
			})
			
		}
	}
</script>

<style>
	page{
		background: #f6f6f6;
	}
	.u-avatar__sex{
		background: #35318f;
	}
</style>
