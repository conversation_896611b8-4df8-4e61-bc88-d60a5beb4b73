<template>
	<view class="swiper-wrap border-top">
		<view class="pd">
			<u-row gutter="16">
				<u-col span="3">
					<view class="title-213">会议预约</view>
				</u-col>
				<u-col span="9">
					<u-search placeholder="请输入单号"  :show-action="true" v-model="keyword" shape="square" @custom="search"></u-search>
				</u-col>
			</u-row>
			
		</view>
		<u-tabs-swiper ref="uTabs" name="name" active-color="#35318f" :list="tabs" :current="current" @change="tabsChange" :is-scroll="false" swiperWidth="750"></u-tabs-swiper>
		<view class="swiper-box">
			<scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="onreachBottom">
				<view class="count">共{{orderCount}}条记录</view>
				<view class="list">
					<block :key="index" v-for="(item,index) in orderList">
						<view class="i flex border-all" @click="gotoUrl(item.Code)">
							<view class="flex-bd">
								<view class="t">单号：{{item.OrderNo}}</view>
								<view class="info">申请人：{{item.ApplyUserName}}</view>
								<view class="info">会议时间：{{item.BeginTime}}</view>
								<view class="info">申请时间：{{item.ApplyTime}}</view>
							</view>
						</view>
					</block>
					<view class="mr30" v-if="!empty">
						<u-loadmore :status="loadmore.status" :icon-type="loadmore.iconType" :load-text="loadmore.loadText" />
					</view>
					<u-empty class="u-text-center" v-if="empty" text="暂无内容" margin-top="300"></u-empty>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import utils from '../../components/utils.js'
	export default {
		data() {
			return {
				tabs: [{
					name:'待处理',
					status:0
				},{
					name:'已完成',
					status:1
				}],
				current: 0,
				keyword:'',
				orderList:'',
				empty:false,
				page:1,
				loadmore:{
					status: 'loading',
					iconType: 'flower',
					loadText: {
						loading: '努力加载中',
						nomore: '已无更多'
					}
				},
				orderCount:0,
				status:0,
				ProjectCode:'',
				roleGroup:''
			}
		},
		onLoad(e) {
			var roleGroup=utils.getRoleGroup(uni.getStorageSync('UserRemark'))
			var UserRemark=uni.getStorageSync('UserRemark')
			this.roleGroup=roleGroup
			var ProjectCode=e.projectCode
			if(UserRemark=='008'||UserRemark=='009'||UserRemark=='108'||UserRemark=='004'||UserRemark=='109'||UserRemark=='110'||UserRemark=='111'||UserRemark=='112'){
				var applyStatusList = '0,1'
			}
			if(UserRemark=='002'||UserRemark=='003'||UserRemark=='106'||UserRemark=='107'||UserRemark=='101'||UserRemark=='102'||UserRemark=='103'||UserRemark=='104'||UserRemark=='105'){
				var applyStatusList = '0,2,1'
			}
			
			
			this.$http.get('/ProjectMngApi/GetMeetingCount', {
				params:{
					UserCode:uni.getStorageSync('UserCode'),
					ProjectCode:ProjectCode,
					orderNo:'',
					applyStatusList:applyStatusList
				}
			}).then(res => {
				var data =res.Data
				console.log(data)
				if(UserRemark=='008'||UserRemark=='009'||UserRemark=='108'||UserRemark=='004'||UserRemark=='109'||UserRemark=='110'||UserRemark=='111'||UserRemark=='112'){
					this.tabs=[{
						name:'待反馈',
						status:0,
						count:data.Count1
					},{
						name:'已反馈',
						status:1,
						count:data.Count2
					}]
				}
				if(UserRemark=='002'||UserRemark=='003'||UserRemark=='106'||UserRemark=='107'||UserRemark=='101'||UserRemark=='102'||UserRemark=='103'||UserRemark=='104'||UserRemark=='105'){
					this.tabs=[{
						name:'待处理',
						status:0,
						count:data.Count1
					},{
						name:'进行中',
						status:2,
						count:data.Count2
					},{
						name:'已完成',
						status:1,
						count:data.Count3
					}]
				}
				
				var tabs=this.tabs
				this.ProjectCode=ProjectCode
				this.renderList(1,this.status).then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.getNum()
			})
				
		},
		methods: {
			gotoUrl(e){
				var UserRemark=uni.getStorageSync('UserRemark')
				var roleGroup=utils.getRoleGroup(uni.getStorageSync('UserRemark'))
				if(UserRemark=='002'||UserRemark=='003'||UserRemark=='106'||UserRemark=='107'||roleGroup=='admin'){
					var url='../fzr-meetingDetail/fzr-meetingDetail?code='+e+'&ProjectCode='+this.ProjectCode
				}
				else{
					var url='../yg-meetingDetail/yg-meetingDetail?code='+e+'&ProjectCode='+this.ProjectCode
				}
				console.log(url)
				uni.navigateTo({
					url:url
				})
			},
			tabsChange(item) {
				if(this.current == item.index){
					return
				}
				this.current = item.index;
				this.status=item.item.status
				this.loadmore.status='loading'
				this.renderList(1,item.item.status).then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.getNum()
				this.page=1
			},
			onreachBottom() {
				if(this.loadmore.status=='nomore'){
					return
				}
				var orderList=this.orderList
				this.renderList(this.page+1,this.status).then(res=>{
					for(let index in res){
						orderList.push(res[index])
					}
					if(res.length>0){
						this.orderList=orderList
						this.page++
					}
					else{
						this.loadmore.status='nomore'
					}
				})
			},
			search(value) {
				this.renderList(1,this.status).then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.getNum()
				this.page=1
			},
			
			//获取数量
			getNum(){				
				this.$http.get('/UserApi/GetVariousNum', {
					params:{
						UserCode:uni.getStorageSync('UserCode'),
						ProjectCode:this.ProjectCode,
						ApplyStatus:this.status,
						OrderNo:this.keyword
					}
				}).then(res => {
					this.orderCount=res.Data.meetNum
				})
				
			},
			//获取列表
			renderList(page,status=''){
				var type=''
				var UserRemark=uni.getStorageSync('UserRemark')
				return new Promise((resolve)=>{
					if((this.roleGroup=='fzr'&&UserRemark!='004')||this.roleGroup=='admin'){
						var url='/MeetingMngApi/GetMeetingList'
					}
					else{
						if(UserRemark=='004'||UserRemark=='009'||UserRemark=='108'||UserRemark=='109'||UserRemark=='110'||UserRemark=='111'||UserRemark=='112'){
							
							if(UserRemark==108||UserRemark==109){
								type=3
							}
							else if(UserRemark==112){
								type=1
							}
							else if(UserRemark==110||UserRemark==111){
								type=4
							}
							else{
								type=2
							}
							var url='/MeetingMngApi/GetMeetingListByType'
						}
						else{
							var url='/MeetingMngApi/GetMyMeetingList'
						}
						
					}
					
					this.$http.get(url, {
						params:{
							p:page,
							ps:10,
							projectCode:this.ProjectCode,
							OrderNo:this.keyword,
							ApplyStatus:status,
							UserCode:uni.getStorageSync('UserCode'),
							type:type
						}
					}).then(res => {
						if(page==1&res.Data.length==0){
							this.empty=true
						}
						else{
							this.empty=false
							for(let index in res.Data){
								res.Data[index].ApplyTime=res.Data[index].ApplyTime.replace(/\//g,'-')
								res.Data[index].ApplyTime=res.Data[index].ApplyTime.replace('T',' ')
							}
						}
						var list=[]
						resolve(res.Data)
					})
				})		
			}
		}
	}
</script>

<style>
	@import url("style.css");
	.title-213{
		font-size: 36rpx;
	}
</style>
