<template>
	<view>
		<view class="project-list">
			<view class="i flex border-all" v-for="(item,index) in projectList" :key="index">
				<view class="flex-hd">
					<image :src="item.MainImage" mode="aspectFill"></image>
				</view>
				<view class="flex-bd">
					<view class="name">{{item.ProjectName}}</view>
					<view class="info">地址：{{item.Address}}</view>
					<view class="info">项目负责人：{{item.ProjectLeaderUserName}}</view>
					<view class="count" v-if="roleGroup=='fzr'">
						<view class="f inline">
							待处理工单：<view class="inline">{{item.waitdealnum}}</view>
						</view>
						<view class="f inline">
							待处理预约：<view class="inline">{{item.waitdealreservenum}}</view>
						</view><!-- 
						<view class="f inline">
							巡检超期：<view class="inline">15</view>
						</view> -->
					</view>
					<view class="count" v-else-if="roleGroup=='fw'">
						<view class="f inline">
							待接单：<view class="inline">{{item.waitdealnum}}</view>
						</view>
						<view class="f inline">
							进行中：<view class="inline">{{item.inhandnum}}</view>
						</view>
					</view>
					<view class="count" v-else>
						
					</view>
				</view>
			</view>
		</view>
		<u-empty v-if="projectList.length==0" class="u-text-center" text="暂无内容" margin-top="150"></u-empty>
	</view>
</template>

<script>
	const app = getApp();
	import utils from '../../components/utils.js'
	export default {
		data() {
			return {
				projectList:[],
				hostUrl:app.globalData.hostUrl,
				roleGroup:''
			}
		},
		methods: {
			
		},
		onLoad() {
			var url
			var roleGroup=utils.getRoleGroup(uni.getStorageSync('UserRemark'))
			console.log(roleGroup)
			this.roleGroup=roleGroup
			if(roleGroup=='fzr'||roleGroup=='admin'){
				url='/HomepageMng/GetProjectMain'
			}
			else if(roleGroup=='fw'){
				url='/HomepageMng/GetMyProject'
			}
			else{
				url='/HomepageMng/GetProjectByChargeUser'
			}
			this.$http.get(url, {
				params:{
					UserCode:uni.getStorageSync('UserCode')
				}
			}).then(res => {
				var list=JSON.parse(res.Data)
				for(let index in list){
					if(list[index].MainImage){
						list[index].MainImage=this.hostUrl+list[index].MainImage
					}
					else{
						list[index].MainImage='http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/projectimg01.png'
					}
				}
				console.log(list)
				this.projectList=list
			})
		}
	}
</script>

<style>

</style>
