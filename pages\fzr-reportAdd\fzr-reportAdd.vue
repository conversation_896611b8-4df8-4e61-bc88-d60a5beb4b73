<template>
	<view class="border-top">
		<view class="report-top flex">
			<view class="tab flex-bd">
				<view class="i active">我的日报</view>
			</view>
			<view class="flex-hd more-icon">
				<i class="custom-icon custom-icon-liebiao"></i>
			</view>
		</view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">填写日报</view>
				</view>
			</view>
			<view slot="body">
				<u-form :model="form" :rules="rules" ref="uForm" :errorType="errorType">
					<u-form-item label="填报日期" label-width="140rpx" :border-bottom="flase" >
						<u-input v-model="form.DailyTime" type="date" disabled />
					</u-form-item>
					<u-form-item label="日报内容" label-width="140rpx" :border-bottom="flase" prop="Content">
						<u-input :height="400" v-model="form.Content" type="textarea" :border="true" :maxlength="20000" />
					</u-form-item>
					<u-form-item label="上传图片" label-width="140rpx" :border-bottom="flase">
						<u-upload ref="uUpload1" :action="action" :header="uploadHeader" @on-success="uploadOk" :file-list="fileList" ></u-upload>
					</u-form-item>		
				</u-form>
			</view>
		</u-card>
		<view class="mr30">
			<view class="mr30"><u-button type="primary" @click="submit">确认提交</u-button></view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			
			return {
				uploadHeader:{
					Authorization: 'Bearer '+ uni.getStorageSync('access_token')
				},
				action:app.globalData.uploadUrl,
				UserType:'',
				form:{
					ProjectCode:'',
					DailyUserCode:'',
					DailyTime:'',
					Images:'',
					Content:'',
				},
				errorType: ['message'],	
				rules: {
					Content: [
						{
							required: true,
							message: '请输入日报内容',
							trigger: ['change', 'blur']
						}
					]
				},							
				fileList:[],
				photoList:[]
			}
		},
		onLoad() {
			this.action=this.uploadUrl
			this.form.UserName = uni.getStorageSync('UserName')
			this.form.ProjectCode = uni.getStorageSync('projectCode')
			this.form.DailyUserCode = uni.getStorageSync('UserCode')			
			this.form.Images=''
			this.UserType=uni.getStorageSync('UserType')
			var aData = new Date();		    
			this.form.DailyTime =aData.getFullYear() + "/" + (aData.getMonth() + 1) + "/" + aData.getDate() ;
			   
			
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		methods: {
			submit(){
				var photoList1=[]
				var files1 = this.$refs.uUpload1.lists;
				for(let index in files1){
					photoList1.push(files1[index].response.RetValue)
				}				
				this.form.Images=photoList1.toString();
				var aData = new Date();	
				this.form.DailyTime =aData.getFullYear() + "/" + (aData.getMonth() + 1) + "/" + aData.getDate() +" "+aData.getHours()+":"+aData.getMinutes()+":"+aData.getSeconds();
				this.$refs.uForm.validate(valid => {
					if (valid) {
						this.$http.post('/DailyMngApi/Execute?Docmd=add', this.form).then(res => {
							this.$u.toast(res.ErrMsg);
							if(res.ErrCode==100){
								setTimeout(function(){
										uni.redirectTo({
											url:'/pages/fzr-reportListMy/fzr-reportListMy'
										})
									
									
								},1000)
							}
						})
					}
				})
			},
			uploadOk(data,index,lists){
				console.log(data)
				this.photoList.push(data.RetValue)
			}
			 
			
		}
	}
</script>

<style>
	@import url("../fzr-reportList/style.css");	
</style>
