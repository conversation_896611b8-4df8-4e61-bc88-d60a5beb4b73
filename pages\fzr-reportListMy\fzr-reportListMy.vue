<template>
	<view class="border-top swiper-wrap ">
		<view class="report-top">
			<view class="tab">
				<view v-if="fzr" class="i" @click="gotoUrl('/pages/fzr-reportList/fzr-reportList?projectCode='+ProjectCode)">工作日报</view>
				<view class="i active" >我的日报</view>
			</view>
		</view>
		<view class="swiper-box">
			<scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="onreachBottom">
				<view class="list">
					<u-card v-for="(item, index) in List" :key="index" :foot-border-top="false" padding="20" class="card-readyonly">
						<view slot="head">
							<view class="u-flex u-col-top u-row-between">
								<view class="u-flex-nowrap u-item-title u-font-28">{{ item.DailyUserName }}</view>
								<view class="time u-font-12">{{ item.DailyTime }}</view>
							</view>
						</view>
						<view slot="body">
							<view class="u-text">{{ item.DailyContent }}</view>
							<view class="detail-img-list">
								<image :src="item1" v-for="(item1,index1) in item.Images" :key="index1" @click="previewImage(index1,item.Images)"></image>
							</view>
						</view>
					</u-card>
					<view class="mr30" v-if="!empty"><u-loadmore :status="loadmore.status" :icon-type="loadmore.iconType" :load-text="loadmore.loadText" /></view>
					<u-empty class="u-text-center" v-if="empty" text="暂无内容" margin-top="300"></u-empty>
				</view>
			</scroll-view>
		</view>

		<view class="fix-add"><i class="custom-icon custom-icon-jia" @click="gotoUrl('/pages/fzr-reportAdd/fzr-reportAdd')"></i></view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			ProjectCode:'',
			fzr:false,			
			empty: false,
			page: 1,
			List: [],
			loadmore: {
				status: 'loading',
				iconType: 'flower',
				loadText: {
					loading: '努力加载中',
					nomore: '已无更多'
				}
			}
		};
	},
	onLoad(e) {
		this.ProjectCode=e.ProjectCode
		console.log(this.ProjectCode)
		this.renderList(1).then(res => {
			this.List = res;
			if (res.length < 10) {
				this.loadmore.status = 'nomore';
			}
		});
		var UserRemark = uni.getStorageSync('UserRemark')
		if(UserRemark == "002" || UserRemark == "003" || UserRemark == "004"){
			this.fzr=true				
		}
		
		
	},
	methods: {
		gotoUrl(e) {
			uni.navigateTo({
				url: e
			});
		},
		renderList(page) {
			return new Promise(resolve => {
				this.$http
					.post('/DailyMngApi/GetList?p=' + page + '&ps=10', {
						DailyUserCode: uni.getStorageSync('UserCode'),
						ProjectCode:this.ProjectCode,
						StartTime: '',
						EndTime: ''
					})
					.then(res => {
						if ((page == 1) & (res.Data.length == 0)) {
							this.empty = true;
						} else {
							this.empty = false;
							for (let index in res.Data) {
								res.Data[index].DailyTime = res.Data[index].DailyTime.split('T')[0].replace(/\-/g,"/");
								if (res.Data[index].Images) {
									res.Data[index].Images = res.Data[index].Images.split(',');
									for (var j = 0; j < res.Data[index].Images.length; j++) {
										res.Data[index].Images[j] = getApp().globalData.hostUrl + res.Data[index].Images[j];
									}
								}
								//console.log(res.Data[index].Images)
							}
						}
						var list = [];
						resolve(res.Data);
				});
			});
		},
		onreachBottom() {
			if (this.loadmore.status == 'nomore') {
				return;
			}
			var List = this.List;
			this.renderList(this.page + 1, this.type).then(res => {
				for (let index in res) {
					orderList.push(res[index]);
				}
				if (res.length > 0) {
					this.List = List;
					this.page++;
				} else {
					this.loadmore.status = 'nomore';
				}
			});
		},
		previewImage(index1, Images) {
			let photoList = Images.map(item1 => {
				let newImg =  item1;
				return newImg;
			});
			uni.previewImage({
				current: index1,
				urls: photoList
			});
		}
	}
};
</script>

<style>
@import url('../fzr-reportList/style.css');
</style>
