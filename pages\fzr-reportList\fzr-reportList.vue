<template>
	<view class="border-top swiper-wrap">
		<view class="report-top">
			<view class="tab">
				<view class="i active">工作日报</view>
				<view v-if="roleGroup!='admin'" class="i" @click="gotoUrl('/pages/fzr-reportListMy/fzr-reportListMy?ProjectCode='+ProjectCode)">我的日报</view>
			</view>
			<view class="select flex">
				<view class="flex-bd">
					<u-form-item label="选择日期" label-width="150rpx" :border-bottom="flase">
						<u-input height="30" type="date" v-model="sea.startDate+'-'+sea.endDate" :border="true" @click="calendar" placeholder="请选择日期" />						
					</u-form-item>
					<u-form-item label="选择员工" label-width="150rpx" :border-bottom="flase">
						<u-input height="30" type="select" v-model="sea.UserName" :border="true" @click="pselect" placeholder="请选择人员" />
					</u-form-item>
					
				</view>
				<view class="flex-hd"><view class="btn" @click="search">立即查询</view></view>
			</view>
		</view>
		<view class="swiper-box">
			<scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="onreachBottom">
				<view class="list">
					<u-card v-for="(item, index) in List" :key="index" :foot-border-top="false" padding="20" class="card-readyonly">
						<view slot="head">
							<view class="u-flex u-col-top u-row-between">
								<view class="u-flex-nowrap u-item-title u-font-28">{{ item.DailyUserName }}</view>
								<view class="time u-font-12">{{ item.DailyTime }}</view>
							</view>
						</view>
						<view slot="body">
							<view class="u-text">{{ item.DailyContent }}</view>
							<view class="detail-img-list">
								<image :src="item1" v-for="(item1, index1) in item.Images" :key="index1" @click="previewImage(index1, item.Images)"></image>
							</view>
						</view>
					</u-card>
					<view class="mr30" v-if="!empty"><u-loadmore :status="loadmore.status" :icon-type="loadmore.iconType" :load-text="loadmore.loadText" /></view>
					<u-empty class="u-text-center" v-if="empty" text="暂无内容" margin-top="300"></u-empty>
				</view>
			</scroll-view>
		</view>
		<u-calendar v-model="sea.show" @change="calendarchange" mode="range" btn-type="primary" range-bg-color="#e4e3ff" :active-bg-color="ThemeColor" :range-color="ThemeColor"></u-calendar>
		<u-select  v-model="pshow" :list="plist" @confirm="confirm" :confirm-color="ThemeColor"></u-select>
	</view>
</template>

<script>
	import utils from '../../components/utils.js'
export default {
	data() {
		return {
			ProjectCode:'',
			ThemeColor: '',
			empty: false,
			page: 1,
			pshow:false,
			List: [],
			plist:[],
			sea: {
				show: false,
				startDate: '',
				endDate: '',
				UserName:'',
				UserCode:''
				
			},
			loadmore: {
				status: 'loading',
				iconType: 'flower',
				loadText: {
					loading: '努力加载中',
					nomore: '已无更多'
				}
			},
			mode: 'range',
			roleGroup:''
		};
	},
	onLoad(e) {
		//主题色
		this.ThemeColor = getApp().globalData.ThemeColor;
		this.ProjectCode=e.projectCode;
		this.renderList(1).then(res => {
			this.List = res;
			if (res.length < 10) {
				this.loadmore.status = 'nomore';
			}
		});
		this.$http
		.get('/DailyMngApi/GetUserInfo', {
			params: {projectCode:e.projectCode,userCode:uni.getStorageSync('UserCode')}, 
			})
			.then(res => {	
				var plist=[];
				res.Data.forEach(function(item,index){
					var value=item.UserCode,
						label=item.UserName;
						plist.push({value:value,label:label})				
				})
				this.plist=plist;
		})
		
		var UserRemark=uni.getStorageSync('UserRemark')
		var roleGroup=utils.getRoleGroup(UserRemark)
		this.roleGroup=roleGroup
		
	},
	methods: {		
		gotoUrl(e) {
			uni.navigateTo({
				url: e
			});
		},
		renderList(page,StartTime='',EndTime='',DailyUserCode='') {
			return new Promise(resolve => {
				this.$http
					.post('/DailyMngApi/GetList?p=' + page + '&ps=10', {
						DailyUserCode:DailyUserCode,
						ProjectCode:this.ProjectCode,
						StartTime: StartTime,
						EndTime: EndTime,
						UserCode:uni.getStorageSync('UserCode')
					})
					.then(res => {
						if ((page == 1) & (res.Data.length == 0)) {
							this.empty = true;
						} else {
							this.empty = false;
							for (let index in res.Data) {
								res.Data[index].DailyTime =res.Data[index].DailyTime.split('T')[0].replace(/\-/g,"/");
								if (res.Data[index].Images) {
									res.Data[index].Images = res.Data[index].Images.split(',');
									for (var j = 0; j < res.Data[index].Images.length; j++) {
										res.Data[index].Images[j] = getApp().globalData.hostUrl + res.Data[index].Images[j];
									}
								}
								//console.log(res.Data[index].Images)
							}
						}
						var list = [];
						resolve(res.Data);
					});
			});
		},
			onreachBottom() {
			if (this.loadmore.status == 'nomore') {
				return;
			}
			var orderList = this.orderList;
			this.renderList(this.page + 1, this.type).then(res => {
				for (let index in res) {
					orderList.push(res[index]);
				}
				if (res.length > 0) {
					this.orderList = orderList;
					this.page++;
				} else {
					this.loadmore.status = 'nomore';
				}
			});
		},
		previewImage(index1, Images) {
			let photoList = Images.map(item1 => {
				let newImg = item1;
				return newImg;
			});
			uni.previewImage({
				current: index1,
				urls: photoList
			});
		},
		calendar(){
			this.sea.show = true;
			
		},
		calendarchange(e){
			this.sea.startDate=e.startDate.replace(/\-/g,"/");
			this.sea.endDate=e.endDate.replace(/\-/g,"/");
			
		},
		pselect(){
			this.pshow=true
		},
		confirm(e) {
			//console.log(e)
			this.sea.UserName=e[0].label
			this.sea.UserCode=e[0].value
			//console.log(this.sea.UserName)
		},
		
			search() {
				this.renderList(1,this.sea.startDate,this.sea.endDate,this.sea.UserCode).then(res=>{
					//console.log(res)				
					this.List=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.page=1
			}
			
		
		
	}
};
</script>

<style>
@import url('style.css');
</style>
