.report-top{
	background: #fff;
}
.report-top .tab{
	padding: 20rpx 0;
}
.report-top .tab .i{
	display: inline-block;
	position: relative;
	font-size: 32rpx;
	padding: 0 30rpx;
}
.report-top .tab .i:before{
	content: "";
	width: 1px;
	height: 100%;
	position: absolute;
	border-left: 1px solid #ddd;
	top: 0;
	left: 0;
}
.report-top .tab .i.active{
	color: #000;
font-weight: bold;
}
.report-top .select{
	padding:0 30rpx 20rpx;
}
.report-top .u-form-item{
	padding: 10rpx 0 !important;
	line-height: 40rpx !important;
}
.report-top .btn{
	background: #35318f;
	margin-left: 30rpx;
	color: #fff;
	width: 100rpx;
	padding: 20rpx;
	border-radius: 4px;
}
.report-top  .more-icon{
	padding-right: 30rpx;
}
.u-text{
	padding: 20rpx 0;
}
.u-text+.u-text{
	padding-top: 0;
}
.time{
	color: #999;
}
.time u-button{
	margin-left: 20rpx;
}
.fix-add{
	position: fixed;
	right: 30rpx;
	bottom: 50rpx;
	width: 100rpx;
	height: 100rpx;
	line-height: 100rpx;
	text-align: center;
	border-radius: 50%;
	box-shadow: 1px 1px 10px rgba(0,0,0,0.1);
	background: #35318f;
}
.fix-add i{
	color: #fff;
	font-size: 48rpx;
}