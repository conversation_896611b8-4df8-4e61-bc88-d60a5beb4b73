<template>
	<view class="border-top">
		<view class="detail-top flex">
			<view class="flex-bd">
				工单号：{{info.RepairNo}}
			</view>
		</view>
		<view class="detail-finish">
			<view class="q">
				<view class="q-t">{{info.repairStatus}}</view>
			</view>
		</view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">报修信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="报修人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.applyusername}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="报修时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ApplyTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修处室" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.Department}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修地点" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.Address}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="报修内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.RepairMatter}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.Remark}}</text>
					</u-cell-item>
					
				</u-cell-group>
				<view class="detail-img-list">
					<image v-for="(img,index) in info.Photos" :key="index" :src="img" @click="lookImg(index,info.Photos)"></image>
				</view>
				<view class="detail-phone" @click="callPhone(info.CellPhone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		
		
		<block v-if="info.repairStatus=='待指派'">
			<u-card :foot-border-top="false" padding="20" class="card-readyonly">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">派单信息</view>
					</view>
				</view>
				<view slot="body">
					<u-form :model="form" ref="uForm">
						<u-form-item label="维修类型" label-width="150rpx" :border-bottom="flase">
							<u-input v-model="RepairTypeName"  type="select" :border="true" @click="typeShow = true" />
						</u-form-item>
						<u-form-item label="维修人员" label-width="150rpx" :border-bottom="flase">					
							<view slot="right">
								<u-button type="primary" size="mini" @click="selectPeople('')">选择</u-button>
							</view>
						</u-form-item>	
					</u-form>
					<view class="work" v-if="selectedlist">
						<u-avatar :src="selectedlist.HeadImg"></u-avatar>
						<view class="w-no" v-if="selectedlist.jobnumber">{{selectedlist.jobnumber}}</view>
						<view class="w-name">{{selectedlist.name}}</view>
					</view>
				</view>
			</u-card>
			<u-picker mode="selector" range-key="label" v-model="typeShow" :default-selector="[0]" :range="typeList" @confirm="typeOk"></u-picker>
			<view class="mr30" v-if="info.repairStatus=='待指派'&&roleGroup!='admin'">
				<u-button type="primary" @click="paidan">确认派单</u-button>
			</view>
		</block>
		

		
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus=='待接单'||info.repairStatus=='已接单'||info.repairStatus=='已完成'||info.repairStatus=='已评价'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">派单信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="派单时间" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.dispatchTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="接单时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.AcceptTime!='0001-01-01 00:00:00'">{{info.AcceptTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="派单人" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.DispatchUserName}}</text>
					</u-cell-item>						
				</u-cell-group>
				<view class="work">
					<u-avatar src="http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png"></u-avatar>
					<view class="w-no"  v-if="selectedlist.repairJobNumber">{{info.repairJobNumber}}</view>
					<view class="w-name">{{info.repairusername}}</view>
				</view>
				<view class="detail-phone" @click="callPhone(info.dispatchuserphone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		
		<block v-if="rePaidan2&&roleGroup!='admin'">
			<u-card :foot-border-top="false" padding="20" class="card-readyonly">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">重新指派</view>
					</view>
				</view>
				<view slot="body">
					<u-form :model="form" ref="uForm">
						<u-form-item label="维修类型" label-width="150rpx" :border-bottom="flase">
							<u-input v-model="RepairTypeName"  type="select" :border="true" @click="typeShow = true" />
						</u-form-item>
						<u-form-item label="维修人员" label-width="150rpx" :border-bottom="flase">					
							<view slot="right">
								<u-button type="primary" size="mini" @click="selectPeople('')">选择</u-button>
							</view>
						</u-form-item>	
					</u-form>
					<view class="work" v-if="selectedlist">
						<u-avatar :src="selectedlist.HeadImg"></u-avatar>
						<view class="w-no" v-if="selectedlist.jobnumber">{{selectedlist.jobnumber}}</view>
						<view class="w-name">{{selectedlist.name}}</view>
					</view>
				</view>
			</u-card>
			<u-picker mode="selector" range-key="label" v-model="typeShow" :default-selector="[0]" :range="typeList" @confirm="typeOk"></u-picker>
		</block>
		
		
		<view class="mr30" v-if="info.repairStatus=='待接单'&&roleGroup!='admin'">
			<u-row gutter="16" v-if="rePaidan2">
				<u-col span="6">
					<u-button type="default" @click="rePaidan2=!rePaidan2">取消指派</u-button>
				</u-col>
				<u-col span="6">
					<u-button type="primary" @click="paidan">确认指派</u-button>
				</u-col>
			</u-row>
			<u-row gutter="16" v-else>
				<u-col span="6">
					<u-button type="error" @click="cuicu">催促接单</u-button>
				</u-col>
				<u-col span="6">
					<u-button type="primary" @click="rePaidan2=!rePaidan2">重新指派</u-button>
				</u-col>
			</u-row>		
		</view>
		
		<block v-if="info.RepairUserCode==UserCode">
			<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus=='已接单'">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">维修信息</view>
					</view>
				</view>
				<view slot="body">
					<u-form :model="form" ref="uForm">
						<u-form-item label="维修服务单" label-width="150rpx" :border-bottom="flase">
							<u-upload ref="uUpload1" :action="action" :header="uploadHeader" @on-remove="remove1" @on-success="uploadOk1" :file-list="fileList1" ></u-upload>
						</u-form-item>	
						
						<u-form-item label="备注内容" label-width="150rpx" :border-bottom="flase">
							<u-input v-model="RepairContent" type="textarea" :border="true" />
						</u-form-item>
						<u-form-item label="现场图片" label-width="150rpx" :border-bottom="flase">
							<u-upload ref="uUpload2" :action="action" :header="uploadHeader" @on-remove="remove2" @on-success="uploadOk2" :file-list="fileList2" ></u-upload>
						</u-form-item>
					</u-form>
				</view>
			</u-card>
			<view class="mr30" v-if="info.repairStatus=='已接单'">
				<u-button type="primary" @click="submit">确认完成</u-button>
			</view>
		</block>
		
		<block v-if="info.repairStatus=='已完成'&&info.RepairSituations.length>0">
			<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus=='已完成'||info.repairStatus=='已评价'">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">检修情况</view>
					</view>
				</view>
				<view slot="body" v-for="(item,index) in info.RepairSituations">
					<view class="tit bor-b flex">
						<view class="flex-bd">用料{{index+1}}</view>
					</view>
					<u-cell-group :border="flase">
						<u-cell-item class="cell-item-reset" title="用料名称" :border-bottom="flase" :arrow="flase" title-width="150">
							<text>{{item.MaterialName}}</text>
						</u-cell-item>
						<u-cell-item  class="cell-item-reset" title="数量" :border-bottom="flase" :arrow="flase"  title-width="150" >
							<text>{{item.MaterialNumber}}</text>
						</u-cell-item>
						<u-cell-item  class="cell-item-reset" title="价格" :border-bottom="flase" :arrow="flase"  title-width="150" >
							<text>{{item.MaterialPrice}}</text>
						</u-cell-item>
					</u-cell-group>
				</view>
			</u-card>
		</block>
		
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus=='已完成'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">维修信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="维修人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.repairusername}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.CompleteTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{!info.RepairContent?'':info.RepairContent}}</text>
					</u-cell-item>
					
				</u-cell-group>
				<view class="detail-img-list">
					<image v-for="(img,index) in info.RepairPhotos" :key="index" :src="img" @click="lookImg(index,info.RepairPhotos)"></image>
				</view>
			</view>
		</u-card>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus=='已评价'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">评价信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="评价人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.applyusername}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修质量" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.RepairQuality}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="服务质量" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ServiceQuality}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价星级" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<u-rate :disabled="true" v-model="info.EvaluateLevel" :count="5" active-color="#eca805"></u-rate>
					</u-cell-item>
					<u-cell-item v-if="info.EvaluateContent" class="cell-item-reset" title="建议和要求" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateContent}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.EvaluatePhotos" :key="index" :src="img" @click="lookImg(index,info.EvaluatePhotos)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		<view style="height: 1px;"></view>
	</view>
</template>

<script>
	const app = getApp();
	import utils from '../../components/utils.js'
	export default {
		data() {
			return {
				info:{
					RepairNo:'',
					repairStatus:'',
					applyusername:'',
					ApplyTime:'',
					Remark:'',
					Photos:'',
					dispatchtime:'',
					AcceptTime:'',
					DispatchUserJobNumber:'',
					DispatchUserName:'',
					dispatchuserphone:'',
					repairusername:'',
					completetime:'',
					RepairContent:'',
					RepairPhotos:'',
					applyusername:'',
					EvaluateTime:'',
					EvaluateLevel:'',
					EvaluateContent:'',
					Department:'',
					Address:'',
					ServiceList:''
				},
				typeShow:'',
				typeList:'',
				RepairCode:"",
				RepairTypeName:'',
				RepairTypeCode:'',
				AssignUserCode:'',
				ProjectCode:'',
				hostUrl:app.globalData.hostUrl,
				selectedlist:'',
				isFzr:'',
				rePaidan1:'',
				rePaidan2:'',
				roleGroup:'',
				uploadHeader:{
					Authorization: 'Bearer '+ uni.getStorageSync('access_token')
				},
				action:app.globalData.uploadUrl,
				ServiceList:[],
				RepairPhotos:[],
				RepairContent:'',
				UserCode:''
			}
		},
		onLoad(e) {
			this.UserCode = uni.getStorageSync('UserCode')
			console.log(e)
			var roleGroup=utils.getRoleGroup(uni.getStorageSync('UserRemark'))
			this.roleGroup=roleGroup
			var RepairCode=e.code
			this.RepairCode=RepairCode
			this.ProjectCode=e.ProjectCode
			
			
			this.getType()
			this.load()
			
			var UserRemark=uni.getStorageSync('UserRemark')
			if(UserRemark=='002'||UserRemark=='004'||UserRemark=='106'||UserRemark=='107'){
				this.isFzr=true
			}
			
		},
		onShow() {
			uni.$on('handleFun', res => {
				console.log('人员',res)
				this.selectedlist = res.selectedlist[0];
				// 清除监听
				uni.$off('handleFun');
			});
		},
		methods: {
			load(){
				this.$http.get('/RepairMngApi/GetDetail', {
					params:{
						RepairCode:this.RepairCode
					}
				}).then(res => {
					
					res.Data.ApplyTime=res.Data.ApplyTime?res.Data.ApplyTime.replace('T',' '):''
					res.Data.DispatchTime=res.Data.DispatchTime?res.Data.DispatchTime.replace('T',' '):''
					res.Data.AcceptTime=res.Data.AcceptTime?res.Data.AcceptTime.substring(0,19).replace('T',' '):''
					res.Data.CompleteTime=res.Data.ApplyTime?res.Data.CompleteTime.replace('T',' '):''
					res.Data.EvaluateTime=res.Data.EvaluateTime?res.Data.EvaluateTime.replace('T',' '):''
					if(res.Data.Photos){
						res.Data.Photos=res.Data.Photos.split(',')
						for(let index in res.Data.Photos){
							res.Data.Photos[index]=this.hostUrl+res.Data.Photos[index]
						}
					}
					if(res.Data.RepairPhotos){
						res.Data.RepairPhotos=res.Data.RepairPhotos.split(',')
						for(let index in res.Data.RepairPhotos){
							res.Data.RepairPhotos[index]=this.hostUrl+res.Data.RepairPhotos[index]
						}
					}
					if(res.Data.ServiceList){
						res.Data.ServiceList=res.Data.ServiceList.split(',')
						for(let index in res.Data.ServiceList){
							res.Data.ServiceList[index]=this.hostUrl+res.Data.ServiceList[index]
						}
					}
					//合并图片
					if(res.Data.RepairPhotos&&res.Data.ServiceList){
						res.Data.RepairPhotos=res.Data.RepairPhotos.concat(res.Data.ServiceList)
					}
					
					if(res.Data.EvaluatePhotos){
						res.Data.EvaluatePhotos=res.Data.EvaluatePhotos.split(',')
						for(let index in res.Data.EvaluatePhotos){
							res.Data.EvaluatePhotos[index]=this.hostUrl+res.Data.EvaluatePhotos[index]
						}
					}
					
					this.info=res.Data
				})
			},
			lookImg(index,imgList){
				uni.previewImage({
					current:index,
					urls: imgList
				})
			},
			callPhone(e){
				uni.showModal({
				    title: '是否拨打电话？',
				    content: e,
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber:e
							})
				        } 
				    }
				});
			},
			typeOk(e){
				this.RepairTypeName=this.typeList[e[0]].label
				this.RepairTypeCode=this.typeList[e[0]].value
			},
			paidan(){
				var that=this
				if(!that.RepairTypeCode){
					return that.$u.toast('请选择维修类型');
				}
				if(!that.selectedlist.usercode){
					return that.$u.toast('请选择维修人员');
				}
				uni.showModal({
				    title: '提示',
				    content: '确认派单给'+this.selectedlist.name,
					confirmText:'确定',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							that.$http.post('/RepairMngApi/Execute?Docmd=assign', {
								RepairCode:that.RepairCode,
								RepairType:that.RepairTypeCode,
								DealUserCode:uni.getStorageSync('UserCode'),
								AssignUserCode:that.selectedlist.usercode,
								AssignType:1
							}).then(res => {
								that.$u.toast(res.ErrMsg);
								if(res.ErrCode==100){
									setTimeout(function(){
										uni.navigateBack({
											delta: 1
										});
									},1000)
								}
							})
				        } 
				    }
				});
				
			},
			getType(){
				this.$http.get('/RepairMngApi/GetRepairType', {
					params:{}
				}).then(res => {
					var list=[]
					for(let index in res.Data){
						var item={
							label:res.Data[index].Name,
							value:res.Data[index].Code
						}
						list.push(item)
					}
					this.typeList=list
				})
			},
			cuicu(){
				this.$http.post('/RepairMngApi/Execute?Docmd=urge', {
					RepairCode:this.RepairCode
				}).then(res => {
					this.$u.toast(res.ErrMsg);
				})
			},
			selectPeople(e){
				uni.navigateTo({
					url:'../selectPeople/selectPeople?ProjectCode='+this.ProjectCode+'&renyuan=1&range='+e
				})
			},
			submit(){
				var that=this
				
				var photoList1=[]
				var files1 = this.$refs.uUpload1.lists;
				for(let index in files1){
					photoList1.push(files1[index].response.RetValue)
				}
				var photoList2=[]
				var files2 = this.$refs.uUpload2.lists;
				for(let index in files2){
					photoList2.push(files2[index].response.RetValue)
				}
				if(!photoList1[0]){
					return that.$u.toast('上传维修单！');
				}
				if(!photoList2[0]){
					return that.$u.toast('上传现场图片！');
				}
				
				uni.showModal({
				    title: '提示',
				    content: '确认完成？',
					confirmText:'确定',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							that.$http.post('/RepairMngApi/Execute?Docmd=complete', {
								RepairCode:that.RepairCode,
								ServiceList:photoList1.toString(),
								RepairPhotos:photoList2.toString(),
								RepairContent:that.RepairContent
							}).then(res => {
								that.$u.toast(res.ErrMsg);
								if(res.ErrCode==100){
									setTimeout(function(){
										that.load()
									},1000)
								}
							})
				        } 
				    }
				});				
			},
			uploadOk1(data,index,lists){
				console.log(data)
				this.ServiceList.push(data.RetValue)
			},
			uploadOk2(data,index,lists){
				console.log(data)
				this.RepairPhotos.push(data.RetValue)
			},
			remove1(index){
				console.log(index)
				this.ServiceList.splice(index,1)
			},
			remove2(index){
				console.log(index)
				this.RepairPhotos.splice(index,1)
			},
		}
		
	}
</script>

<style>
	page{
		background: #f6f6f6;
	}
</style>
