<template>
	<view class="swiper-wrap border-top">
		<view class="pd">
			<u-row gutter="16">
				<u-col span="3">
					<view class="title-213">工单中心</view>
				</u-col>
				<u-col span="9">
					<u-search placeholder="请输入单号"  :show-action="true" v-model="keyword" shape="square" @custom="search"></u-search>
				</u-col>
			</u-row>
			
		</view>
		<u-tabs-swiper ref="uTabs" name="name" active-color="#35318f" :list="tabs" :current="current" @change="tabsChange" :is-scroll="false" swiperWidth="750"></u-tabs-swiper>
		<view class="swiper-box">
			<scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="onreachBottom">
				<view class="count">共{{orderCount}}条记录</view>
				<view class="list">
					<block :key="index" v-for="(item,index) in orderList">
						<navigator class="i flex border-all" :url="roleGroup=='fzr'||roleGroup=='admin'?'../fzr-workDetail/fzr-workDetail?code='+item.RepairCode+'&ProjectCode='+ProjectCode:'../yg-workDetail/yg-workDetail?code='+item.RepairCode+'&ProjectCode='+ProjectCode">
							
							<view class="flex-bd">
								<view class="t">单号：{{item.RepairNo}}</view>
								<view class="info">报修人：{{item.applyusername}}</view>
								<view class="info" v-if="item.repairusername">接单人：{{item.repairusername}}</view>
								<view class="info">报修时间：{{item.ApplyTime}}</view>
							</view>
							<view class="flex-hd">
								<u-button type="default" size="mini" plain disabled v-if="item.RepairStatus==0">待指派</u-button>
								<u-button type="default" size="mini" plain disabled v-if="item.RepairStatus==1">待接单</u-button>
								<u-button type="warning" size="mini" plain disabled v-if="item.RepairStatus==2">待维修</u-button>
								<u-button type="success" size="mini" plain disabled v-if="item.RepairStatus==3">待评价</u-button>
							</view>
						</navigator>
					</block>
					<view class="mr30" v-if="!empty">
						<u-loadmore :status="loadmore.status" :icon-type="loadmore.iconType" :load-text="loadmore.loadText" />
					</view>
					<u-empty class="u-text-center" v-if="empty" text="暂无内容" margin-top="300"></u-empty>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import utils from '../../components/utils.js'
	export default {
		data() {
			return {
				tabs: [],
				current: 0,
				keyword:'',
				orderList:'',
				empty:false,
				page:1,
				loadmore:{
					status: 'loading',
					iconType: 'flower',
					loadText: {
						loading: '努力加载中',
						nomore: '已无更多'
					}
				},
				orderCount:0,
				status:'',
				ProjectCode:'',
				roleGroup:''
			}
		},
		onLoad(e) {
			var roleGroup=utils.getRoleGroup(uni.getStorageSync('UserRemark'))
			this.roleGroup=roleGroup
			var ProjectCode=e.projectCode
			this.ProjectCode=ProjectCode
			var tabs
			
			if(roleGroup=='fzr'||roleGroup=='admin'){
				var applyStatusList = '0,1,2,3'
			}
			else{
				var applyStatusList = '1,2,3'
			}
			
			this.$http.get('/ProjectMngApi/GetRepairCount', {
				params:{
					UserCode:uni.getStorageSync('UserCode'),
					ProjectCode:this.ProjectCode,
					orderNo:'',
					applyStatusList:applyStatusList
				}
			}).then(res => {
				var data =res.Data
				console.log(data)
				if(roleGroup=='fzr'||roleGroup=='admin'){
					tabs=[
						{status:0,name:'待指派',count:data.Count1},
						{status:1,name:'待接单',count:data.Count2},
						{status:2,name:'已接单',count:data.Count3},
						{status:3,name:'已完成',count:data.Count4}
					]
					
				}
				else{
					var tabs=[
						{status:1,name:'待接单',count:data.Count1},
						{status:2,name:'进行中',count:data.Count2},
						{status:3,name:'已完成',count:data.Count3}
					]
				}
				this.tabs=tabs
				this.status=tabs[0].status
				this.renderList(1,tabs[0].status).then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.getNum(tabs[0].status)
			})
				
		},
		methods: {
			tabsChange(item) {
				if(this.current == item.index){
					return
				}
				this.current = item.index;
				this.status=item.item.status
				this.loadmore.status='loading'
				this.renderList(1,item.item.status).then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.getNum(item.item.status)
				this.page=1
			},
			onreachBottom() {
				if(this.loadmore.status=='nomore'){
					return
				}
				var orderList=this.orderList
				this.renderList(this.page+1,this.status).then(res=>{
					for(let index in res){
						orderList.push(res[index])
					}
					if(res.length>0){
						this.orderList=orderList
						this.page++
					}
					else{
						this.loadmore.status='nomore'
					}
				})
			},
			search(value) {
				this.renderList(1,this.status).then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.page=1
				this.getNum(this.status)
			},
			
			//获取数量
			getNum(status=''){
				this.$http.get('RepairMngApi/GetRepairNum', {
					params:{
						UserCode:uni.getStorageSync('UserCode'),
						ProjectCode:this.ProjectCode,
						RepairNo:this.keyword,
						RepairStatus:status
					}
				}).then(res => {
					this.orderCount=res.Data.mydealcount
				})
			},
			//获取列表
			renderList(page,status=''){
				var url=''
				var params={
					p:page,
					ps:10,
					projectCode:this.ProjectCode,
					RepairNo:this.keyword,
					RepairStatus:status
				}
				if(this.roleGroup=='fzr'||this.roleGroup=='admin'){
					url='/RepairMngApi/GetListByProjectCode'
					params.UserCode=uni.getStorageSync('UserCode')
				}
				else{
					url='/RepairMngApi/GetMyList'
					params.RepairUserCode=uni.getStorageSync('UserCode')
				}
				return new Promise((resolve)=>{
					this.$http.get(url, {
						params:params
					}).then(res => {
						if(page==1&res.Data.length==0){
							this.empty=true
						}
						else{
							this.empty=false
							for(let index in res.Data){
								res.Data[index].ApplyTime=res.Data[index].ApplyTime.replace(/\//g,'-')
								res.Data[index].ApplyTime=res.Data[index].ApplyTime.replace('T',' ')
							}
						}
						var list=[]
						resolve(res.Data)
					})
				})		
			}
		}
	}
</script>

<style>
	@import url("style.css");
	.title-213{
		font-size: 36rpx;
	}
</style>
