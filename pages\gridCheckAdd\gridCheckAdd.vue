<template>
	<view class="border-top">
		<view class="report-top">
			<view class="tab flex">
				<view class="i flex-bd active">网格巡检</view>
			</view>
		</view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">详情内容</view>
				</view>
			</view>
			<view slot="body">
				<u-form :model="form" ref="uForm">					
					<u-form-item label="填报时间" label-width="150rpx" :border-bottom="false">
						<u-input v-model="form.CheckTime" disabled="" :border="true" />
					</u-form-item>	
					<u-form-item label="编号" label-width="150rpx" :border-bottom="false">
						<u-input v-model="info.PlaceNumber" disabled="" :border="true" />
					</u-form-item>
					<u-form-item label="巡检描述" label-width="150rpx" :border-bottom="false">
						<view>
							<view class="" v-for="(item,index) in remarkList">
								<view class="item">{{item.Content}}</view>
								<view>
									<u-radio-group v-model="item.value">
										<u-radio
											v-for="(i, idx) in item.radio" :key="idx" 
											:name="i.name"
											:disabled="i.disabled"
										>
											{{i.name}}
										</u-radio>
									</u-radio-group>
								</view>
							</view>
						</view>
					</u-form-item>
					<u-form-item label="备注" label-width="150rpx" :border-bottom="false">
						<u-input v-model="form.CheckRemark" type="textarea" :border="true" />
					</u-form-item>
					<u-form-item label="上传图片" label-width="150rpx" :border-bottom="false">
						<u-upload ref="uUpload1" :action="action" :header="uploadHeader" @on-success="uploadOk" :file-list="fileList" ></u-upload>
					</u-form-item>
					<!-- <u-form-item label="签名" label-width="150rpx" :border-bottom="false">
						<view class="singature" v-if="SignImageUrl">
							<image class="singature-images" :src="SignImageUrl"></image>
						</view>
						<navigator class="sign" url="../singature/singature">请点击进行签名</navigator>
					</u-form-item> -->
				</u-form>
			</view>
		</u-card>
		<view class="mr30">
			<u-button type="primary" @click="submit">确认提交</u-button>
		</view>
	</view>
</template>

<script>
	import utils from '../../components/utils.js'
	const app = getApp();
	export default {
		data() {
			return {
				uploadHeader:{
					Authorization: 'Bearer '+ uni.getStorageSync('access_token')
				},
				action:app.globalData.uploadUrl,
				form:{
					CheckTime:'',
					CheckRemark:''
				},
				SignImageUrl:'',
				photoList:[],
				remarkList:[],
				index:0,
				value:'是',
				fileList:[],
				info:''
			}
		},
		methods: {
			submit(){
				var remarkList = this.remarkList
				var codes = []
				for(var i =0;i<remarkList.length;i++){
					if(remarkList[i].value == '是'){
						codes.push(remarkList[i].Code)
					}
				}
				
				// if(!this.form.SignImage&&codes.length==remarkList.length){
				// 	return this.$u.toast('请进行签名');
				// }
				
				var photoList1=[]
				var files1 = this.$refs.uUpload1.lists;
				for(let index in files1){
					photoList1.push(files1[index].response.RetValue)
				}	
				this.form.Images=photoList1.toString()
				this.form.Codes=codes.toString()
				this.$http.post('/GridCheckApi/Execute?docmd=add', this.form).then(res => {
					this.$u.toast(res.ErrMsg);
					if(res.ErrCode==100){
						setTimeout(function(){
							uni.navigateBack({
								delta:1
							})
							uni.$emit('update',{update:true})
						},1000)
					}
				})
			},
			uploadOk(data,index,lists){
				console.log(data)
				this.photoList.push(data.RetValue)
			}
		},
		onLoad(e) {
			this.form.CheckTime=utils.formatTime(new Date())
			this.form.CheckUserCode=uni.getStorageSync('UserCode')
			
			
			this.$http.get('/GridCheckApi/GetGridCheckByCode', {
				params:{
					code:e.code?e.code:'',
					placeNumber:e.placeNumber?e.placeNumber:''
				}
			}).then(res => {
				this.info=res.Data.GridCheck
				this.form.GridCheckCode=res.Data.GridCheck.Code
				
				var list = res.Data.GridCheckRemarks
				for(var i=0;i<list.length;i++){
					list[i].radio = [
						{
							name: '是',
							disabled: false
						},
						{
							name: '否',
							disabled: false
						}
					]
					list[i].value = '是'
				}
				this.remarkList = list
			})
			
			
		}
	}
</script>

<style>
	@import url("../fzr-reportList/style.css");
	.qcode{
		padding-right: 30rpx;
	}
	.qcode i{
		font-size: 48rpx;
	}
	.sign{
		background: #F6F6F6;
		padding: 4rpx 10rpx;
		border-radius: 2px;
		text-align: center;
		margin-bottom: 20rpx;
	}
	.singature{
		position: relative;
		height: 200rpx;
		width: 400rpx;
		padding-bottom: 30rpx;
	}
	.singature-images{
		transform:rotate(-90deg);
		position: absolute;
		top: 50%;
		left: 50%;
		margin-left: -100rpx;
		margin-top:-200rpx ;
		height: 400rpx;
		width: 200rpx;
	}
</style>
