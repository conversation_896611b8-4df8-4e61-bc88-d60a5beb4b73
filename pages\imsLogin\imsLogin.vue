<template>
	<view class="ims">
		<view class="ims-login">
			<view class="login-header">
				<view class="logo">
					<image src="https://miaojie.anyoucloud.com/Style/v2/wx-app/<EMAIL>" mode="aspectFit"></image>
				</view>
				<view class="login-title">综合管理系统</view>
			</view>
			<view class="login-box">
				<uv-form labelPosition="top" ref="form">
					<uv-form-item label="手机号" labelWidth="100">
						<uv-input v-model="ims.phone" placeholder="请输入手机号"></uv-input>
					</uv-form-item>
					<uv-form-item label="验证码" labelWidth="100">
						<uv-input v-model="ims.VerifyCode" placeholder="请输入验证码">
							<template v-slot:suffix>
								<uv-code ref="uCode" seconds="20" changeText="X秒重新获取哈哈哈"></uv-code>
								<uv-button type="primary" customStyle="height:28px;line-height:28x;background: #94A5FF;border-color:#94A5FF ;padding:0 20px" @click="getVerifyCode" :text="tips" shape="circle" size="mini"></uv-button>
							</template>
						</uv-input>
					</uv-form-item>
					<uv-form-item>
						<uv-button type="primary" size="large" customStyle="height:42px;line-height:42px;margin-top: 20px;width:100%" text="登录" shape="circle" @click="login"></uv-button>
					</uv-form-item>
				</uv-form>
			</view>
			<view class="login-footer">
				<view class="link" @click="pms">妙洁物业管理系统</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			ims: {
				phone: "",
				VerifyCode: "",
			},
			canGetCode: true,
			timer: "",
			timerNum: 60,
			tips: "获取验证码",
		};
	},

	methods: {
		//获取验证码
		getVerifyCode() {
			if (!this.ims.phone) {
				uni.showToast({
					icon: "none",
					title: "请输入手机号",
					duration: 1500,
				});
				return;
			}
			if (!uni.$uv.test.mobile(this.ims.phone)) {
				uni.showToast({
					icon: "none",
					title: "请输入正确的手机号",
					duration: 1500,
				});
				return;
			}
			if (this.$refs.uCode.canGetCode) {
				// 模拟向后端请求验证码
				uni.showLoading({
					title: "正在获取验证码",
				});
				this.$apis
					.getSms(
						{
							CellPhone: this.ims.phone,
						},
						{ custom: { loading: false } }
					)
					.then((res) => {
						uni.showToast({
							icon: "none",
							title: res.msg,
							duration: 1500,
						});

						this.canGetCode = false;
						this.timer = setInterval(() => {
							this.timerNum--;
							this.tips = "倒计时" + this.timerNum + "秒";
						}, 1000);
						setTimeout(() => {
							this.canGetCode = true;
							this.timerNum = 60;
							this.tips = "获取验证码";
							clearInterval(this.timer);
						}, 60000);
					});
				setTimeout(() => {
					uni.hideLoading();
					// 这里此提示会被this.start()方法中的提示覆盖
					uni.showToast({
						icon: "none",
						title: "验证码已发送",
						duration: 1500,
					});
					// 通知验证码组件内部开始倒计时
					this.$refs.uCode.start();
				}, 2000);
			} else {
				uni.showToast({
					icon: "none",
					title: "倒计时结束后再发送",
					duration: 1500,
				});
			}
		},
		//登录
		login() {
			if (!this.ims.phone) {
				uni.showToast({
					icon: "none",
					title: "请输入手机号",
					duration: 1500,
				});
				return;
			}
			if (!uni.$uv.test.mobile(this.ims.phone)) {
				uni.showToast({
					icon: "none",
					title: "请输入正确的手机号",
					duration: 1500,
				});
				return;
			}
			if (this.ims.VerifyCode == "") {
				uni.showToast({
					icon: "none",
					title: "请输入验证码",
					duration: 1500,
				});
				return;
			}			
			this.$apis
				.login({
					CellPhone: this.ims.phone.trim(),
					VerifyCode: this.ims.VerifyCode.trim(),
				})
				.then((res) => {
					if (res.code == 100) {
						// 保存登录信息
						uni.setStorageSync("ImsUserInfo", res.data.UserInfo);
						uni.setStorageSync("ImsUserCode", res.data.UserInfo.Code);
						if(res.data.RoleList.length==1 && res.data.RoleList[0].RoleCode=="possessor"){
							
							uni.setStorageSync("ImsUserRole", 0);//普通职员
						}else{
							uni.setStorageSync("ImsUserRole", 1);
						}
						
						uni.showToast({
							mask: true,
							title: "登录成功",
							icon: "success",
							duration: 1500,
						});
						setTimeout(() => {
							uni.navigateTo({
								url: "/ims/index/index",
							});
						}, 1500);
					} else {
						uni.showToast({
							icon: "none",
							title: res.msg || "登录失败",
							duration: 1500,
						});
						this.ims.VerifyCode = ""; // 清空验证码
					}
				})
				.catch((err) => {
					uni.showToast({
						icon: "none",
						title: "登录失败，请重试",
						duration: 1500,
					});					
					console.error(err);
					this.ims.VerifyCode = ""; // 清空验证码
				});
		},
		//跳转物业系统
		pms() {
			uni.navigateTo({
				url: "/pages/login/login",
			});
		},
	},
};
</script>

<style lang="scss">
body {
	background: url("https://miaojie.anyoucloud.com/Style/v2/wx-app/<EMAIL>") #edeff3 top center no-repeat;
	background-size: 100%;
}
.ims {
	.ims-login {
		.login-header {
			text-align: center;
			.logo {
				width: 300rpx;
				margin: 0 auto;
				padding: 300rpx 0 30rpx 0;
				image {
					width: 300rpx;
					height: 107rpx;
				}
			}
			.login-title {
				font-size: 56rpx;
				font-weight: bold;
				letter-spacing: 2px;
			}
		}
		.login-box {
			margin: 30rpx 10%;
			.uv-form-item {
				.uv-form-item__body__left__content__label {
					line-height: 30px;
					color: #6c7278;
				}
				.uv-input {
					background: #fff;
					border-color: #e7e6e4 !important;
					border-radius: 5px;

					.uv-input__content__field-wrapper__field {
						height: 34px;
						line-height: 34px;
					}
				}
			}
		}
		.login-footer {
			margin-top: 10vh;
			.link {
				text-align: center;
				color: #5771f9;
				font-size: 30rpx;
				line-height: 60rpx;
			}
		}
	}
}
</style>
