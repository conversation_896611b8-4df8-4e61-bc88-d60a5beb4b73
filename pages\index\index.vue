<template>
	<view class="content">
		<view class="u-page">
			<view class="index-header">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap ">
						<h1 class="u-font-30">欢迎使用妙洁物业管理平台</h1>
						<p class="u-font-24">{{UserName}},您好</p>
					</view>
					<view class="u-flex-nowrap">
						<u-button type="primary" shape="circle" size="mini" @click="gotoUrl('/pages/user-inform/user-inform')">
							消息中心
							<!-- <u-badge :is-dot="true" :offset="[0, 0]" size="mini"></u-badge> -->
						</u-button>
					</view>
				</view>
			</view>
			<u-swiper :list="swiper"></u-swiper>
			<view class="index-notic u-border-bottom u-flex u-row-between">
				<view class="u-padding-right-30 u-border-right">
					<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/index-notic.png"></image>
				</view>
				<view class="u-flex-nowrap u-flex-12 u-margin-left-26 u-margin-right-26">
					<swiper class="swiper" :autoplay="true" :interval="5000" :duration="500" :vertical="true">
						<swiper-item v-for="(item ,index) in indexnotic" :key="index">
							<navigator :url="'../noticeDetail/noticeDetail?NoticeCode='+item.code">
								<view class="notic_t">
									<u-tag class="u-margin-right-10" text="已读" type="info" size="mini" mode="dark"  v-if="item.IsSee==1" />
									<u-tag class="u-margin-right-10" text="未读" type="error" size="mini" mode="dark" v-else />
									
									{{ item.date }}
								</view>
								<text class="notic_f">{{ item.title }}</text>
							</navigator>
						</swiper-item>
					</swiper>
				</view>
				<view>
					<u-button type="info" size="medium" @click="moreInfo">查看全部</u-button>
				</view>
			</view>
			<view class="index-count">
				我的项目<view class="inline">{{projectList.length}}</view>
			</view>
			<view class="project-list">
				<navigator class="i flex border-all" v-for="(item,index) in projectList" :key="index" :url="'../project-index/project-index?ProjectCode='+item.ProjectCode">
					<view class="flex-hd">
						<image v-if="item.MainImage" :src="item.MainImage" mode="aspectFill"></image>
						<image v-else src="../../static/sbnopic.png" mode="aspectFill"></image>
					</view>
					<view class="flex-bd">
						<view class="name">{{item.ProjectName}}</view>
						<view class="info">地址：{{item.Address}}</view>
						<view class="info">项目负责人：{{item.ProjectLeaderUserName}}</view>
						<view class="count" v-if="UserRemark=='002'||UserRemark=='106'||UserRemark=='107'||roleGroup=='admin'">
							<view class="f inline">
								待处理工单：<view class="inline" v-if="item.waitdealnum==0">0</view><view class="inline" v-else>{{item.waitdealnum}}</view>
							</view>
							<view class="f inline">
								待处理预约：<view class="inline" v-if="item.waitdealreservenum==0">0</view><view class="inline" v-else>{{item.waitdealreservenum}}</view>
							</view><!-- 
							<view class="f inline">
								巡检超期：<view class="inline">15</view>
							</view> -->
						</view>
						<view class="count" v-else-if="UserRemark=='003'">
							<view class="f inline">
								待处理预约：<view class="inline" v-if="item.waitdealreservenum==0">0</view><view class="inline" v-else>{{item.waitdealreservenum}}</view>
							</view>
						</view>
						<view class="count" v-else-if="UserRemark=='004'">
							<view class="f inline">
								待处理工单：<view class="inline" v-if="item.waitusenum==0">0</view><view class="inline" v-else>{{item.waitusenum}}</view>
							</view>
							<view class="f inline">
								待反馈：<view class="inline" v-if="item.waitdealnum==0">0</view><view class="inline" v-else>{{item.waitdealnum}}</view>
							</view>
						</view>
						<view class="count" v-else-if="UserRemark=='009'">
							<view class="f inline">
								待处理工单：<view class="inline" v-if="item.waitusenum==0">0</view><view class="inline" v-else>{{item.waitusenum}}</view>
							</view>
							<view class="f inline">
								待反馈：<view class="inline" v-if="item.waitdealnum==0">0</view><view class="inline" v-else>{{item.waitdealnum}}</view>
							</view>
						</view>
						<view class="count" v-else-if="UserRemark=='108'||UserRemark=='109'||UserRemark=='110'||UserRemark=='111'||UserRemark=='112'">
							<view class="f inline">
								待反馈：<view class="inline" v-if="item.waitdealnum==0">0</view><view class="inline" v-else>{{item.waitdealnum}}</view>
							</view>
							<view class="f inline">
								已反馈：<view class="inline" v-if="item.waitusenum==0">0</view><view class="inline" v-else>{{item.waitusenum}}</view>
							</view>
						</view>
						<view class="count" v-else>
							<view class="f inline">
								待接单：<view class="inline" v-if="item.waitdealnum==0">0</view><view class="inline" v-else>{{item.waitdealnum}}</view>
							</view>
							<view class="f inline">
								进行中：<view class="inline" v-if="item.waitusenum==0">0</view><view class="inline" v-else>{{item.waitusenum}}</view>
							</view>
						</view>
					</view>
					<view class="flex__ft"></view>
				</navigator>
			</view>
			<u-empty v-if="projectList.length==0" class="u-text-center" text="暂无内容" margin-top="150"></u-empty>
		</view>
		<u-tabbar v-model="current" :list="tabbar" :inactive-color="inactiveColor" :activeColor="ThemeColor" @change="change"></u-tabbar>
		<u-mask class="authshow u-text-center" :show="AuthShow">
			<view class="auth_box u-flex u-flex-wrap u-row-center">
				<view>

					<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/auth_img.png" @click="authlink"></image>

					<view @click="AuthShow = false">
						<u-icon name="close-circle" color="#999" size="60" class="u-padding-30"></u-icon>
					</view>
				</view>
			</view>
		</u-mask>
	</view>
</template>
<script>
	const app = getApp();
	import utils from '../../components/utils.js'
	export default {
		data() {
			return {
				ThemeColor: '',
				inactiveColor: '#909399',
				AuthShow:false,
				UserName:'用户名',
				UserType: '',
				indexnotic: [],
				swiper: [],
				tabbar: [
					{
						iconPath: 'shouye',
						selectedIconPath: 'tabbar-icon01_h',
						text: '首页',
						customIcon: true,
						pagePath: '/pages/index/index'
					},
					{
						iconPath: 'nfc',
						selectedIconPath: 'nfc1',
						text: 'NFC',
						customIcon: true,
						pagePath: '/pages/nfc/nfc'
					},
					{
						iconPath: 'yonghu',
						selectedIconPath: 'wode',
						text: '我的',
						customIcon: true,
						pagePath: '/pages/user/user'
					}
				],
				current: 0,
				swiperCurrent: 0,
				roleGroup:'',
				projectList:[],
				hostUrl:app.globalData.hostUrl,
				tabbarSet:false,
				UserRemark:''
			};
		},
		onLoad() {
			this.ThemeColor = getApp().globalData.ThemeColor;
			
			var UserType=uni.getStorageSync('UserType')
			var IsCheck=uni.getStorageSync('IsCheck')
			var homeForm=uni.getStorageSync('homeForm')			
			//判断是否业主是否审核通过
			uni.setStorageSync('homeForm','')				
			this.getNotice()
			
			if(uni.getStorageSync('IsCheck')!=2){
				this.getProject()
			}
			this.getSlide()
		},
		onShow(){
			//当审核中每次更新信息
			if(uni.getStorageSync('IsCheck')==1){
				this.authUpdate().then(res=>{
					uni.setStorageSync('IsCheck',res.Data.IsCheck)
					uni.setStorageSync('projectName',res.Data.DefaultProjectName)
					uni.setStorageSync('projectCode',res.Data.DefaultProject?res.Data.DefaultProject.ProjectCode:'')
					uni.setStorageSync('UserRemark',res.Data.UserRemark)
					uni.setStorageSync('roleName',utils.getRole(res.Data.UserRemark))
					this.showFn()
				})	
			}
			else{
				this.showFn()
			}
			if(uni.getStorageSync('IsCheck')==2){
				this.getProject()
			}
			var that=this
			uni.$on('update',function(res){
				if(res.noticeUpdate){
					that.getNotice()
				}
				// 清除监听
				uni.$off('update');
			})
		},
		methods: {
			change(e){
				console.log(e)
				if(e==1){
					uni.navigateTo({
						url:"../nfc/nfc"
					})
				}
			},
			showFn(){
				var IsCheck=uni.getStorageSync('IsCheck')
				//当未审核或审核不通过则弹出审核
				if(IsCheck==0||IsCheck==3){
					this.AuthShow=true
				}				
				this.UserType=uni.getStorageSync('UserType')
				this.UserName=uni.getStorageSync('UserName')
				this.UserRemark=uni.getStorageSync('UserRemark')
				if(uni.getStorageSync('UserRemark')=='002'){
					if(this.tabbarSet==true){
						return
					}
					var phonebook={
						iconPath: 'tongxunlu',
						selectedIconPath: 'renyuantongxunlu',
						text: '通讯录',
						customIcon: true,
						pagePath: '/pages/add-book/add-book'
					}
					console.log('www')
					var newTabber=[]
					var oldTabbar=this.tabbar
					newTabber[0]=oldTabbar[0]
					newTabber[1]=oldTabbar[1]
					newTabber[2]=phonebook
					newTabber[3]=oldTabbar[2]
					this.tabbar=newTabber
					this.tabbarSet=true
				}
			},
			
			getProject() {
				var url
				var UserRemark=uni.getStorageSync('UserRemark')
				var roleGroup=utils.getRoleGroup(UserRemark)
				var type=''
				console.log(roleGroup)
				this.roleGroup=roleGroup
				if(roleGroup=='fzr'||roleGroup=='admin'){
					url='/HomepageMng/GetProjectMain'
				}
				else if(roleGroup=='fw'){
					if(UserRemark==108||UserRemark==109){
						type=3
					}
					else if(UserRemark==112){
						type=1
					}
					else if(UserRemark==110||UserRemark==111){
						type=4
					}
					else if(UserRemark=='004'||UserRemark=='009'){
						type=2
					}
					else{
					}
					url='/HomepageMng/GetMyProject'
					
				}
				else{
					url='/HomepageMng/GetProjectByChargeUser'
				}
				if(type){
					var params={
						UserCode:uni.getStorageSync('UserCode'),
						type:type
					}
				}
				else{
					var params={
						UserCode:uni.getStorageSync('UserCode')
					}
				}
				this.$http.get(url, {
					params:params
				}).then(res => {
					var list=JSON.parse(res.Data)
					for(let index in list){
						if(list[index].MainImage){
							list[index].MainImage=this.hostUrl+list[index].MainImage
						}
						else{
							list[index].MainImage='http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/projectimg01.png'
						}
					}
					console.log(list)
					this.projectList=list
				})
			},
			moreInfo() {
				uni.navigateTo({
					url: "../user-notice/user-notice"
				})
			},
			authlink(){				
				uni.navigateTo({
					url: '/pages/auth/auth'
				});
				this.AuthShow=false
			},
			gotoUrl(e){
				if(this.authCheck()){
					uni.navigateTo({
						url:e
					})
				}
			},
			authCheck(e){
				var IsCheck = uni.getStorageSync('IsCheck')
				if (IsCheck == 2) {
					return true				
				} else if(IsCheck == 1){
					this.$u.toast('正在认证中');
				} else {
					this.AuthShow = true					
					return false
				}
			},
			getNotice(){
				this.$http.post('/NoticeMng/GetListAll?p=1&ps=10', {
					UserCode:uni.getStorageSync('UserCode')
				}).then(res => {
					var list=[]
					for(let index in res.Data){
						var item={
							title:res.Data[index].Notice.Title,
							date:res.Data[index].Notice.CreateDatetimeText,
							code:res.Data[index].NoticeCode,
							IsSee:res.Data[index].IsSee
						}
						list.push(item)
					}
					this.indexnotic=list
				})
			},
			authUpdate(){
				return new Promise((resolve, reject)=>{
					this.$http.get('/HomepageMng/GetUserInfo', {
						params:{
							CellPhone: uni.getStorageSync('CellPhone')
						}
					}).then(res => {
						resolve(res)						
					})
				})
			},
			getSlide(){
				this.$http.get('HomepageMng/GetCmsInfo?channelCode=banner', {
					params:{
						
					}
				}).then(res => {
					var swiper=[]
					for(let index in res.Data){
						swiper.push({
							image:this.hostUrl+res.Data[index].SmallImage,
							title:res.Data[index].InfoTitle
						})
					}
					this.swiper=swiper
				})
			}
		}
	};
</script>
<style>
	.more-btn {
		text-align: center;
		margin: 30rpx auto;
	}
	.index-count{
		text-align: center;
		margin-top: 30rpx;
		font-size: 24rpx;
		color: #000;
	}
	.index-count .inline{
		width: 36rpx;
		height: 36rpx;
		background: #669999;
		color: #fff;
		border-radius: 50%;
		line-height: 36rpx;
		text-align: center;
		margin-left: 20rpx;
	}
</style>
