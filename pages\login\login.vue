<template>
	<view class="content">
		<view class="login_top">
			<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/login_top.png"></image>
			<view class="user-img">
				<open-data type="userAvatarUrl"></open-data>
			</view>
		</view>
		<view class="login-title">妙洁物业管理系统</view>
		<view class="login-box">
			<u-form :model="model" :rules="rules" ref="uForm" :errorType="errorType">
				<u-form-item prop="userType" label-width="150" :border-bottom="flase">
					<u-radio-group v-model="radio" @change="radioGroupChange" :width="radioCheckWidth" :wrap="radioCheckWrap"
					 :active-color="ThemeColor">
						<u-radio shape="circle" v-for="(item, index) in radioList" :key="index" :name="item.name">{{ item.name }}</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label=" " prop="phone" label-width="50">
					<u-icon name="shouji" custom-prefix="custom-icon" size="36" color="#666" class="u-absolute"></u-icon>
					<u-input :border="border" placeholder="请输入手机号" cursor-spacing="30" v-model="model.phone" type="number"></u-input>
				</u-form-item>
				<u-form-item label=" " prop="code" label-width="50">
					<u-icon name="yanzhengma" custom-prefix="custom-icon" size="36" color="#666" class="u-absolute"></u-icon>
					<u-input :border="border" placeholder="请输入验证码" cursor-spacing="30" v-model="model.code" type="text"></u-input>
					<u-button slot="right" type="primary" size="mini" :plain="true" @click="getCode">{{ codeTips }}</u-button>
				</u-form-item>
			</u-form>
			<!-- <view class="tips">未注册的手机号验证后自动创建妙洁账号</view> -->
			<u-button @click="submit" :ripple="true" type="primary" shape="circle" :custom-style="customStyle">提交</u-button>
			<u-verification-code seconds="60" ref="uCode" @change="codeChange"></u-verification-code>
		</view>
	</view>
</template>

<script>
	import utils from "../../components/utils.js"
	export default {
		data() {
			let that = this;
			return {
				ThemeColor: '#000',
				model: {
					phone: '',
					code: '',
					userType: '我是业主'
				},

				rules: {
					phone: [{
							required: true,
							message: '请输入手机号',
							trigger: ['change', 'blur']
						},
						{
							validator: (rule, value, callback) => {
								// 调用uView自带的js验证规则，详见：https://www.uviewui.com/js/test.html
								return this.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change，二者之间用英文逗号隔开
							trigger: ['change', 'blur']
						}
					],
					code: [{
							required: true,
							message: '请输入验证码',
							trigger: ['change', 'blur']
						},
						{
							type: 'number',
							message: '验证码只能为数字',
							trigger: ['change', 'blur']
						}
					],
				},
				radioList: [{
						name: '我是业主',
						checked: true,
						disabled: false
					},
					{
						name: '我是员工',
						checked: false,
						disabled: false
					}
				],
				radio: '我是业主',
				border: false,
				check: false,
				labelPosition: 'left',
				codeTips: '获取验证码',
				errorType: ['message'],
				openid:'',
				unionid:''
			};
		},
		onLoad() {
			var that = this
			uni.login({
				provider: 'weixin',
				success: function(res) {
					console.log(res.code);
					that.$http.get('/LoginMng/GetOpenid', {
						params: {
							Jscode: res.code
						}
					}).then(res => {
						that.openid=JSON.parse(res.Data).openid
						that.unionid=JSON.parse(res.Data).unionid
					})
				}
			});
			
			var UserCode=uni.getStorageSync('UserCode')
			var UserType=uni.getStorageSync('UserType')
			if(UserCode){
				if (UserType == 1) {
					uni.switchTab({
						url: '/pages/yz-home/yz-home'
					})
				} else {
					uni.switchTab({
						url: '/pages/index/index'
					})
				}
			}
		},
		onReady() {
			this.ThemeColor = getApp().globalData.ThemeColor;
			this.$refs.uForm.setRules(this.rules);
		},
		methods: {
			submit() {				
				var code = this.model.code
				var phone = this.model.phone
				var userType = this.model.userType
				this.$refs.uForm.validate(valid => {
					if (valid) {
						if (!code) return this.$u.toast('请输入正确的验证码');
						this.$http.post('/LoginMng/Login', {
							CellPhone: phone,
							VerificationByCode: code,
							UserType: userType == '我是业主' ? 1 : 2,
							WxOpenid: this.openid,
							Unionid:this.unionid
						}).then(res => {
							if (res.ErrCode == 100) {
								console.log(phone)
								this.$http.get('/HomepageMng/GetUserInfo', {
									params: {
										CellPhone: phone
									}
								}).then(res => {
									this.$u.toast(res.ErrMsg);
									if (res.ErrCode == 100) {
										uni.setStorageSync('CellPhone', res.Data.CellPhone)
										uni.setStorageSync('UserCode', res.Data.UserCode)
										uni.setStorageSync('IsCheck', res.Data.IsCheck)
										uni.setStorageSync('UserType', res.Data.UserType)
										uni.setStorageSync('HeadImg', res.Data.HeadImg)
										uni.setStorageSync('Gender', res.Data.Gender)
										uni.setStorageSync('UserName', res.Data.UserName)
										uni.setStorageSync('JobNumber', res.Data.JobNumber)
										uni.setStorageSync('projectName', res.Data.DefaultProjectName)
										if (res.Data.DefaultProject) {
											uni.setStorageSync('projectCode', res.Data.DefaultProject.ProjectCode)
										} else {
											uni.setStorageSync('projectCode', '')
										}
										uni.setStorageSync('WxOpenid', res.Data.WxOpenid)
										uni.setStorageSync('UserRemark', res.Data.UserRemark)
										var role=utils.getRole(res.Data.UserRemark)
										uni.setStorageSync('roleName',role)
										setTimeout(function() {
											if (res.Data.UserType == 1) {
												uni.switchTab({
													url: '/pages/yz-home/yz-home'
												})
											} else {
												uni.switchTab({
													url: '/pages/index/index'
												})
											}
										}, 1000)

									}
								})


							} else {
								this.$u.toast(res.ErrMsg);
							}

						})
					} else {
						console.log('验证失败');
					}
				});
			},

			// radio选择发生变化
			radioGroupChange(e) {
				this.model.userType = e;
			},
			labelPositionChange(index) {
				this.labelPosition = index == 0 ? 'left' : 'top';
			},
			codeChange(text) {
				this.codeTips = text;
			},
			// 获取验证码
			getCode() {
				if (!this.model.phone) return this.$u.toast('请输入手机号');
				if (!this.$u.test.mobile(this.model.phone)) return this.$u.toast('请输入正确的手机号');
				if (this.$refs.uCode.canGetCode) {

					this.$http.post('/LoginMng/GetSms', {
						CellPhone: this.model.phone
					}).then(res => {
						this.$u.toast('验证码已发送');
						// 通知验证码组件内部开始倒计时
						this.$refs.uCode.start();
					})

				} else {
					this.$u.toast('倒计时结束后再发送');
				}
			}
			
		}
	};
</script>

<style>
	body {
		background-color: #fff !important;
	}

	.login_top {
		position: relative;
	}

	.login_top image {
		width: 100%;
	}

	.login_top .user-img {
		position: absolute;
		width: 20vw;
		height: 20vw;
		box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3);
		bottom: 8vw;
		right: 20vw;
		border: rgba(255, 255, 255, 0.8) 8rpx solid;
		border-radius: 50%;
		overflow: hidden;
	}

	.login-title {
		text-align: center;
		font-size: 36rpx;
		font-weight: bold;
		line-height: 60rpx;
	}

	.login-box {
		padding: 5vw 10vw;
	}

	.login-box>u-button {
		display: block;
		margin-top: 10vw;
	}

	.login-box u-radio {
		display: inline-block;
		width: 40%;
		padding-left: 10%;
	}

	.login-box u-icon {
		left: 0;
	}

	.tips {
		margin-top: 20rpx;
	}
</style>
