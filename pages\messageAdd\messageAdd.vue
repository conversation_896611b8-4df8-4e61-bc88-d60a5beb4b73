<template>
	<view>
		<view class="feedback">
			<view class="stitle">
				请填写内容
			</view>
			<view class="text">
				<u-input v-model="form.FeedBackContent" type="textarea" placeholder="在这里输入内容"></u-input>
			</view>
			<view class="stitle">
				上传图片
			</view>
			<view class="img">
				<u-upload :action="action" :form-data="formData" @on-success="uploadOk" :before-upload="beforeUpload" @on-remove="remove" :width="150" :height="150" ></u-upload>
			</view>
			<!-- <u-form-item label="手机号码" label-width="140"><u-input v-model="form.name" placeholder="请输入手机号码" /></u-form-item> -->
			<view class="mr30" style="margin: 30rpx 0;">
				<u-button type="primary" @click="submit">确认提交</u-button>
			</view>
		</view>
		<u-modal v-model="loginShow" :show-cancel-button="true" content="请先登录" cancel-text="暂不登录" confirm-text="登录" @cancel="loginShow=false" @confirm="gotoLogin()"></u-modal>
	</view>
</template>

<script>
	// import OSSUtil from '../../common/aliyun/ossUtil.js'
	export default {
		data() {
			return {
				background:{
					backgroundColor: '#2465aa',
				},
				customStyle: {
					height: "100rpx",
					marginTop: '60rpx',
					background: "#2465aa",
				},
				form:{
					FeedBackContent:'',
					FeedBackImages:'',
					FeedBackType:''
				},
				formData:{},
				action:OSSUtil.host(),
				type:[{
					name:"APP使用",
					checked:false
				},{
					name:"继续教育",
					checked:false
				},{
					name:"安全教育",
					checked:false
				},{
					name:"考试",
					checked:false
				},{
					name:"其他",
					checked:false
				}],
				typeIndex:0,
				imagesList:[],
				loginShow:false
			}
		},
		onLoad() {
			
			var that = this
			var timeStr = this.timeStr()
			OSSUtil.get_STS().then(res=>{
				console.log(res)
				var formData={
					name:'',
					key: timeStr,
					policy: res.policy,
					OSSAccessKeyId: res.accessId,
					success_action_status: '200',
					signature: res.signature,
				}
				that.formData = formData
			})
			
		
		},
		methods: {
			call(e){
				uni.makePhoneCall({
					phoneNumber:e
				})
			},
			gotoLogin(){
				uni.navigateTo({
					url:'../login/login'
				})
			},
			setType(index){
				if(this.typeIndex != index){
					this.typeIndex = index
					
				}
			},
			timeStr(){
				return 'images/' + new Date().getTime() + '_' + Math.random().toString(36).substring(3, 20)
			},
			remove(index){
				console.log(index)
				this.imagesList.splice(index,1)
			},
			async beforeUpload(index, list) {
				let sleep = (time) => {
				  return new Promise(resolve => setTimeout(resolve, time))
				}
				let data = await sleep(500)
				return true;
			},
			uploadOk(data, index, lists, name){
				console.log(data,index,lists,name)
				console.log(OSSUtil.host()+this.formData.key)
				this.imagesList.push(OSSUtil.host()+this.formData.key)
				this.$set(this.formData,"key",this.timeStr())
			},
			submit(){
				if(!uni.getStorageSync('membercode')){
					return this.loginShow = true
				}
				if(!this.form.FeedBackContent){
					return this.$u.toast('请输入内容')
				}
				this.form.FeedBackType = this.type[this.typeIndex].name
				this.form.CompanyCode = uni.getStorageSync("companycode")
				this.form.FeedBackImages = this.imagesList.toString()
				this.form.CompanyUserCode = uni.getStorageSync("membercode")
				this.form.CompanyCode = uni.getStorageSync("companycode")
				this.$http.post('/feedback/addentity', this.form).then(res => {
					console.log(res)
					this.$u.toast(res.msg);
					if(res.code=="success"){
						setTimeout(function(){
							uni.navigateBack({
								delta:1
							})
						},1000)
					}
				})
			}
		}
	}
</script>

<style>
	page{
		background: #fff;
	}
	.feedback{
		padding: 0 30rpx;
	}
	.feedback .stitle{
		line-height: 1;
		padding-left: 15rpx;
		font-size: 28rpx;
		border-left: 4px solid #2465AA;
		margin: 40rpx 0;
	}
	.type-list{
		
	}
	.type-list .i{
		display: inline-block;
		padding: 16rpx 30rpx;
		border-radius: 100px;
		background: #f6f6f6;
		margin-right: 20rpx;
		margin-bottom: 20rpx;
		width: 30%;
		text-align: center;
	}
	.type-list .i.active{
		background: #2465AA;
		color: #fff;
	}
	.text{
		background: #f6f6f6;
		padding: 20rpx;
	}
	.tel{
		background: #f6f6f6;
		margin: 20rpx 0;
		padding:20rpx 30rpx;
	}
	.tel .tit{
		font-weight: bold;
		color: #333;
	}
	.tel .bot{
		font-weight: bold;
		color: #333;
	}
	.tel .cons{
		padding-bottom: 15rpx;
	}
	.tel .cons .i{
		font-size: 24rpx;
	}
</style>
