<template>
	<view>
		<view class="message-list">
			<view class="li">
				<view class="t flex">
					<view class="flex-bd">咨询下关于物业进行安全管理的通知</view>
					<view class="tag tag2">已回复</view>
				</view>
				<view class="con">客服回复：您好，安全管理的通知关于物业进行</view>
			</view>
			<view class="li">
				<view class="t flex">
					<view class="flex-bd">关于物业进行安全管理的通知</view>
					<view class="tag tag1">暂无回复</view>
				</view>
				<view class="con">暂无回复</view>
			</view>
		</view>
		<view class="btn-wrap" @click="add">
			<view class="btn">增加</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				
			}
		},
		methods: {
			add(){
				uni.navigateTo({
					url: '/pages/messageAdd/messageAdd'
				})
			}
		}
	}
</script>

<style>
	page{
		background: #f6f6f6;
	}
	.message-list{
		
	}
	.message-list .li{
		margin: 30rpx;
		padding: 30rpx;
		background: #fff;
		border-radius: 6rpx;
	}
	.message-list .li .t{
		margin-bottom: 5px;
	}
	.message-list .li .tag{
		font-size: 24rpx;
		margin-left: 4px;
		color: #fff;
		width: 110rpx;
		text-align: center;
		border-radius: 4px
	}
	.message-list .li .tag1{
		background: #ff5500;
	}
	.message-list .li .tag2{
		background: #0aad15;
	}
	.message-list .li .con{
		color: #999;
	}
	
	.btn-wrap{
		background: #fff;
		height: 90rpx;
		padding: 10rpx 30rpx;
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		box-sizing: border-box;
	}
	.btn-wrap .btn{
		height: 70rpx;
		text-align: center;
		line-height: 70rpx;
		background: #35318f;
		color: #fff;
		border-radius: 100rpx;
	}
</style>
