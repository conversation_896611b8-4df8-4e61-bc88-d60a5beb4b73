<template>
	<view class="container">
		<view class="cover">
			<image bindtap="coverTap" class="cover-image" src="@/static/index.png" background-size="cover"></image>
		</view>
		<view class="prompt-text-view">
			<text class="prompt-text">请靠近设备...</text>
		</view>
	</view>
</template>

<script>
	var aid_list = ['F222222222']
	export default {
		data() {
			return {}
		},
		methods: {
			getHCEState() {
				// 判断设备是否支持NFC
				wx.getHCEState({
					success: function(res) {
						console.log('设备支持NFC:::', res)
					},
					fail: function(res) {
						console.log('设备支持不NFC:::', res)
					}
				})
			},
			startHCE: function() {
				//初始化 NFC 模块
				wx.startHCE({
					aid_list: aid_list,
					success: function(res) {
						console.log('初始化成功', res)
						// 监听NFC设备
						wx.onHCEMessage(function(res) {
							console.log('启动监听', res)
							const buffer = new ArrayBuffer(1)
							const dataView = new DataView(buffer)
							dataView.setUint8(0, 0)
							console.log(buffer)
							if (res.messageType === 1) {
								console.log('send')
								wx.sendHCEMessage({
									data: buffer
								})
							}
						})
					},
					fail: function(res) {
						console.log('初始化失败', res)
					}
				})
			}
		},
		onLoad() {
			this.getHCEState()
		}
	}
</script>

<style>
	@import url("style.css");
</style>
