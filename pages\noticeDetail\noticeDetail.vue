<template>
	<view class="content bg-w border-top">
		<view class="u-p-30 ">
			
			<h1 class="u-font-36">{{info.Title}}</h1>
			<text class="u-font-20">{{info.CreateDatetimeText}}</text>
			</view>
			<u-line />
			<view class="u-p-30">
				<u-parse :content="content"></u-parse>
			</view>
		
	</view>
</template>

<script>
	import uParse from '@/components/u-parse/u-parse.vue'
	export default {
		components: {
			uParse
		},
		data() {
			return {
				info:'',
				content:''
			}
		},
		onLoad(e) {
			console.log(e)
			var NoticeCode=e.NoticeCode
			var IsSee=e.IsSee
			if(e.ProjectCode){
				var api = 'ProjectNoticeMng'
			}
			else{
				var api = 'NoticeMng'
			}
			this.$http.post('/'+api+'/GetNoticeDetail', {
				NoticeCode:NoticeCode,
				UserCode:uni.getStorageSync('UserCode')
			}).then(res => {
				this.info=res.Data
				this.content=res.Data.NoticeContent
				console.log(res.Data.NoticeContent)
			})
			if(IsSee==0){
				uni.$emit("handleFun",{isRead: true});
			}
			
		}
	}
</script>

<style>
	text{color: #999;}
</style>
