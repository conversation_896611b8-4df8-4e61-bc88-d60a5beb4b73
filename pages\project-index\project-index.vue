<template>
  <view class="content border-top">
    <view class="u-page">
      <view class="bg-w">
        <view class="project-index-info flex" style="padding-bottom: 0">
          <view class="flex-hd">
            <image :src="detail.Project.MainImage" mode="aspectFill"></image>
          </view>
          <view class="flex-bd">
            <view class="name">{{ detail.Project.ProjectName }}</view>
            <view class="info">地址：{{ detail.Project.Address == null ? "" : detail.Project.Address }}</view>
            <view class="info">项目负责人：{{ detail.Project.ProjectLeaderUserName }}</view>
          </view>
        </view>
        <view class="index-notic u-border-bottom u-flex u-row-between" style="padding-bottom: 30rpx; margin-bottom: 10rpx">
          <view class="u-padding-right-30 u-border-right">
            <image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/index-notic.png"></image>
          </view>
          <view class="u-flex-nowrap u-flex-12 u-margin-left-26 u-margin-right-26">
            <swiper class="swiper" :autoplay="true" :interval="5000" :duration="500" :vertical="true">
              <swiper-item v-for="(item, index) in indexnotic" :key="index">
                <navigator :url="'../noticeDetail/noticeDetail?NoticeCode=' + item.code + '&ProjectCode=' + ProjectCode">
                  <view class="notic_t">
                    <u-tag class="u-margin-right-10" text="已读" type="info" size="mini" mode="dark" v-if="item.IsSee == 1" />
                    <u-tag class="u-margin-right-10" text="未读" type="error" size="mini" mode="dark" v-else />

                    {{ item.date }}
                  </view>
                  <text class="notic_f">{{ item.title }}</text>
                </navigator>
              </swiper-item>
            </swiper>
          </view>
          <view>
            <u-button type="info" size="medium" @click="moreInfo">查看全部</u-button>
          </view>
        </view>
        <view scroll-x="true" class="project-index-status">
          <block v-if="roleGroup != 'dsf'">
            <!-- 新增 -->
            <view class="i i4" @click="gotoUrl('../qualityIndex/qualityIndex?projectCode=' + ProjectCode + '&projectname=' + detail.Project.ProjectName)">
              <view class="wrap">
                <view class="t t4">品质巡检</view>
                <view class="s">待处理</view>
                <view class="num">{{ newCount.CheckCount + newCount.GridCount }}</view>
              </view>
            </view>
            <view class="i i3" @click="gotoUrl('../equipmentList/equipmentList?projectCode=' + ProjectCode + '&projectname=' + detail.Project.ProjectName + '&Xjnum=' + detail.Xjnum + '&WarnSbnum=' + detail.WarnSbnum)">
              <view class="wrap">
                <view class="t t3">设备巡检</view>
                <view class="s">待处理</view>
                <view class="num">{{ detail.Xjnum }}</view>
              </view>
            </view>
            <view class="i i1" @click="gotoUrl('../safeList/safeList?projectCode=' + ProjectCode + '&projectname=' + detail.Project.ProjectName)">
              <view class="wrap">
                <view class="t t1">安防巡逻</view>
                <view class="s">待处理</view>
                <view class="num">{{ newCount.PatrolCount }}</view>
              </view>
            </view>
            <view class="i i2" @click="gotoUrl('../cleanList/cleanList?projectCode=' + ProjectCode + '&projectname=' + detail.Project.ProjectName)">
              <view class="wrap">
                <view class="t t2">保洁监管</view>
                <view class="s">待处理</view>
                <view class="num">{{ newCount.SuperviseCount }}</view>
              </view>
            </view>
            <view class="i i5" @click="gotoUrl('../docList/docList?projectCode=' + ProjectCode + '&projectname=' + detail.Project.ProjectName)">
              <view class="wrap">
                <view class="t t5">档案管理</view>
                <view class="s">待处理</view>
                <view class="num">{{ newCount.ArchivesCount }}</view>
              </view>
            </view>
            <view class="i i6" @click="gotoUrl('../energyList/energyList?projectCode=' + ProjectCode + '&projectname=' + detail.Project.ProjectName)">
              <view class="wrap">
                <view class="t t6">能耗管理</view>
                <view class="s">待处理</view>
                <view class="num">{{ newCount.MeterCount }}</view>
              </view>
            </view>
            <view class="i i3" @click="gotoUrl('../assetList/assetList?projectCode=' + ProjectCode + '&projectname=' + detail.Project.ProjectName)">
              <view class="wrap">
                <view class="t t3">资产管理</view>
                <view class="num">{{ newCount.AssetCount }}</view>
              </view>
            </view>
            <view class="i i7" @click="gotoUrl('../fzr-foodList/fzr-foodList?type=room&ProjectCode=' + ProjectCode)">
              <view class="wrap">
                <view class="t t7">包厢预定</view>
                <view class="s">待处理</view>
                <view class="num">{{ shitang.boxNum }}</view>
              </view>
            </view>
            <view class="i i6" @click="gotoUrl('../fzr-foodList/fzr-foodList?type=eat&ProjectCode=' + ProjectCode)">
              <view class="wrap">
                <view class="t t6">餐食预留</view>
                <view class="s">待处理</view>
                <view class="num">{{ shitang.reserveNum }}</view>
              </view>
            </view>
            <view class="i i5" @click="gotoUrl('../fzr-foodList/fzr-foodList?type=food&ProjectCode=' + ProjectCode)">
              <view class="wrap">
                <view class="t t5">卤菜外卖</view>
                <view class="s">待处理</view>
                <view class="num">{{ shitang.takeOutNum }}</view>
              </view>
            </view>
            <view class="i i1" @click="gotoUrl('../fzr-workList/fzr-workList?projectCode=' + ProjectCode)">
              <view class="wrap">
                <view class="t t1">工程工单</view>
                <view class="s">待处理</view>
                <view class="num">{{ detail.Gdnum }}</view>
              </view>
            </view>
            <view class="i i2" @click="gotoUrl('../fzr-meetingList/fzr-meetingList?projectCode=' + ProjectCode)">
              <view class="wrap">
                <view class="t t2">会务预约</view>
                <view class="s">待处理</view>
                <view class="num">{{ detail.Hynum }}</view>
              </view>
            </view>
            <view class="i i4" @click="gotoUrl('../fzr-reportList/fzr-reportList?projectCode=' + ProjectCode)">
              <view class="wrap">
                <view class="t t4">工作日报</view>
                <view class="s">已上报</view>
                <view class="num">{{ detail.Rbnum }}</view>
              </view>
            </view>
            <view class="i i3">
              <view class="wrap">
                <view class="t t3">投诉处理</view>
                <view class="s">已上报</view>
                <view class="num">0</view>
              </view>
            </view>
            <view class="i i6">
              <view class="wrap">
                <view class="t t6">物业交费</view>
                <view class="s">待处理</view>
                <view class="num">0</view>
              </view>
            </view>
            <view class="i i5">
              <view class="wrap">
                <view class="t t5">访客管理</view>
                <view class="s">待处理</view>
                <view class="num">0</view>
              </view>
            </view>
            <view class="i i1">
              <view class="wrap">
                <view class="t t1">停车管理</view>
                <view class="s">待处理</view>
                <view class="num">0</view>
              </view>
            </view>
          </block>
          <block v-else>
            <view v-if="UserRemark == '005'" class="i i1" @click="gotoUrl('../fzr-foodList/fzr-foodList?type=food&ProjectCode=' + ProjectCode)">
              <view class="wrap">
                <view class="t t1">卤菜外卖</view>
                <view class="s">待处理</view>
                <view class="num">{{ shitang.takeOutNum }}</view>
              </view>
            </view>
            <view v-if="UserRemark == '006'" class="i i2" @click="gotoUrl('../fzr-foodList/fzr-foodList?type=eat&ProjectCode=' + ProjectCode)">
              <view class="wrap">
                <view class="t t2">餐食预留</view>
                <view class="s">待处理</view>
                <view class="num">{{ shitang.reserveNum }}</view>
              </view>
            </view>
            <view v-if="UserRemark == '007'" class="i i3" @click="gotoUrl('../fzr-foodList/fzr-foodList?type=room&ProjectCode=' + ProjectCode)">
              <view class="wrap">
                <view class="t t3">包厢预定</view>
                <view class="s">待处理</view>
                <view class="num">{{ shitang.boxNum }}</view>
              </view>
            </view>
          </block>
        </view>
        <scroll-view scroll-x="true" class="project-index-tools">
          <navigator class="i" :url="item.page" v-for="(item, index) in appList" v-if="item.show">
            <view>
              <image class="iicon" :src="item.icon"></image>
            </view>
            <text class="name">{{ item.name }}</text>
          </navigator>
          <navigator class="i" url="/pages/404/404">
            <view>
              <image class="iicon" src="../../static/iicon4.png" style="filter: gray; filter: grayscale(100%); -webkit-filter: grayscale(100%); opacity: 0.7"></image>
            </view>
            <text class="name">视频监控</text>
          </navigator>
          <navigator class="i" url="/pages/404/404">
            <view>
              <image class="iicon" src="../../static/iicon3.png" style="filter: gray; filter: grayscale(100%); -webkit-filter: grayscale(100%); opacity: 0.6"></image>
            </view>
            <text class="name">停车缴费</text>
          </navigator>
          <navigator class="i" url="/pages/404/404">
            <view>
              <image class="iicon" src="../../static/iicon6.png" style="filter: gray; filter: grayscale(100%); -webkit-filter: grayscale(100%); opacity: 0.9"></image>
            </view>
            <text class="name">生活超市</text>
          </navigator>
        </scroll-view>
      </view>
      <view class="project-index-list">
        <scroll-view scroll-x="true" class="tab-hd">
          <view :class="'i ' + (index == tabIndex ? 'active' : '')" v-for="(item, index) in tabList" :key="index" @click="getList(item, index)">{{ item }}</view>
        </scroll-view>
        <view class="list">
          <block :key="index" v-for="(item, index) in list">
            <view v-if="nowType == '我的预约'" class="i flex border-all" @click="gotoItem(item.Type, item.Code)">
              <view class="flex-hd image">
                <image v-if="item.Type == '卤菜外卖'" src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_wm.png"></image>
                <image v-else-if="item.Type == '餐食预留'" src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_cs.png"></image>
                <image v-else-if="item.Type == '会议预约'" src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_hy.png"></image>
                <image v-else src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_bx.png"></image>
                <view>{{ item.Type }}</view>
              </view>
              <view class="flex-bd">
                <view class="t">单号：{{ item.OrderNo }}</view>
                <view class="info" v-if="item.Type == '卤菜外卖'">取餐时间：{{ item.BeginTime }}</view>
                <view class="info" v-else-if="item.Type == '餐食预留'">预留时间：{{ item.BeginTime }}</view>
                <view class="info" v-else-if="item.Type == '会议预约'">会议时间：{{ item.BeginTime }}</view>
                <view class="info" v-else>预留时间：{{ item.BeginTime }}</view>
                <view class="info">申请时间：{{ item.ApplyTime }}</view>
              </view>
              <view class="flex-hd">
                <u-button type="default" size="mini" plain disabled v-if="item.ApplyStatus == 0">待审核</u-button>
                <u-button type="success" size="mini" plain disabled v-else-if="item.ApplyStatus == 1">预约成功</u-button>
                <u-button type="warning" size="mini" plain disabled v-else-if="item.ApplyStatus == 2">预约失败</u-button>
                <u-button type="success" size="mini" plain disabled v-else>已完成</u-button>
              </view>
            </view>
            <navigator v-else-if="nowType == '我的报修'" class="i flex border-all" :url="'/yezhu/yz-repairDetail/yz-repairDetail?code=' + item.RepairCode">
              <view class="flex-bd">
                <view class="t">单号：{{ item.RepairNo }}</view>
                <view class="info">维修人：{{ item.applyusername }}</view>
                <view class="info">报修时间：{{ item.applyTime }}</view>
              </view>
              <view class="flex-hd">
                <u-button type="default" size="mini" plain disabled v-if="item.RepairStatus == 0">待维修</u-button>
                <u-button type="warning" size="mini" plain disabled v-if="item.RepairStatus == 3">待评价</u-button>
                <u-button type="success" size="mini" plain disabled v-if="item.RepairStatus == 4">已完成</u-button>
              </view>
            </navigator>
            <navigator v-else-if="nowType == '待处理工单'" class="i flex border-all" :url="roleGroup == 'fzr' || roleGroup == 'admin' ? '../fzr-workDetail/fzr-workDetail?code=' + item.RepairCode + '&ProjectCode=' + ProjectCode : '../yg-workDetail/yg-workDetail?code=' + item.RepairCode + '&ProjectCode=' + ProjectCode">
              <view class="flex-bd">
                <view class="t">单号：{{ item.RepairNo }}</view>
                <view class="info">报修人：{{ item.applyusername }}</view>
                <view class="info" v-if="item.repairusername">接单人：{{ item.repairusername }}</view>
                <view class="info">报修时间：{{ item.ApplyTime }}</view>
              </view>
              <view class="flex-hd">
                <u-button type="default" size="mini" plain disabled v-if="item.RepairStatus == 0">待维修</u-button>
                <u-button type="warning" size="mini" plain disabled v-if="item.RepairStatus == 3">待评价</u-button>
              </view>
            </navigator>
            <view v-else-if="nowType == '待处理预约'" class="i flex border-all" @click="gotoMeeting(item.Code)">
              <view class="flex-bd">
                <view class="t">单号：{{ item.OrderNo }}</view>
                <view class="info">申请人：{{ item.ApplyUserName }}</view>
                <view class="info">会议时间：{{ item.BeginTime }}</view>
                <view class="info">申请时间：{{ item.ApplyTime }}</view>
              </view>
              <view class="flex-hd">
                <u-button type="default" size="mini" plain disabled v-if="item.RepairStatus == 0">待维修</u-button>
                <u-button type="warning" size="mini" plain disabled v-if="item.RepairStatus == 3">待评价</u-button>
              </view>
            </view>
            <navigator v-else-if="nowType == '设备巡检'" class="i flex border-all" :url="'../fzr-checkDetail/fzr-checkDetail?EquipmentCode=' + item.EquipmentCode">
              <view class="flex-bd">
                <view class="t">设备：{{ item.EquipmentNumber }}</view>
                <view class="info">设备：{{ item.EquipmentName }}</view>
                <view class="info">巡检周期：{{ item.InspectionTimeUnit }}</view>
                <view class="info">最后一次巡检：{{ item.LastInspectionTime }}</view>
              </view>
              <view class="flex-hd">
                <u-button type="error" size="mini" pain v-if="item.isoverdue == 1">超期</u-button>
              </view>
            </navigator>

            <view class="i flex border-all" v-else-if="nowType == '卤菜外卖'" @click="gotoUrl('/pages/fzr-foodDetail/fzr-foodDetail?type=卤菜外卖&code=' + item.OutCode + '&ProjectCode=' + ProjectCode)">
              <view class="flex-hd image">
                <image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_wm.png"></image>
                <view>卤菜外卖</view>
              </view>
              <view class="flex-bd">
                <view class="t">单号：{{ item.OrderNo }}</view>
                <view class="info">取餐时间：{{ item.OutTime }}</view>
                <view class="info">申请时间：{{ item.ApplyTime }}</view>
              </view>
              <view class="flex__ft"></view>
            </view>
            <view class="i flex border-all" v-else-if="nowType == '餐食预留'" @click="gotoUrl('/pages/fzr-eatDetail/fzr-eatDetail?type=餐食预留&code=' + item.ReserveCode + '&ProjectCode=' + ProjectCode)">
              <view class="flex-hd image">
                <image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_cs.png"></image>
                <view>餐食预留</view>
              </view>
              <view class="flex-bd">
                <view class="t">单号：{{ item.OrderNo }}</view>
                <view class="info">预留时间：{{ item.ReserveTime }}</view>
                <view class="info">申请时间：{{ item.ApplyTime }}</view>
              </view>
              <view class="flex__ft"></view>
            </view>
            <view class="i flex border-all" v-else @click="gotoUrl('/pages/fzr-roomDetail/fzr-roomDetail?type=包厢预定&code=' + item.BoxCode + '&ProjectCode=' + ProjectCode)">
              <view class="flex-hd image">
                <image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_bx.png"></image>
                <view>包厢预定</view>
              </view>
              <view class="flex-bd">
                <view class="t">单号：{{ item.OrderNo }}</view>
                <view class="info">预留时间：{{ item.FromTime }}</view>
                <view class="info">申请时间：{{ item.ApplyTime }}</view>
              </view>
              <view class="flex__ft"></view>
            </view>
          </block>
        </view>
        <view class="more-btn mr30">
          <u-button size="medium" @click="gotoUrl(nowUrl)">查看全部</u-button>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
const app = getApp();
import utils from "../../components/utils.js";
export default {
  data() {
    return {
      detail: {
        Project: {
          MainImage: "",
          ProjectName: "",
          Address: "",
          ServiceStartDate: "",
          ServiceEndDate: "",
          ProjectLeaderUserName: "",
          OwnerinChargeName: "",
        },
      },
      hostUrl: app.globalData.hostUrl,
      roleGroup: "",
      tabList: "",
      tabIndex: 0,
      list: "",
      nowUrl: "",
      nowType: "",
      ProjectCode: "",
      UserRemark: "",
      shitang: "",
      appList: [
        {
          name: "我要报修",
          icon: "../../static/iicon1.png",
          page: "/yezhu/yz-repairAdd/yz-repairAdd",
          show: false,
        },
        {
          name: "会议室预约",
          icon: "../../static/iicon5.png",
          page: "/yezhu/yz-meetingAdd/yz-meetingAdd",
          show: false,
        },
        {
          name: "卤菜外卖",
          icon: "../../static/iicon2.png",
          page: "/yezhu/yz-foodAdd/yz-foodAdd",
          show: false,
        },
        {
          name: "餐食预留",
          icon: "../../static/iicon7.png",
          page: "/yezhu/yz-eatAdd/yz-eatAdd",
          show: false,
        },
        {
          name: "包厢预定",
          icon: "../../static/iicon8.png",
          page: "/yezhu/yz-roomAdd/yz-roomAdd",
          show: false,
        },
      ],
      indexnotic: [],
      newCount: "", //新增加的模块
    };
  },
  onLoad(e) {
    var ProjectCode = e.ProjectCode;
    this.ProjectCode = ProjectCode;
    var UserRemark = uni.getStorageSync("UserRemark");
    var roleGroup = utils.getRoleGroup(UserRemark);
    this.roleGroup = roleGroup;
    this.UserRemark = UserRemark;
    var IsLeader;
    var tabList;
    if (roleGroup == "admin") {
      tabList = ["待处理工单", "待处理预约", "设备巡检", "卤菜外卖", "餐食预留", "包厢预定", "我的预约", "我的报修"];
    }
    if (UserRemark == "002" || UserRemark == "106" || UserRemark == "107") {
      IsLeader = 1;
      tabList = ["待处理工单", "待处理预约", "设备巡检", "卤菜外卖", "餐食预留", "包厢预定", "我的预约", "我的报修"];
    }
    if (UserRemark == "003") {
      IsLeader = 1;
      tabList = ["待处理预约", "我的预约", "我的报修"];
    }
    if (UserRemark == "008" || UserRemark == "108" || UserRemark == "109" || UserRemark == "110" || UserRemark == "111" || UserRemark == "112") {
      if (UserRemark == "108" || UserRemark == "109" || UserRemark == "110" || UserRemark == "111" || UserRemark == "112") {
        tabList = ["待反馈预约", "我的预约", "我的报修"];
      } else {
        tabList = ["待处理预约", "我的预约", "我的报修"];
      }
    }
    if (UserRemark == "004") {
      IsLeader = 1;
      tabList = ["待反馈预约", "待处理工单", "我的预约", "我的报修"];
    }
    if (UserRemark == "009") {
      tabList = ["待反馈预约", "待处理工单", "我的预约", "我的报修"];
    }
    if (UserRemark == "005") {
      tabList = ["卤菜外卖", "我的预约", "我的报修"];
    }
    if (UserRemark == "006") {
      tabList = ["餐食预留", "我的预约", "我的报修"];
    }
    if (UserRemark == "007") {
      tabList = ["包厢预定", "我的预约", "我的报修"];
    }
    this.tabList = tabList;
    this.$http
      .post("/ProjectMngApi/GetProjectDeatil", {
        ProjectCode,
        UserCode: uni.getStorageSync("UserCode"),
        IsLeader: IsLeader,
      })
      .then((res) => {
        res.Data.Project.ServiceStartDate = res.Data.Project.ServiceStartDate.split("T")[0];
        res.Data.Project.ServiceEndDate = res.Data.Project.ServiceEndDate.split("T")[0];
        if (res.Data.Project.MainImage) {
          res.Data.Project.MainImage = this.hostUrl + res.Data.Project.MainImage;
        } else {
          res.Data.Project.MainImage = "http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/projectimg01.png";
        }
        this.detail = res.Data;
      });
    this.getList(tabList[0], 0, true);
    this.getNum();

    if (uni.getStorageSync("projectCode")) {
      this.$http
        .post("/ProjectMngApi/GetProjectDeatil", {
          UserCode: uni.getStorageSync("UserCode"),
          ProjectCode: uni.getStorageSync("projectCode"),
        })
        .then((res) => {
          console.log(res);
          if (res.Data.Project.EnableService) {
            var EnableService = res.Data.Project.EnableService.split(",");
            if (EnableService.indexOf("设备报修") > -1) {
              this.appList[0].show = true;
            } else {
              this.appList[0].show = false;
            }
            if (EnableService.indexOf("会议预约") > -1) {
              this.appList[1].show = true;
            } else {
              this.appList[1].show = false;
            }
            if (EnableService.indexOf("卤菜外卖") > -1) {
              this.appList[2].show = true;
            } else {
              this.appList[2].show = false;
            }
            if (EnableService.indexOf("餐食预留") > -1) {
              this.appList[3].show = true;
            } else {
              this.appList[3].show = false;
            }
            if (EnableService.indexOf("包厢预定") > -1) {
              this.appList[4].show = true;
            } else {
              this.appList[4].show = false;
            }
          } else {
            this.appList[0].show = false;
            this.appList[1].show = false;
            this.appList[2].show = false;
            this.appList[3].show = false;
            this.appList[4].show = false;
          }
        });

      this.$http
        .get("/PatrolApi/GetCountByProjectCode", {
          params: {
            ProjectCode: ProjectCode,
          },
        })
        .then((res) => {
          this.newCount = res.Data;
        });
    }

    this.getNotice();
  },
  methods: {
    moreInfo() {
      uni.navigateTo({
        url: "../user-notice/user-notice?ProjectCode=" + this.ProjectCode,
      });
    },
    getNotice() {
      this.$http
        .post("/ProjectNoticeMng/GetListAll?p=1&ps=10", {
          UserCode: uni.getStorageSync("UserCode"),
          IsSee: 0,
          ProjectCode: this.ProjectCode,
        })
        .then((res) => {
          console.log(res.Data);
          var list = [];
          for (let index in res.Data) {
            var item = {
              title: res.Data[index].ProjectNotice.Title,
              date: res.Data[index].ProjectNotice.CreateDatetimeText,
              code: res.Data[index].NoticeCode,
              IsSee: res.Data[index].IsSee,
            };
            list.push(item);
          }
          this.indexnotic = list;
        });
    },
    gotoMeeting(e) {
      var UserRemark = uni.getStorageSync("UserRemark");
      var roleGroup = utils.getRoleGroup(UserRemark);
      if (UserRemark == "002" || UserRemark == "003" || UserRemark == "106" || UserRemark == "107" || roleGroup == "admin") {
        var url = "../fzr-meetingDetail/fzr-meetingDetail?code=" + e + "&ProjectCode=" + this.ProjectCode;
      }
      // else if(UserRemark=='003'){
      // 	var url='../fzr-meetingDetail/fzr-meetingDetailzg?code='+e+'&ProjectCode='+this.ProjectCode
      // }
      else {
        var url = "../yg-meetingDetail/yg-meetingDetail?code=" + e + "&ProjectCode=" + this.ProjectCode;
      }
      uni.navigateTo({
        url: url,
      });
    },
    getList(e, index = 0, onload) {
      var UserRemark = uni.getStorageSync("UserRemark");
      if (this.tabIndex == index && !onload) {
        return;
      }
      this.list = [];
      this.tabIndex = index;
      var url = "";
      var parmas = "";
      if (e == "待处理工单") {
        this.nowUrl = "/pages/fzr-workList/fzr-workList?projectCode=" + this.ProjectCode;
        this.nowType = "待处理工单";
        if (this.roleGroup == "fzr" || this.roleGroup == "admin") {
          url = "/RepairMngApi/GetListByProjectCode";
          parmas = {
            RepairNo: "",
            repairstatus: 0,
            RepairUserCode: uni.getStorageSync("UserCode"),
          };
        } else {
          url = "/RepairMngApi/GetMyList";
          parmas = {
            RepairNo: "",
            repairstatus: 1,
            RepairUserCode: uni.getStorageSync("UserCode"),
          };
        }
      } else if (e == "待处理预约" || e == "待反馈预约") {
        this.nowUrl = "/pages/fzr-meetingList/fzr-meetingList?projectCode=" + this.ProjectCode;
        this.nowType = "待处理预约";
        if ((this.roleGroup == "fzr" && UserRemark != "004") || this.roleGroup == "admin") {
          url = "/MeetingMngApi/GetMeetingList";
          parmas = {
            OrderNo: "",
            ApplyStatus: 0,
            UserCode: uni.getStorageSync("UserCode"),
          };
        } else {
          if (UserRemark == "004" || UserRemark == "009" || UserRemark == "108" || UserRemark == "109" || UserRemark == "110" || UserRemark == "111" || UserRemark == "112") {
            var type = "";
            if (UserRemark == 108 || UserRemark == 109) {
              type = 3;
            } else if (UserRemark == 112) {
              type = 1;
            } else if (UserRemark == 110 || UserRemark == 111) {
              type = 4;
            } else {
              type = 2;
            }
            (url = "/MeetingMngApi/GetMeetingListByType"),
              (parmas = {
                OrderNo: "",
                ApplyStatus: 0,
                UserCode: uni.getStorageSync("UserCode"),
                type: type,
              });
          } else {
            (url = "/MeetingMngApi/GetMyMeetingList"),
              (parmas = {
                OrderNo: "",
                ApplyStatus: 0,
                UserCode: uni.getStorageSync("UserCode"),
              });
          }
        }
      } else if (e == "设备巡检") {
        this.nowUrl = "/pages/equipmentList/equipmentList?projectCode=" + this.ProjectCode + "&projectname=" + this.detail.Project.ProjectName;
        this.nowType = "设备巡检";
        url = "/EquipmentMngApi/GetList";
        parmas = {
          InspectorUserCode: "",
        };
      } else if (e == "我的预约") {
        this.nowUrl = "/yezhu/yz-orderList/yz-orderList?projectCode=" + this.ProjectCode;
        this.nowType = "我的预约";
        url = "/MeetingMngApi/GetList";
        parmas = {
          UserCode: uni.getStorageSync("UserCode"),
          ApplyStatus: "",
          OrderNo: "",
        };
      } else if (e == "我的报修") {
        this.nowUrl = "/yezhu/yz-repairList/yz-repairList?projectCode=" + this.ProjectCode;
        this.nowType = "我的报修";
        url = "/RepairMngApi/GetListAll";
        parmas = {
          UserCode: uni.getStorageSync("UserCode"),
          RepairStatus: "",
          RepairNo: "",
        };
      } else if (e == "卤菜外卖") {
        this.nowUrl = "/pages/fzr-foodList/fzr-foodList?type=food&ProjectCode=" + this.ProjectCode;
        this.nowType = "卤菜外卖";
        url = "/VegetablesTakeoutMngApi/GetListByPorject";
        parmas = {
          ApplyStatus: 0,
          OrderNo: "",
        };
      } else if (e == "餐食预留") {
        this.nowUrl = "/pages/fzr-foodList/fzr-foodList?type=eat&ProjectCode=" + this.ProjectCode;
        this.nowType = "餐食预留";
        url = "/ReserveMngApi/GetListByPorject";
        parmas = {
          ApplyStatus: 0,
          OrderNo: "",
        };
      } else {
        this.nowUrl = "/pages/fzr-foodList/fzr-foodList?type=room&ProjectCode=" + this.ProjectCode;
        this.nowType = "包厢预定";
        url = "/BoxMngApi/GetListByPorject";
        parmas = {
          ApplyStatus: 0,
          OrderNo: "",
        };
      }
      parmas.p = 1;
      parmas.ps = 5;
      parmas.projectCode = this.ProjectCode;
      parmas.UserCode = uni.getStorageSync("UserCode");

      this.$http
        .get(url, {
          params: parmas,
        })
        .then((res) => {
          console.log(res);
          for (let index in res.Data) {
            if (res.Data[index].ApplyTime) {
              res.Data[index].ApplyTime = res.Data[index].ApplyTime.replace("T", " ");
            }
            if (res.Data[index].BeginTime) {
              res.Data[index].BeginTime = res.Data[index].BeginTime.replace("T", " ");
            }
            if (res.Data[index].ReserveTime) {
              res.Data[index].ReserveTime = res.Data[index].ReserveTime.replace("T", " ");
            }
            if (res.Data[index].FromTime) {
              res.Data[index].FromTime = res.Data[index].FromTime.replace("T", " ");
            }
            if (res.Data[index].OutTime) {
              res.Data[index].OutTime = res.Data[index].OutTime.replace("T", " ");
            }

            if (res.Data[index].LastInspectionTime) {
              if (res.Data[index].LastInspectionTime.indexOf("0001") == -1) {
                res.Data[index].LastInspectionTime = res.Data[index].LastInspectionTime.replace("T", " ");
              } else {
                res.Data[index].LastInspectionTime = "";
              }
            }
          }
          this.list = res.Data;
        });
    },
    gotoUrl(e) {
      uni.navigateTo({
        url: e,
      });
    },
    gotoItem(type, code) {
      if (type == "卤菜外卖") {
        uni.navigateTo({
          url: "/yezhu/yz-foodDetail/yz-foodDetail?code=" + code,
        });
      } else if (type == "餐食预留") {
        uni.navigateTo({
          url: "/yezhu/yz-eatDetail/yz-eatDetail?code=" + code,
        });
      } else if (type == "会议预约") {
        uni.navigateTo({
          url: "/yezhu/yz-meetingDetail/yz-meetingDetail?code=" + code,
        });
      } else {
        uni.navigateTo({
          url: "/yezhu/yz-roomDetail/yz-roomDetail?code=" + code,
        });
      }
    },
    getNum() {
      this.$http
        .get("/UserApi/GetVariousNum", {
          params: {
            UserCode: uni.getStorageSync("UserCode"),
            ProjectCode: this.ProjectCode,
            ApplyStatus: 0,
            OrderNo: "",
          },
        })
        .then((res) => {
          this.shitang = res.Data;
        });
    },
  },
  onPullDownRefresh() {
    this.getList(this.tabList[0], 0, true);
    setTimeout(function () {
      uni.stopPullDownRefresh();
    }, 1000);
  },
};
</script>
<style>
page {
  background: #f6f6f6;
}

.more-btn {
  text-align: center;
  margin: 30rpx auto;
}
.iicon {
  width: 80rpx;
  height: 80rpx;
}
</style>
