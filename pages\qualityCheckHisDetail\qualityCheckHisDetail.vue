<template>
	<view class="">
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">详情内容</view>
				</view>
			</view>
			<view slot="body">
				<u-form ref="uForm">
					<!-- <u-form-item label="设备名称" label-width="150rpx" :border-bottom="false">
						<u-input v-model="info.EquipmentName" :disabled="true" :border="true" />
					</u-form-item> -->
					<u-form-item label="检查周期" label-width="150rpx" :border-bottom="false">
						<u-input v-model="info.CheckTimeUnit" :disabled="true" :border="true" />
					</u-form-item>
					<u-form-item label="检查内容" label-width="150rpx" :border-bottom="false">
						<u-input v-model="info.CateText==null?'':info.CateText" :disabled="true" :border="true" />
					</u-form-item>
					<!-- <u-form-item label="设备号" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="info.EquipmentNumber" :disabled="true" :border="true" />
					</u-form-item> -->
					
					<u-form-item label="填报时间" label-width="150rpx" :border-bottom="false">
						<u-input v-model="info.ThisTimeText" :disabled="true" :border="true" />
					</u-form-item>	
					
					<u-form-item label="巡逻描述" label-width="150rpx" :border-bottom="false">
						<view>
							<view class="cell" v-for="(item,index) in remarkList">
								<view class="item">{{item.Content}}</view>
								<u-radio-group v-model="item.value">
									<u-radio
										v-for="(i, idx) in item.radio" :key="idx" 
										:name="i.name"
										:disabled="item.value!=i.name"
									>
										{{i.name}}
									</u-radio>
								</u-radio-group>
							</view>
						</view>
					</u-form-item>
					<u-form-item label="备注" label-width="150rpx" :border-bottom="false">
						<u-input v-model="info.CheckRemark" placeholder="暂无内容" type="textarea" :disabled="true" :border="true" />
					</u-form-item>
					<u-form-item label="图片" label-width="150rpx" :border-bottom="false">
						<view class="detail-img-list">
							<image v-for="(img,index) in info.Images" :key="index" :src="img" @click="lookImg(index,info.Images)"></image>
						</view>
					</u-form-item>
					<!-- <u-form-item label="签名" label-width="150rpx" :border-bottom="false">
						<view class="singature">
							<image class="singature-images" :src="hostUrl+info.SignImage"></image>
						</view>
					</u-form-item> -->
				</u-form>
			</view>
		</u-card>
		<view style="height: 1px;"></view>
	</view>
</template>

<script>
	import utils from '../../components/utils.js'
	const app = getApp();
	export default {
		data() {
			return {
				hostUrl:getApp().globalData.hostUrl,
				photoList:[],
				remarkList:[],
				info:{}
			}
		},
		methods: {
			lookImg(index,imgList){
				uni.previewImage({
					current:index,
					urls: imgList
				})
			},
		},
		onLoad(e) {
			console.log(JSON.parse(e.info))
			var info = JSON.parse(e.info)
			var InspectionRemarks = info.QualityCheckInfoRemarks
			var remarkList = []
			for(var i =0;i<InspectionRemarks.length;i++){
				if(InspectionRemarks[i].Result==1){
					var item = {
						Content:InspectionRemarks[i].Content,
						value:'是',
						radio:[
							{
								name: '是',
								disabled: true
							},
							{
								name: '否',
								disabled: true
							}
						]
					}
				}
				else{
					var item = {
						Content:InspectionRemarks[i].Content,
						value:'否',
						radio:[
							{
								name: '是',
								disabled: true
							},
							{
								name: '否',
								disabled: true
							}
						]
					}
				}
				
				remarkList.push(item)
			}
			
			this.remarkList = remarkList
			this.info = info
			
		}
	}
</script>

<style>
	@import url("../fzr-reportList/style.css");
	.qcode{
		padding-right: 30rpx;
	}
	.qcode i{
		font-size: 48rpx;
	}
	.sign{
		background: #F6F6F6;
		padding: 4rpx 10rpx;
		border-radius: 2px;
		text-align: center;
		margin-bottom: 20rpx;
	}
	.singature{
		position: relative;
		height: 200rpx;
		width: 400rpx;
		padding-bottom: 30rpx;
	}
	.singature-images{
		transform:rotate(-90deg);
		position: absolute;
		top: 50%;
		left: 50%;
		margin-left: -100rpx;
		margin-top:-200rpx ;
		height: 400rpx;
		width: 200rpx;
	}
</style>
