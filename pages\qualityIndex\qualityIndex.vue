<template>
	<view class="content border-top">
		<view class="list">
			<view class="flex i" @click="gotoUrl('../secureList/secureList?projectCode='+projectCode+'&projectname='+projectname)">
				<view class="flex-bd">
					安全检查 {{Xjnum1}}
				</view>
				<view class="flex-hd">
					<i class="custom-icon custom-icon-right"></i>
				</view>
			</view>
			<view class="flex i" @click="gotoUrl('../qualityList/qualityList?projectCode='+projectCode+'&projectname='+projectname)">
				<view class="flex-bd">
					质量检查 {{Xjnum2}}
				</view>
				<view class="flex-hd">
					<i class="custom-icon custom-icon-right"></i>
				</view>
			</view>
			
			<view class="flex i" @click="gotoUrl('../gridList/gridList?projectCode='+projectCode+'&projectname='+projectname)">
				<view class="flex-bd">
					网格巡检 {{Xjnum3}}
				</view>
				<view class="flex-hd">
					<i class="custom-icon custom-icon-right"></i>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				projectCode:'',
				projectname:'',
				Xjnum1:0,
				Xjnum2:0,
				Xjnum3:0,
			}
		},
		onLoad(e) {
			this.projectCode = e.projectCode
			this.projectname = e.projectname
			
			this.$http.get('/SafetyCheckApi/GetCountForSafetyCheck', {
				params:{
					projectCode:e.projectCode
				}
			}).then(res => {
				this.Xjnum1 = res.Data.OverdueCount
			})
			
			this.$http.get('/QualityCheckApi/GetCountForQualityCheck', {
				params:{
					projectCode:e.projectCode
				}
			}).then(res => {
				this.Xjnum2 = res.Data.OverdueCount
			})
			
			this.$http.get('/GridCheckApi/GetCountForGridCheck', {
				params:{
					projectCode:e.projectCode
				}
			}).then(res => {
				this.Xjnum3 = res.Data.OverdueCount
			})
		},
		methods: {
			gotoUrl(e) {
				uni.navigateTo({
					url: e
				});
			},
		}
	}
</script>

<style>
	.path{
		height: 100rpx;
		line-height: 60rpx;
		background: #fff;
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		padding: 20rpx;
		z-index: 88;
	}
	/* #ifdef H5 */
	.path{
		top: 44px;
	}
	/* #endif */
	.path::after{
		content: " ";
		position: absolute;
		left: 0;
		bottom: 0;
		right: 0;
		height: 1px;
		border-bottom: 1px solid rgba(0,0,0,0.1);
		color: rgba(0,0,0,0.1);
		-webkit-transform-origin: 0 100%;
		transform-origin: 0 100%;
		-webkit-transform: scaleY(0.5);
		transform: scaleY(0.5);
	}
	.path_blank{
		height: 100rpx;
		line-height: 100rpx;
	}
	.path .i{
		display: inline-block;
		font-size: 28rpx;
	}
	.path .i i{
		display: inline-block;
		color: #999;
		font-size: 24rpx;
		margin: 0 10rpx;
	}
	.path .i.active{
		color: #35318f;
		font-weight: bold;
	}
	.list{
		background: #fff;
		margin-top: 20rpx;
	}
	
	.list .i{
		padding: 30rpx;
		position: relative;
	}
	.list .i::after{
		content: " ";
		position: absolute;
		left: 0;
		bottom: 0;
		right: 0;
		height: 1px;
		border-bottom: 1px solid rgba(0,0,0,0.1);
		color: rgba(0,0,0,0.1);
		-webkit-transform-origin: 0 100%;
		transform-origin: 0 100%;
		-webkit-transform: scaleY(0.5);
		transform: scaleY(0.5);
	}
	.list .i:last-child:after{
		height: 0;
		border: 0;
	}
	.qrcode{
		position: fixed;
		width: 100rpx;
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
		background: #27246C;
		color: #fff;
		right: 30rpx;
		bottom: 30rpx;
		border-radius: 50%;
		z-index: 999;
	}
	.qrcode i{
		font-size: 48rpx;
	}
	
	.action-btn{
		margin-left: 30rpx;
		margin-top: 20rpx;
	}
	.action-btn .i{
		display: inline-block;
		background: #35318f;
		color: #fff;
		padding: 10rpx 30rpx;
		margin-right: 30rpx;
		border-radius: 100px;
		font-size: 24rpx;
	}
</style>
