<template>
	<view class="border-top">
		<view class="pd">
			<u-search placeholder="请输入工号或姓名"  :show-action="true" v-model="keyword" shape="square" @custom="search"></u-search>
		</view>
		<view class="list">
			<view class="i flex u-radio" v-for="(item, index) in list" :key="index" @tap="check(index)">
				<view :class="'flex-hd icon '+(item.checked?'icon_active':'')">
					<u-icon class="u-radio__icon-wrap__icon" :color="item.checked?'#fff':'#999'" size="24" name="checkbox-mark"/>
				</view>
				<view class="flex-hd">
					<image :src="item.headimg"></image>
				</view>
				<view class="flex-bd">
					<view class="name">{{item.username}}<view class="inline" v-if="item.isleader=='是'">主管</view></view>
					<view class="workno" v-if="item.jobnumber">{{item.jobnumber}}</view>
				</view>
			</view>
		</view>
		<!-- <view class="mr30" v-if="!empty">
			<u-loadmore :status="loadmore.status" :icon-type="loadmore.iconType" :load-text="loadmore.loadText" />
		</view> -->
		<u-empty class="u-text-center" v-if="empty" text="暂无内容" margin-top="300"></u-empty>
		<view class="fix-btn">
			<view class="submit" @click="submit">确认选择</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				list: [],
				keyword:'',
				empty:'',
				loadmore:{
					status: 'loading',
					iconType: 'flower',
					loadText: {
						loading: '努力加载中',
						nomore: '已无更多'
					}
				},
				hostUrl:app.globalData.hostUrl,
				value:'',
				checkType:'',
				renyuan:'',
				range:'',
				limit:""
			}
		},
		methods: {
			check(idx){
				var list=this.list
				if(list[idx].disable){
					return
				}
				if(this.range=='notleader'&&list[idx].isleader=='是'){
					return this.$u.toast('主管不能是服务人员');
				}
				
				if(this.checkType!='duoxuan'){
					for(let index in list){
						list[index].checked=false
					}
					list[idx].checked=true
					
				}
				else{
					list[idx].checked=!list[idx].checked
				}
				this.list=list
			},
			//获取列表
			renderList(page){
				var renyuan = this.renyuan	
				if(renyuan==99){
					var api= '/MeetingMngApi/GetSoundEngineerList?p=1&ps=100'
				}
				else{
					var api= '/ProjectMngApi/GetServiceUserList?p=1&ps=100'
				}
				return new Promise((resolve)=>{
					this.$http.get(api, {
						params:{
							ProjectCode:this.ProjectCode,
							ServiceName:this.renyuan,
							searchtext:this.keyword
						}
					}).then(res => {
						if(renyuan==99){
							var res=res.Data
							for(let index in res){
								res[index].username=res[index].UserName
								res[index].JobNumber=res[index].JobNumber
								res[index].usercode=res[index].UserCode
								
							}
						}
						else{
							
							var res=JSON.parse(res.Data)
						}
						console.log(res)
						var lists=[]
						for(let index in res){
							res[index].checked=false
							if(this.range=='notleader'){
								if(res[index].isleader!='是'){
									lists.push(res[index])
								}
								
							}
							else{
								lists.push(res[index])
							}
							
						}
						resolve(lists)
					})
				})		
			},
			search(value) {
				this.renderList().then(res=>{
					this.list=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
			},
			submit(){
				var selectedlist = [];
				this.list.forEach((item, index) => {
					if (item.checked == true) {
						selectedlist.push({"name":item.username,"usercode":item.usercode,"isleader":item.isleader,"jobnumber":item.jobnumber,"HeadImg":item.headimg});
					}
				});
				console.log(selectedlist)
				uni.$emit("handleFun",{selectedlist: selectedlist});
				uni.navigateBack();
			}
		},
		onLoad(e) {
			console.log(e)
			this.ProjectCode=e.ProjectCode
			this.checkType=e.type
			this.renyuan=e.renyuan
			this.range=e.range
			if(e.limit){
				var limit=JSON.parse(e.limit)
				this.limit=limit
			}
			this.renderList().then(res=>{
				console.log(res)
				var radioList=[]
				if(res.length<10){
					this.loadmore.status='nomore'
				}
				for(let index in res){
					
					if(res[index].headimg){
						res[index].headimg=this.hostUrl+res[index].headimg
					}
					else{
						res[index].headimg='http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png'
					}
				}
				
				if(limit){
					var res = res.filter(function (x) {
						for(var i=0;i<limit.length;i++){
							console.log(limit[i],x.usercode)
							if(limit[i]==x.usercode){
								x.checked=true
								x.disable=true
							}
						}
					    return x
					});
				}
								
				this.list=res
				if(res[0]){
					this.value=res[0].username
				}
			
			})
		}
	}
</script>

<style>
	@import url("style.css");
	.u-checkbox{
		width: 100%;
	}
</style>
