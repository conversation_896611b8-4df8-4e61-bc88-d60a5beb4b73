page{
	background: #fff;
}
.pd{
	padding: 20rpx 20rpx 0;
	background: #fff;
}
.i image{
	width: 120rpx;
	height: 120rpx;
	vertical-align: top;
	margin-right: 30rpx;
	border-radius: 50%;
	border: 1px solid #eee;
	margin-left: 30rpx;
}
.list{
	padding: 30rpx 0;
}
.u-radio{
	padding-left: 30rpx;
	padding-right: 30rpx;
	padding-bottom: 30rpx;
	margin-bottom: 30rpx;
	border-bottom: 1px solid #eee;
}
.fix-btn{
	height: 120rpx;
}
.fix-btn .submit{
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100%;
	line-height: 90rpx;
	height: 90rpx;
	text-align: center;
	color: #fff;
	background: #35318f;
}
.name{
	
}
.workno{
	color: #999;
	font-size: 24rpx;
}
.name .inline{
	font-size: 24rpx;
	background: #9a551d;
	color: #fff;
	margin-left: 10rpx;
	border-radius: 4px;
	padding: 0 6rpx;
}

.flex-hd.icon{
	width: 40rpx;
	line-height: 40rpx;
	text-align: center;
	height: 40rpx;
	border-radius:1000px;
	background: #ddd;
}
.flex-hd.icon_active{
	background:#27246c;
}