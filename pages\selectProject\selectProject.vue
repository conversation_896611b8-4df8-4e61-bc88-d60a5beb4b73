<template>
	<view class="content bort border-top">
		<view class="fixed-box-100">
			<view class="bg-w u-p-l-30 u-p-r-30 u-p-t-20 u-p-b-20 search-fixed">
				<u-search placeholder="请输入项目单位名称" v-model="keyword" :show-action="false" @blur="search" @clear="reset"></u-search>
				<u-line />
			</view>
		</view>
		<u-empty text="暂无项目" mode="list" :margin-top="150" :show="list_empty"></u-empty>
		<u-empty text="无此关键词项目" mode="search" :margin-top="150" :show="sea_empty"></u-empty>
		<view class="bg-w">
			<u-checkbox-group @change="projectSelect" class="select-list" shape="circle" wrap="true" width="100%" :active-color="ThemeColor">
				<u-checkbox v-for="(item, index) in list" :key="index" v-model="item.checked" >
					{{ item.ProjectName }}
					<text>地址：{{ item.Address }}</text>
					<u-line />
				</u-checkbox>
			</u-checkbox-group>
		</view>	
		<view class="fixed-box-100">
			<view class="btn-foot-fixed"><u-button @click="submit" :ripple="true" type="primary" shape="circle" :custom-style="customStyle">确认选择</u-button></view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			val: 0,
			sea_empty: false,
			list_empty: false,
			ThemeColor: '#000',
			keyword: '',
			list: [],
			listData:[],
			customStyle:''
		};
	},
	onLoad() {
		this.ThemeColor = getApp().globalData.ThemeColor;

		this.$http.post('/LoginMng/GetProjectForSelect', {			
		}).then(res => {
			var list=res.Data
			if(list.length==0){
				this.list_empty = true
			}
			for(let index in list) {  
				list[index].checked=false 
			}		
			this.list=list
			this.listData=list
		})
		
	},
	
	methods: {	
		search() {
			//搜索
			var keyword = this.keyword;
			if (keyword) {				
				var newlist = this.listData.filter(function (x) {
					console.log(x.ProjectName,keyword)
					if(x.ProjectName.indexOf(keyword)>-1){
						return x
					}					
				});
				if (newlist.length == 0) {
					this.sea_empty = true;
				}
				this.list = newlist;
			}
		},
		//清空搜索
		reset() {
			this.sea_empty = false;
			this.list = this.listData;
		},
		
		submit() {
			var selectedlist = [];
			this.list.forEach((item, index) => {
				if (item.checked == true) {
					selectedlist.unshift({"projectcode":item.ProjectCode,"projectname":item.ProjectName,"onshow":true});
				}
			});
			uni.$emit("handleFun",{selectedlist: selectedlist});
			uni.navigateBack();
		},
		projectSelect(e){
			console.log(e)
		}
	}
};
</script>

<style></style>
