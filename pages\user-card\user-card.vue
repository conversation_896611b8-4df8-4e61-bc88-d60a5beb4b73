<template >
	<view class="content" >
		<swiper class="swiper" :indicator-dots="true" indicator-active-color="#fff" indicator-color="rgba(255,255,255,.5)" :autoplay="true" :interval="5000" :duration="500" >
			<swiper-item class="u-text-center u-flex" v-for="(item,index) in list" :key="index">
				<block >
					<view class="u-flex-10">
					<h1 class="card_title u-p-b-50">{{item.ProjectName}}</h1>						
						<view class="card">				
							<image v-if="HeadImg" :src="HeadImgUrl" style="width: 600rpx;height: 600rpx;overflow: hidden;"></image>
							<view v-else style="width: 600rpx;height: 600rpx;overflow: hidden;">
								<open-data type="userAvatarUrl"></open-data>  
							</view> 
							<view class="u-flex u-p-30 bg-w">
								<view class="u-flex-9 u-text-left">
									<h4>{{UserName}}</h4>
									<text>{{roleName}}</text>
									<text v-if="UserType==2&&JobNumber">工号：{{JobNumber}}</text>
								</view>
								<view class="u-flex-3">
									<u-image mode="widthFix" src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/user_logo.png"></u-image>
								</view>
							</view>
						</view>
					</view>
				</block>
			</swiper-item>
		</swiper>
	</view>
</template>

<script>
	const app = getApp();
	import utils from "../../components/utils.js"
	export default {
		data() {
			return {	
				UserName:'',
				list:'',
				UserType:'',
				roleName:'',
				JobNumber:'',
				hostUrl:app.globalData.hostUrl,
				HeadImg:'',
				HeadImgUrl:'',
				roleGroup:''
			}
		},
		onLoad() {
			this.UserName=uni.getStorageSync('UserName')
			this.UserType=uni.getStorageSync('UserType')
			this.roleName=uni.getStorageSync('roleName')
			this.JobNumber=uni.getStorageSync('JobNumber')
			this.HeadImg=uni.getStorageSync('HeadImg')
			this.HeadImgUrl=this.hostUrl+uni.getStorageSync('HeadImg')
			var roleGroup=utils.getRoleGroup(uni.getStorageSync('UserRemark'))
			console.log(roleGroup)
			this.roleGroup=roleGroup
			var url
			if(roleGroup=='fzr'||roleGroup=='admin'){
				url='/HomepageMng/GetProjectMain'
			}
			else if(roleGroup=='fw'){
				url='/HomepageMng/GetMyProject'
			}
			else if(roleGroup=='dsf'){
				url='/HomepageMng/GetProjectByChargeUser'
			}
			else{
				url='/HomepageMng/GetProjectAll'
			}
			this.$http.get(url, {
				params:{
					UserCode:uni.getStorageSync('UserCode')
				}
			}).then(res => {
				var list=JSON.parse(res.Data)
				this.list=list
			})

		},
		methods: {
			
		}
	}
</script>

<style >
	image{
		vertical-align: top;
	}
.content{background-image: url(https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/user-card.jpg) ;background-size:cover;}
.swiper{width: 100%;height:100vh;}
.card_title{color: #fff;font-size:36rpx;line-height:50rpx;font-weight: bold;}
.card{margin:0 10vw 5vw  10vw;border-radius:20rpx;position: relative;}
.card >u-image{border-radius:20rpx 20rpx 0 0;overflow: hidden;display: block;background: #fff;}
.card .u-flex{border-radius:0 0 20rpx 20rpx;overflow:hidden;}
.card .u-flex-9 h4{font-size:36rpx;line-height:60rpx;}

.card .u-flex-9 text{font-size:24rpx;line-height: 50rpx;color: #999;display: block;}
.card::before{display: block;content: "";width:90%;border-radius:0 0 20rpx 20rpx;background: rgba(255,255,255,.5);height: 40rpx;bottom: -20rpx;position: absolute;left: 5%;}
.card::after{display: block;content: "";width:80%;border-radius:0 0 20rpx 20rpx;background: rgba(255,255,255,.5);height:60rpx;bottom: -40rpx;position: absolute;left: 10%;}
</style>
