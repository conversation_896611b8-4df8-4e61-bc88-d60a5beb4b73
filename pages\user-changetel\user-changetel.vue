<template>
	<view class="content border-top">
		<view class="bg-w u-p-l-30 u-p-r-30">
			<u-form :model="model" :rules="rules" ref="uForm" :errorType="errorType">				
				<u-form-item label="原手机号" prop="phone" label-width="150">					
					<u-input placeholder="请输入原手机号" v-model="model.oldphone" type="number" disabled></u-input>
				</u-form-item>
				<u-form-item label="验证码" prop="OldSmsCode" label-width="150">
					<u-input placeholder="请输入验证码" v-model="model.OldSmsCode" type="text"></u-input>
					<u-button slot="right" type="primary" size="mini" :plain="true" @click="getCode1">{{codeTips1}}</u-button>
				</u-form-item>
				<u-form-item label="新手机号" prop="NewPhone" label-width="150">					
					<u-input placeholder="请输入新手机号" v-model="model.NewPhone" type="number"></u-input>
				</u-form-item>
				<u-form-item label="验证码" prop="NewSmsCode" label-width="150">
					<u-input placeholder="请输入验证码" v-model="model.NewSmsCode" type="text"></u-input>
					<u-button slot="right" type="primary" size="mini" :plain="true" @click="getCode2">{{codeTips2}}</u-button>
				</u-form-item>
			</u-form>
		</view>	
			
		<view class="u-m-80">
			<u-button type="primary" shape="circle" @click="submit">确认提交</u-button>
		</view>
		<u-verification-code seconds="60" ref="uCode1" @change="codeChange1"></u-verification-code>
		<u-verification-code seconds="60" ref="uCode2" @change="codeChange2"></u-verification-code>
	</view>
</template>

<script>
export default {
	data() {
		return {		
			model:{
				oldphone:'',
				OldSmsCode:'',
				NewPhone:'',
				NewSmsCode:''
			},		
			codeTips1: '获取验证码',
			codeTips2: '获取验证码',
			rules: {
				NewPhone: [
					{
						required: true,
						message: '请输入手机号',
						trigger: ['change', 'blur']
					},
					{
						validator: (rule, value, callback) => {
							return this.$u.test.mobile(value);
						},
						message: '手机号码不正确',
						// 触发器可以同时用blur和change，二者之间用英文逗号隔开
						trigger: ['change', 'blur']
					}
				],
				OldSmsCode: [
					{
						required: true,
						message: '请输入验证码',
						trigger: ['change', 'blur']
					},
					{
						type: 'number',
						message: '验证码只能为数字',
						trigger: ['change', 'blur']
					}
				],
				NewSmsCode: [
					{
						required: true,
						message: '请输入验证码',
						trigger: ['change', 'blur']
					},
					{
						type: 'number',
						message: '验证码只能为数字',
						trigger: ['change', 'blur']
					}
				]
			},
			errorType: ['message']
		};
	},
	onLoad() {
		this.model.oldphone=uni.getStorageSync('CellPhone')
	},
	onReady() {
		this.$refs.uForm.setRules(this.rules);
	},
	methods: {
		// 获取验证码
		getCode1() {
			if (this.$refs.uCode1.canGetCode) {
				
				this.$http.post('/LoginMng/GetSms', {
					CellPhone: this.model.oldphone
				}).then(res => {
					this.$u.toast('验证码已发送');
					// 通知验证码组件内部开始倒计时
					this.$refs.uCode1.start();			
				})
				
			} else {
				this.$u.toast('倒计时结束后再发送');
			}
		},
		codeChange1(text) {
			this.codeTips1 = text;
		},
		// 获取验证码
		getCode2() {
			if (this.$refs.uCode2.canGetCode) {
				if (!this.model.NewPhone) return this.$u.toast('请输入新手机号');
				if(!this.$u.test.mobile(this.model.NewPhone)) return this.$u.toast('请输入正确的手机号');
				this.$http.post('/LoginMng/GetSms', {
					CellPhone: this.model.NewPhone
				}).then(res => {
					this.$u.toast('验证码已发送');
					// 通知验证码组件内部开始倒计时
					this.$refs.uCode2.start();			
				})
				
			} else {
				this.$u.toast('倒计时结束后再发送');
			}
		},
		codeChange2(text) {
			this.codeTips2 = text;
		},
		submit(){
			this.$refs.uForm.validate(valid => {
				if (valid) {
					this.$http.post('/UserApi/UpdatePhone', {
						UserCode: uni.getStorageSync('UserCode'),
						OldSmsCode:this.model.OldSmsCode,
						NewPhone: this.model.NewPhone,
						NewSmsCode:this.model.NewSmsCode
					}).then(res => {
						this.$u.toast(res.ErrMsg);
						if(res.ErrCode==101){
							uni.setStorageSync('CellPhone',this.model.NewPhone)
							setTimeout(function(){
								uni.navigateBack({
									delta: 1
								});
							},1000)
						}	
					})
				}
			})
		}
	}
};
</script>

<style></style>
