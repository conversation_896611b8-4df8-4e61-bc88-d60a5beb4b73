<template>
	<view class="content border-top">
		<view class="bg-w u-p-l-30 u-p-r-30">

			<u-form :model="form" :rules="rules" ref="uForm" :errorType="errorType">
				<u-form-item label-width="140" label="真实姓名" prop="UserName">
					<u-input placeholder="请输入真实姓名" v-model="form.UserName" type="text"></u-input>
				</u-form-item>
				<u-form-item v-if="form.UserType==2" label-width="140" label="员工工号" prop="JobNumber">
					<u-input placeholder="请输入员工工号" v-model="form.JobNumber" type="text"></u-input>
				</u-form-item>
				<u-form-item label="性别" label-width="140">
					<u-radio-group active-color="#27246c" v-model="value">
						<u-radio @change="radioChange" v-for="(item, index) in checkList" :key="index" :name="item.name" :disabled="item.disabled">
							{{item.name}}
						</u-radio>
					</u-radio-group>
				</u-form-item>
				<u-form-item label-width="140" label="头像" prop="HeadImg">
					<image :src="src" class="avatar"></image>
					<u-button slot="right" @click="chooseAvatar" size="mini">选择图片</u-button>
				</u-form-item>
			</u-form>
		</view>

		<view class="u-m-80">
			<u-button type="primary" shape="circle" @click="submit">确认提交</u-button>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				action: app.globalData.uploadUrl,
				hostUrl:app.globalData.hostUrl,
				form: {
					UserName: '',
					JobNumber: '',
					UserCode: '',
					UserType: '',
					Gender: '',
					HeadImg: ''
				},
				checkList: [{
						name: '男',
						disabled: false
					},
					{
						name: '女',
						disabled: false
					}
				],
				fileList: [],
				value: '',
				src:''
			};
		},
		onLoad() {
			this.form.UserName = uni.getStorageSync('UserName')
			this.form.JobNumber = uni.getStorageSync('JobNumber')
			this.form.UserCode = uni.getStorageSync('UserCode')
			this.form.UserType = uni.getStorageSync('UserType')
			this.form.Gender = uni.getStorageSync('Gender')
			this.value = uni.getStorageSync('Gender')
			if(uni.getStorageSync('HeadImg')){
				this.src=this.hostUrl+uni.getStorageSync('HeadImg')
			}
		},
		created() {
			// 监听从裁剪页发布的事件，获得裁剪结果
			uni.$on('uAvatarCropper', path => {
				this.avatar = path;
				// 可以在此上传到服务端
				uni.uploadFile({
					url: this.action,
					filePath: path,
					header:{
						Authorization: 'Bearer ' + uni.getStorageSync('access_token')
					},
					name: 'file',
					complete: (res) => {
						console.log(res);
						var img=JSON.parse(res.data).RetValue
						this.src=this.hostUrl+img
						this.form.HeadImg=img
					}
				});
			})
		},
		methods: {
			radioChange(e) {
				this.form.Gender = e
			},
			selectProject() {
				uni.navigateTo({
					url: '/pages/selectProject/selectProject'
				})
			},
			submit() {
				if (!this.form.UserName) {
					return this.$u.toast('请输入姓名');
				}
				if (this.form.UserType == 2 && !this.form.JobNumber) {
					return this.$u.toast('请输入工号');
				}
				if (!this.form.Gender) {
					return this.$u.toast('请选择性别');
				}
				if (!this.form.HeadImg) {
					return this.$u.toast('请上传头像');
				}
				this.$http.post('/UserApi/UpdateUserInfo', this.form).then(res => {
					this.$u.toast(res.ErrMsg);
					if (res.ErrCode == 101) {
						uni.setStorageSync('UserName', this.form.UserName)
						uni.setStorageSync('JobNumber', this.form.JobNumber)
						uni.setStorageSync('Gender', this.form.Gender)
						uni.setStorageSync('HeadImg', this.form.HeadImg)
						setTimeout(function() {
							uni.navigateBack({
								delta: 1
							});
						}, 1000)
					}
				})
			},

			chooseAvatar() {
				// 此为uView的跳转方法，详见"文档-JS"部分，也可以用uni的uni.navigateTo
				
				this.$u.route({
					// 关于此路径，请见下方"注意事项"
					url: '/uview-ui/components/u-avatar-cropper/u-avatar-cropper',
					// 内部已设置以下默认参数值，可不传这些参数
					params: {
						// 输出图片宽度，高等于宽，单位px
						destWidth: 300,
						// 裁剪框宽度，高等于宽，单位px
						rectWidth: 300,
						// 输出的图片类型，如果'png'类型发现裁剪的图片太大，改成"jpg"即可
						fileType: 'jpg',
					}
				})
			}
		}
	};
</script>

<style>
	image{
		vertical-align: top;
	}
	.avatar{
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
	}
</style>
