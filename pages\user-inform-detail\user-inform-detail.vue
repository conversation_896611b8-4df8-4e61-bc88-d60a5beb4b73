<template>
	<view class="content bg-w border-top">
		<view class="u-p-30 ">
			
			<h1 class="u-font-36">{{info.MsgTitle}}</h1>
			<text class="u-font-20">{{info.CreateDateTime}}</text>
			</view>
			<u-line />
			<view class="u-p-30">
				<u-parse :content="info.MsgContent"></u-parse>
			</view>
		
	</view>
</template>

<script>
	import uParse from '@/components/u-parse/u-parse.vue'
	export default {
		components: {
			uParse
		},
		data() {
			return {
				info:''
			}
		},
		onLoad(e) {
			console.log(e)
			var MsgCode=e.MsgCode
			this.$http.get('/UserApi/GetMessageDetail', {
			 params: {	MsgCode:MsgCode}
			}).then(res => {
				this.info=res.Data
			})
			
		}
	}
</script>

<style>
	text{color: #999;}
</style>
