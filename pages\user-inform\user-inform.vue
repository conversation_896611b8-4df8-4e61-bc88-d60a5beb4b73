<template>
	<view class="content border-top">
		<view class="bg-w">
			<u-checkbox-group @change="checkboxGroupChange" class="message-list" shape="circle" wrap="true" width="100%"
			 :active-color="ThemeColor">
				<u-cell-item v-for="(item, index) in list" :key="item.id">
					<view class="u-flex">
						<u-checkbox class="u-flex-1" v-show="checkbox_show" @change="checkboxChange" :key="item.id" v-model="item.checked"></u-checkbox>
						<u-cell-item class="u-flex-10" @click="goToPage('/pages/user-inform-detail/user-inform-detail?MsgCode='+item.code)" :title="item.title"
						 :label="item.date" :arrow="false" :border-bottom="flase">							
						</u-cell-item>
					</view>
				</u-cell-item>
			</u-checkbox-group>
		</view>
		<u-empty class="u-text-center" v-if="empty" text="暂无内容" mode="message" margin-top="300"></u-empty>
		<view class="mr30" v-if="!empty">
			<u-loadmore :status="loadmore.status" :icon-type="loadmore.iconType" :load-text="loadmore.loadText" />
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				list: [],
				empty:false,
				page:1,
				loadmore:{
					status: 'loading',
					iconType: 'flower',
					loadText: {
						loading: '努力加载中',
						nomore: '已无更多'
					}
				}
			};
		},
		onLoad() {
			this.renderList(1).then(res=>{
				console.log(res)
				this.list=res
				if (res.length < 10) {
					this.loadmore.status = 'nomore';
				}
			})
		},
		methods: {
			renderList(page){
				return new Promise((resolve)=>{
					this.$http.post('/UserApi/GetMessageList?p='+page+'&ps=10', {
						//UserCode:uni.getStorageSync('UserCode'),
						UserCode:'15962750802'
					}).then(res => {
						if(page==1&res.Data.length==0){
							this.empty=true
						}
						else{
							this.empty=false
						}
						var list=[]
						for(let index in res.Data){
							var item={
								title:res.Data[index].Title,
								date:res.Data[index].CreateDateTime,
								code:res.Data[index].MsgCode
							}
							list.push(item)
						}
						resolve(list)
					})
				})		
			},

			goToPage(e) {
				uni.navigateTo({
					url: e
				});
			}
		},
		onReachBottom() {
			if(this.loadmore.status=='nomore'){
				return
			}
			var list=this.list
			this.renderList(this.page+1).then(res=>{
				for(let index in res){
					list.push(res[index])
				}
				if(res.length>0){
					this.list=list
					this.page++
				}
				else{
					this.loadmore.status='nomore'
				}
			})
		}
	}
</script>

<style>
	.message-list .u-cell {
		padding: 16rpx 30rpx !important;
	}

	.message-list .u-badge {
		right: auto !important;
		left: 0rpx;
		top: 50rpx !important;
	}

	.message-list .u-cell_title {
		padding-left: 20rpx !important;
	}
</style>
