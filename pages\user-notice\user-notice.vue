<template>
	<view class="content">
		<view class="fixed-box-100" style="margin-bottom: 20rpx;">
			<view class="search-fixed border-top">
				<view class="u-flex u-text-center u-p-30 bg-w">
					<view class="u-flex-3" @click="getSee(1)">
						已读
						<u-tag class="u-m-l-10" type="info " size="mini" mode="dark" :text="readnum" shape="circle" />
					</view>
					<view class="u-flex-3" @click="getSee(0)">
						未读
						<u-tag class="u-m-l-10" type="error" size="mini" mode="dark" :text="notreadnum" shape="circle" />
					</view>
					<view class="u-flex-4" @click="Bulk('ready_show',ready_show)">
						{{ readyshow_btn }}
					</view>
					<view class="u-flex-2">
						<u-icon name="shanchu" custom-prefix="custom-icon" size="36" color="#000" @click="Bulk('del_show',del_show)"></u-icon>
					</view>
				</view>
			</view>
		</view>

		<u-empty class="u-text-center" v-if="empty" text="暂无内容" mode="message" margin-top="300"></u-empty>

		<view class="bg-w">
			<u-checkbox-group class="message-list" shape="circle" wrap="true" width="100%"
			 :active-color="ThemeColor">
				<u-cell-item v-for="(item, index) in list" :key="index">
					<view class="u-flex">
						<u-checkbox class="u-flex-1" v-show="checkbox_show" :key="item.id" v-model="item.checked"></u-checkbox>
						<u-cell-item class="u-flex-10" @click="goToPage('/pages/noticeDetail/noticeDetail?NoticeCode='+item.code+'&IsSee='+item.IsSee+'&ProjectCode='+ProjectCode)" :title="item.title"
						 :label="item.date" :arrow="false" :border-bottom="flase">
							<u-badge :type="item.IsSee==0?'error':'info'" :absolute="true" :is-dot="true" :show-zero="true" :is-center="true" size="mini"></u-badge>
						</u-cell-item>
					</view>
				</u-cell-item>
			</u-checkbox-group>
		</view>
		<view class="mr30" v-if="!empty">
			<u-loadmore :status="loadmore.status" :icon-type="loadmore.iconType" :load-text="loadmore.loadText" />
		</view>
		<view class="fixed-box-100" v-show="ready_show">
			<view class="btn-foot-fixed">
				<u-button @click="submit" :ripple="true" type="primary" shape="circle" :custom-style="customStyle">确认标记</u-button>
			</view>
		</view>
		<view class="fixed-box-100" v-show="del_show">
			<view class="btn-foot-fixed">
				<u-button @click="del" :ripple="true" type="primary" shape="circle" :custom-style="customStyle">确认删除</u-button>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				checkbox_show: false,
				ready_show: false,
				del_show: false,
				readyshow_btn: "标记已读",
				list: [{
					checked:false,
					IsSee:0,
					date:'2024-01-01',
					title:'关于物业进行安全管理的通知'
				}],
				empty:false,
				page:1,
				loadmore:{
					status: 'nomore',
					iconType: 'flower',
					loadText: {
						loading: '努力加载中',
						nomore: '已无更多'
					}
				},
				readnum:0,
				notreadnum:1,
				IsSee:'',
				ProjectCode:''
			};
		},
		onLoad(e) {
			if(e.ProjectCode){
				this.ProjectCode = e.ProjectCode
			}
			// this.renderList(1).then(res=>{
			// 	this.list=res
			// 	if(res.length<10){
			// 		this.loadmore.status='nomore'
			// 	}
			// })
			
			// this.getReadNum()
		},
		onShow() {
			var that=this
			uni.$on('handleFun', res => {
				if(res.isRead){
					that.getReadNum()
					that.renderList(1,that.IsSee).then(res=>{
						that.list=res
						that.page=1
						if(res.length<10){
							that.loadmore.status='nomore'
						}
						else{
							that.loadmore.status='loading'
						}
						
					})
				}	
				// 清除监听
				uni.$off('handleFun');
			});
		},
		methods: {
			getReadNum(){
				if(this.ProjectCode){
					var api = 'ProjectNoticeMng'
				}
				else{
					var api = 'NoticeMng'
				}
				this.$http.post('/'+api+'/GetNoticeNum', {
					UserCode:uni.getStorageSync('UserCode'),
					ProjectCode:this.ProjectCode
				}).then(res => {
					this.readnum=res.Data.readnum
					this.notreadnum=res.Data.notreadnum
				})
			},
			getSee(IsSee){
				this.IsSee=IsSee
				this.renderList(1,IsSee).then(res=>{
					this.list=res
					this.page=1
					if(res.length<10){
						this.loadmore.status='nomore'
					}
					else{
						this.loadmore.status='loading'
					}
					
				})
			},
			renderList(page,IsSee=''){
				if(this.ProjectCode){
					var api = 'ProjectNoticeMng'
				}
				else{
					var api = 'NoticeMng'
				}
				return new Promise((resolve)=>{
					this.$http.post('/'+api+'/GetListAll?p='+page+'&ps=10', {
						ProjectCode:this.ProjectCode,
						UserCode:uni.getStorageSync('UserCode'),
						IsSee:IsSee
					}).then(res => {
						if(page==1&res.Data.length==0){
							this.empty=true
						}
						else{
							this.empty=false
						}
						
						var list=[]
						for(let index in res.Data){
							if(this.ProjectCode){
								var item={
									title:res.Data[index].ProjectNotice.Title,
									date:res.Data[index].ProjectNotice.CreateDatetimeText,
									code:res.Data[index].NoticeCode,
									IsSee:res.Data[index].IsSee,
									checked:false
								}
							}
							else{
								var item={
									title:res.Data[index].Notice.Title,
									date:res.Data[index].Notice.CreateDatetimeText,
									code:res.Data[index].NoticeCode,
									IsSee:res.Data[index].IsSee,
									checked:false
								}
							}
							
							list.push(item)
						}
						resolve(list)
					})
				})		
			},
			//标记已读
			Bulk(a, e) {
				var that = this;
				if (a == "ready_show") {
					that.del_show = false;
					if (e == false) {
						that.checkbox_show = true;
						that.ready_show = true;
						that.readyshow_btn = "取消标记"

					} else {
						that.checkbox_show = false;
						that.ready_show = false;
						that.readyshow_btn = "标记已读"
					}
				} else if (a == "del_show") {
					that.ready_show = false;
					that.readyshow_btn = "标记已读"
					if (e == false) {
						that.checkbox_show = true;
						that.del_show = true;

					} else {
						that.checkbox_show = false;
						that.del_show = false;
					}

				}

			},

			goToPage(e) {
				uni.navigateTo({
					url: e
				});
			},
			del(){
				var list=this.list
				var selected=[]
				for(let index in list){
					if(list[index].checked==true){
						selected.push(list[index].code)
					}
				}
				if(!selected[0]){
					return this.$u.toast('请选择删除项');
				}
				if(this.ProjectCode){
					var api = 'ProjectNoticeMng'
				}
				else{
					var api = 'NoticeMng'
				}
				this.$http.post('/'+api+'/DeleteNotice', {
					UserCode:uni.getStorageSync('UserCode'),
					NoticeCodes:selected.toString()
				}).then(res => {
					this.renderList(1).then(res=>{
						this.list=res
						if(res.length<10){
							this.loadmore.status='nomore'
						}
					})
					
					this.getReadNum()
					
					this.Bulk('del_show',this.del_show)
					
					uni.$emit('update',{noticeUpdate:true})
				})
				
			},
			submit(){
				var list=this.list
				var selected=[]
				for(let index in list){
					if(list[index].checked==true){
						selected.push(list[index].code)
					}
				}
				if(this.ProjectCode){
					var api = 'ProjectNoticeMng'
				}
				else{
					var api = 'NoticeMng'
				}
				this.$http.post('/'+api+'/ChangeNoticeStatus', {
					UserCode:uni.getStorageSync('UserCode'),
					NoticeCodes:selected.toString()
				}).then(res => {
					this.renderList(1).then(res=>{
						this.list=res
						if(res.length<10){
							this.loadmore.status='nomore'
						}
					})
					
					this.getReadNum()
					this.Bulk('ready_show',this.ready_show)
				})
			}
		},
		
		onReachBottom() {		
			if(this.loadmore.status=='nomore'){
				return
			}
			var list=this.list
			this.renderList(this.page+1).then(res=>{
				for(let index in res){
					list.push(res[index])
				}
				if(res.length>0){
					this.list=list
					this.page++
				}
				else{
					this.loadmore.status='nomore'
				}
			})
		},
		onUnload(){
		    uni.$off('handleFun')  //在此生命周期里销毁地址改变事件的监听
		}
	}
</script>

<style>
	.message-list .u-cell {
		padding: 16rpx 30rpx !important;
	}

	.message-list .u-badge {
		right: auto !important;
		left: 0rpx;
		top: 50rpx !important;
	}

	.message-list .u-cell_title {
		padding-left: 20rpx !important;
	}
</style>
