<template>
	<view class="content">
		<view class="u-flex user-top u-p-30 bg-w">
			<view class="u-flex-3">
				<!-- <u-avatar v-if="HeadImg" :src="HeadImg" mode="circle" size="160"></u-avatar> -->
				<view style="width: 160rpx;height: 160rpx;border-radius: 50%;overflow:hidden;">
					<open-data type="userAvatarUrl"></open-data>  
				</view> 
			</view>
			<view class="u-flex-9">
				<view class="u-p-l-30 ">
					<h4>微信名：<open-data type="userNickName"></open-data>   </h4>
					<!-- <text>微信号：0001</text> -->
				</view>
			</view>
		</view>
		<u-empty class="u-text-center" text="建议绑定微信,可即时推送公众号消息" mode="message" margin-top="300"></u-empty>
		<view class="u-m-80">
			<u-button v-if="WxOpenid" type="primary" shape="circle" @click="wxCancle">解绑微信</u-button>
			<u-button v-else type="primary" shape="circle" @click="wxBind">绑定微信</u-button>
		</view>
		<u-modal v-model="show" :content="content" :show-title="true" :show-cancel-button="true" confirm-text="确认"
		 cancel-text="取消" @confirm="submit"></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				show: false,
				content: '解绑微信后，公众号不能接收实时消息，确认解绑？',
				wxInfo:'',
				WxOpenid:'',
				HeadImg:''
			}
		},
		onLoad() {
			var that=this
			this.HeadImg= uni.getStorageSync('HeadImg')
			this.WxOpenid= uni.getStorageSync('WxOpenid')
		},
		methods: {
			wxCancle() {
				this.show = true;
			},
			submit(){
				this.$http.post('/UserApi/RemoveOpenId', {
					UserCode:uni.getStorageSync('UserCode')
				}).then(res => {
					
					if(res.ErrCode==101){
						this.$u.toast('解绑成功');
						this.WxOpenid=''
						uni.setStorageSync('WxOpenid','')
					}
					else{
						this.$u.toast(res.ErrMsg);
					}
				})
			},
			wxBind(){
				
				var that = this
				uni.login({
					provider: 'weixin',
					success: function(res) {
						console.log(res.code);
						that.$http.get('/LoginMng/GetOpenid', {
							params: {
								Jscode: res.code
							}
						}).then(res => {
							var openid=JSON.parse(res.Data).openid
							var unionid=JSON.parse(res.Data).unionid
							that.$http.post('/UserApi/BindOpenId', {
								WxOpenid: openid,
								Unionid:unionid,
								UserCode:uni.getStorageSync('UserCode')
							}).then(res => {
								if(res.ErrCode==101){
									that.$u.toast('绑定成功');
									that.WxOpenid=openid
									uni.setStorageSync('WxOpenid',openid)
								}
								else{
									that.$u.toast(res.ErrMsg);
								}
							})
						})
					}
				});
			}
		}
	}
</script>

<style>

</style>
