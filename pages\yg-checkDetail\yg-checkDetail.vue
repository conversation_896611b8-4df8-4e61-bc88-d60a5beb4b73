<template>
	<view class="border-top">
		<view class="report-top">
			<view class="tab flex">
				<view class="i flex-bd active">巡检管理</view>
			</view>
		</view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">巡检内容</view>
				</view>
			</view>
			<view slot="body">
				<u-form :model="form" ref="uForm">
					<u-form-item label="设备名称" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="info.EquipmentName" :disabled="true" :border="true" />
					</u-form-item>
					<u-form-item label="巡检周期" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="info.InspectionTimeUnit" :disabled="true" :border="true" />
					</u-form-item>
					<u-form-item label="设备位置" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="info.Location==null?'':info.Location" :disabled="true" :border="true" />
					</u-form-item>
					<u-form-item label="设备号" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="info.EquipmentNumber" :disabled="true" :border="true" />
					</u-form-item>
					
					<u-form-item label="填报时间" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="info.InspectionTime" :disabled="true" :border="true" />
					</u-form-item>	
					
					<u-form-item label="巡检描述" label-width="150rpx" :border-bottom="flase">
						<view class="cell" v-for="(item,index) in remarkList">
							<view class="item">{{item.Content}}</view>
							<u-radio-group v-model="item.value">
								<u-radio
									v-for="(i, idx) in item.radio" :key="idx" 
									:name="i.name"
									:disabled="item.value!=i.name"
								>
									{{i.name}}
								</u-radio>
							</u-radio-group>
						</view>
					</u-form-item>
					<u-form-item label="备注" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="info.InspectionContent" placeholder="暂无内容" type="textarea" :disabled="true" :border="true" />
					</u-form-item>
					<u-form-item label="图片" label-width="150rpx" :border-bottom="flase">
						<view class="detail-img-list">
							<image v-for="(img,index) in info.Images" :key="index" :src="img" @click="lookImg(index,info.Images)"></image>
						</view>
					</u-form-item>
					<!-- <u-form-item label="签名" label-width="150rpx" :border-bottom="flase">
						<view class="singature">
							<image class="singature-images" :src="hostUrl+info.SignImage"></image>
						</view>
					</u-form-item> -->
				</u-form>
			</view>
		</u-card>
	</view>
</template>

<script>
	import utils from '../../components/utils.js'
	const app = getApp();
	export default {
		data() {
			return {
				hostUrl:getApp().globalData.hostUrl,
				photoList:[],
				remarkList:[],
				info:''
			}
		},
		methods: {
			lookImg(index,imgList){
				uni.previewImage({
					current:index,
					urls: imgList
				})
			},
		},
		onLoad(e) {
			console.log(e)
			var info = JSON.parse(e.info)
			var InspectionRemarks = info.InspectionRemarks
			var remarkList = []
			for(var i =0;i<InspectionRemarks.length;i++){
				if(InspectionRemarks[i].Result==1){
					var item = {
						Content:InspectionRemarks[i].Content,
						value:'是',
						radio:[
							{
								name: '是',
								disabled: true
							},
							{
								name: '否',
								disabled: true
							}
						]
					}
				}
				else{
					var item = {
						Content:InspectionRemarks[i].Content,
						value:'否',
						radio:[
							{
								name: '是',
								disabled: true
							},
							{
								name: '否',
								disabled: true
							}
						]
					}
				}
				
				remarkList.push(item)
			}
			
			this.remarkList = remarkList
			
			info.InspectionTime = info.InspectionTime.replace('T',' ')
			this.info = info
			
		}
	}
</script>

<style>
	@import url("../fzr-reportList/style.css");
	.qcode{
		padding-right: 30rpx;
	}
	.qcode i{
		font-size: 48rpx;
	}
	.sign{
		background: #F6F6F6;
		padding: 4rpx 10rpx;
		border-radius: 2px;
		text-align: center;
		margin-bottom: 20rpx;
	}
	.singature{
		position: relative;
		height: 200rpx;
		width: 400rpx;
		padding-bottom: 30rpx;
	}
	.singature-images{
		transform:rotate(-90deg);
		position: absolute;
		top: 50%;
		left: 50%;
		margin-left: -100rpx;
		margin-top:-200rpx ;
		height: 400rpx;
		width: 200rpx;
	}
</style>
