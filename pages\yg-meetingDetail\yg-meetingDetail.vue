<template>
	<view class="border-top">
		<view class="detail-top flex">
			<view class="flex-hd">
				<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_hy.png"></image>
			</view>
			<view class="flex-bd">
				<view class="t">工单号：{{info.OrderNo}}</view>
				<view class="desc">会议预约</view>
			</view>
		</view>
		<view class="detail-finish">
			<view class="q">
				<view class="q-t">{{info.ApplyStatusText}}</view>
			</view>
		</view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">预约信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="申请人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.ApplyUserName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="申请时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ApplyTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="预约时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.timeRange}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="会议名称" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.MeetingName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="申请会议室" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ApplyRoomName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="参会人数" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.PersonNumber}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="会议主持" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.MeetingHost}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="主席台名单" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.RostrumList}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="服务要求" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.DemandText">{{info.DemandText}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.Remark">{{info.Remark}}</text>
					</u-cell-item>
				</u-cell-group>
				<view class="detail-his-list" v-if="showAddHis">
					<view class="i" v-for="(item,index) in addHisList" :key="index">
						<view class="t">
							<view class="span">{{item.ChangeTime}}</view>
						</view>
						<u-cell-group :border="flase">
							<u-cell-item  class="cell-item-reset" title="会议时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.ApplyFromTime}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="参会人数" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.PersonNumber}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.Remark}}</text>
							</u-cell-item>
						</u-cell-group>
					</view>
				</view>
				<view class="detail-more-btn" v-if="addHisList" @click="showAddHis=!showAddHis">
					历史预约记录<i :class="'custom-icon '+(showAddHis?'custom-icon-up-copy':'custom-icon-down')"></i>
				</view>
				<view class="detail-phone" @click="callPhone(info.CellPhone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		
		<block>
			<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatusText!='待审核'">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">审核信息</view>
					</view>
				</view>
				<view slot="body">
					<u-cell-group :border="flase">
						<u-cell-item  class="cell-item-reset" title="审核时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
							<text>{{info.CheckTime}}</text>
						</u-cell-item>
						<u-cell-item class="cell-item-reset" title="联系人" :border-bottom="flase" :arrow="flase" title-width="150">
							<text>{{info.CheckUserName}}</text>
						</u-cell-item>
						<u-cell-item class="cell-item-reset" title="审核结果" :border-bottom="flase" :arrow="flase" title-width="150">
							<text v-if="info.ApplyStatus!=2">审核通过</text>
							<text v-else>审核不通过</text>
						</u-cell-item>
						<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
							<text v-if="info.CheckRemark">{{info.CheckRemark}}</text>
						</u-cell-item>
						
					</u-cell-group>
					<view class="detail-phone" @click="callPhone(info.ProjectExt.ProjectLeaderUser.CellPhone)">
						<i class="custom-icon custom-icon-lujingbeifen3"></i>
					</view>
				</view>
			</u-card>
			
			<!-- 会议安排 -->
			<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatus>0">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">会议安排</view>
					</view>
				</view>
				<view slot="body">
					<u-cell-group :border="flase">
						<u-cell-item class="cell-item-reset" title="会议室" :border-bottom="flase" :arrow="flase" title-width="150">
							<text>{{info.RoomName}}</text>
						</u-cell-item>
						<u-cell-item  class="cell-item-reset" title="会议时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
							<text>{{info.timeRange}}</text>
						</u-cell-item>			
						<u-cell-item v-if="info.SecondCheckUserName" class="cell-item-reset" title="服务负责人" :border-bottom="flase" :arrow="flase" title-width="150">
							<text>{{info.SecondCheckUserName}}</text>
						</u-cell-item>
						<u-cell-item v-else class="cell-item-reset" title="服务人员" :border-bottom="flase" :arrow="flase" title-width="150">
							<text>{{info.UserList.length}}</text>
						</u-cell-item>
					</u-cell-group>
					<view class="work">
						 <scroll-view scroll-x="true">
						 	<view class="i" v-for="(item,index) in info.UserList" :key="index">
						 		<u-avatar :show-sex="item.IsAccept==1" sex-icon="checkbox-mark" :show-level="item.IsLeader==1" level-icon="star-fill" :src="item.HeadImg?'item.HeadImg':'http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png'"></u-avatar>
						 		<view class="w-no" v-if="item.JobNumber">{{item.JobNumber}}</view>
						 		<view class="w-name" v-if="item.UserName">{{item.UserName}}</view>
						 	</view>						
						 </scroll-view>					
					</view>
					<u-cell-group :border="flase">
						<u-cell-item v-if="info.SoundEngineer" class="cell-item-reset" title="音响师" :border-bottom="flase" :arrow="flase" title-width="150">
							<view class="work work2" style="text-align: left;">
								<view class="i">
									<u-avatar :show-sex="info.SoundEngineerStatus==1" :src="info.SoundEngineerUserInfo.HeadImg?'item.HeadImg':'http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png'"></u-avatar>
									<view class="w-no" v-if="info.SoundEngineerUserInfo.JobNumber">{{info.SoundEngineerUserInfo.JobNumber}}</view>
									<view class="w-name" v-if="info.SoundEngineerUserInfo.UserName">{{info.SoundEngineerUserInfo.UserName}}</view>
								</view>
							</view>
						</u-cell-item>
					</u-cell-group>
					<u-cell-item v-if="info.ReverseImage" class="cell-item-reset" title="扭转单" :border-bottom="flase" :arrow="flase" title-width="150">
						<view class="detail-img-list">
							<image v-for="(img,index) in info.ReverseImage" :key="index" :src="img" @click="lookImg(index,info.ReverseImage)"></image>
						</view>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="其他人员" :border-bottom="flase" :arrow="flase" title-width="150">
						
					</u-cell-item>
					<view class="work work2">
						<view class="i" v-if="info.EquipmentChargeUser">
							
							<u-avatar :show-sex="info.EquipmentStatus==1" :src="info.EquipmentChargeUserInfo.HeadImg?'item.HeadImg':'http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png'"></u-avatar>
							<view class="w-no" v-if="info.EquipmentChargeUserInfo.JobNumber">{{info.EquipmentChargeUserInfo.JobNumber}}</view>
							<view class="w-name" v-if="info.EquipmentChargeUserInfo.UserName">{{info.EquipmentChargeUserInfo.UserName}}</view>
							<view class="tag">设备部</view>
						</view>
						<view class="i" v-if="info.CleaningChargeUser">
							
							<u-avatar :show-sex="info.CleaningStatus==1" sex-icon="checkbox-mark" :src="info.CleaningChargeUserInfo.HeadImg?'item.HeadImg':'http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png'"></u-avatar>
							<view class="w-no" v-if="info.CleaningChargeUserInfo.JobNumber">{{info.CleaningChargeUserInfo.JobNumber}}</view>
							<view class="w-name" v-if="info.CleaningChargeUserInfo.UserName">{{info.CleaningChargeUserInfo.UserName}}</view>
							<view class="tag">保洁部</view>
						</view>
						<view class="i" v-if="info.SecurityChargeUser">
							
							<u-avatar :show-sex="info.SecurityStatus==1" :src="info.SecurityChargeUserInfo.HeadImg?'item.HeadImg':'http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png'"></u-avatar>
							<view class="w-no" v-if="info.SecurityChargeUserInfo.JobNumber">{{info.SecurityChargeUserInfo.JobNumber}}</view>
							<view class="w-name" v-if="info.SecurityChargeUserInfo.UserName">{{info.SecurityChargeUserInfo.UserName}}</view>
							<view class="tag">安保部</view>
						</view>
					</view>
				</view>
			</u-card>	
			<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="fuwuLeaderAccept">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">人员安排</view>
					</view>
				</view>
				<view slot="body">
					<u-form :model="form" ref="uForm">
						<block>
							<u-form-item label="服务人员" label-width="150rpx" :border-bottom="flase">
								<view slot="right">
									<u-button type="primary" size="mini" @click="selectPeople('duoxuan',false,'notleader',true)">选择</u-button>
								</view>
							</u-form-item>
							<view class="work" v-if="selectedlist2">
								 <scroll-view scroll-x="true">
									<view class="i" v-for="(item,index) in selectedlist2" :key="index">
										<u-avatar :src="item.HeadImg" :show-level="item.isleader=='是'" level-icon="star-fill"></u-avatar>
										<view class="w-no" v-if="item.jobnumber">{{item.jobnumber}}</view>
										<view class="w-name">{{item.name}}</view>
									</view>
							
								</scroll-view>
								
							</view>
						</block>
					</u-form>
					
				</view>	
			</u-card>
			<view class="mr30" v-if="info.ApplyStatusText=='审核通过'&&!isAccept&&UserRemark=='008'">
				<u-button type="primary" @click="jiedan">确认接单</u-button>
			</view>
		</block>
		
		
		
		
		
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.FactFromTimeText">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">会议信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="开始时间" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.FactFromTimeText}}</text>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="会前检查单" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<view class="detail-img-list">
							<image v-for="(img,index) in info.InspectBefore" :key="index" :src="img" @click="lookImg(index,info.InspectBefore)"></image>
						</view>
					</u-cell-item>
					<u-form-item label="检查项" label-width="150rpx" :border-bottom="flase" v-if="checkBeforeHisList.length>0">
						<view class="checkList">
							<view class="li" v-for="(item,index) in checkBeforeHisList">
								<view class="flex-bd">{{item.Content}}</view>
								<view class="flex-hd">
									<view class="i">
										<u-icon name="checkmark-circle" :color="item.Result==0?'#34aa7f':'#ddd'" :size="32" style="margin-right: 10prx;"></u-icon>否
									</view>
									<view class="i">
										<u-icon name="checkmark-circle" :color="item.Result==1?'#34aa7f':'#ddd'" :size="32" style="margin-right: 10prx;"></u-icon>是
									</view>
								</view>
							</view>
						</view>
					</u-form-item>
					<u-cell-item v-if="info.FactEndTimeText" class="cell-item-reset" title="结束时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.FactEndTimeText}}</text>
					</u-cell-item>
					<u-cell-item v-if="info.FactEndTimeText" class="cell-item-reset" title="会后检查单" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<view class="detail-img-list">
							<image v-for="(img,index) in info.InspectAfter" :key="index" :src="img" @click="lookImg(index,info.InspectAfter)"></image>
						</view>
					</u-cell-item>
					<u-form-item label="检查项" label-width="150rpx" :border-bottom="flase" v-if="checkAfterHisList.length>0">
						<view class="checkList">
							<view class="li" v-for="(item,index) in checkAfterHisList">
								<view class="flex-bd">{{item.Content}}</view>
								<view class="flex-hd">
									<view class="i">
										<u-icon name="checkmark-circle" :color="item.Result==0?'#34aa7f':'#ddd'" :size="32" style="margin-right: 10prx;"></u-icon>否
									</view>
									<view class="i">
										<u-icon name="checkmark-circle" :color="item.Result==1?'#34aa7f':'#ddd'" :size="32" style="margin-right: 10prx;"></u-icon>是
									</view>
								</view>
							</view>
						</view>
					</u-form-item>
					<u-cell-item class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.InspectRemark">{{info.InspectRemark}}</text>
					</u-cell-item>
					
					
					
				</u-cell-group>
			</view>
		</u-card>
		
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatusText=='已评价'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">评价信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="评价人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.applyusername}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价星级" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<u-rate :disabled="true" v-model="info.EvaluateLevel" :count="5" active-color="#eca805"></u-rate>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateContent}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.checkPhotos" :key="index" :src="img" @click="lookImg(index,info.checkPhotos)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		
		<block v-if="info.ApplyStatusText=='进行中'">
			<u-card :foot-border-top="false" padding="20" class="card-readyonly">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">会议信息</view>
					</view>
				</view>
				<view slot="body">
					<u-form :model="form" ref="uForm">
						<u-form-item label="开始时间" label-width="150rpx" :border-bottom="flase" v-if="!info.FactFromTimeText">
							<u-input v-model="FactFromTime" @click="startShow=true" :border="true" disabled type="select" />
						</u-form-item>
						<u-form-item label="结束时间" label-width="150rpx" :border-bottom="flase" v-if="info.FactFromTimeText">
							<u-input v-model="FactEndTime" @click="endShow=true"  :border="true" disabled  type="select"/>
						</u-form-item>
						<u-form-item label="会前检查单" label-width="150rpx" :border-bottom="flase" v-if="!info.FactFromTimeText">
							<u-upload ref="uUpload1" :action="action" :header="uploadHeader" @on-remove="remove1" @on-success="uploadOk1" :file-list="info.InspectBeforeList" ></u-upload>
						</u-form-item>
						<u-form-item label="检查项" label-width="150rpx" :border-bottom="flase" v-if="checkBeforeList.length>0&&!info.FactFromTimeText">
							<view class="checkList">
								<view class="li" v-for="(item,index) in checkBeforeList">
									<view class="flex-bd">{{item.CheckContent}}</view>
									<view class="flex-hd">
										<view class="i" @click="checkBefore(0,index)">
											<u-icon name="checkmark-circle" :color="item.checked=='否'?'#34aa7f':'#ddd'" :size="32" style="margin-right: 10prx;"></u-icon>否
										</view>
										<view class="i" @click="checkBefore(1,index)">
											<u-icon name="checkmark-circle" :color="item.checked=='是'?'#34aa7f':'#ddd'" :size="32" style="margin-right: 10prx;"></u-icon>是
										</view>
									</view>
								</view>
							</view>
						</u-form-item>
						<u-form-item label="检查项" label-width="150rpx" :border-bottom="flase" v-if="checkAfterList.length>0&&info.FactFromTimeText&&!info.FactEndTimeText">
							<view class="checkList">
								<view class="li" v-for="(item,index) in checkAfterList">
									<view class="flex-bd">{{item.CheckContent}}</view>
									<view class="flex-hd">
										<view class="i" @click="checkAfter(0,index)">
											<u-icon name="checkmark-circle" :color="item.checked=='否'?'#34aa7f':'#ddd'" :size="32" style="margin-right: 10prx;"></u-icon>否
										</view>
										<view class="i" @click="checkAfter(1,index)">
											<u-icon name="checkmark-circle" :color="item.checked=='是'?'#34aa7f':'#ddd'" :size="32" style="margin-right: 10prx;"></u-icon>是
										</view>
									</view>
								</view>
							</view>
						</u-form-item>
						<!-- <u-form-item label="会中检查单" label-width="150rpx" :border-bottom="flase">
							<u-upload ref="uUpload2" :action="action" :header="uploadHeader" @on-remove="remove2" @on-success="uploadOk2" :file-list="info.InspectInList" ></u-upload>
						</u-form-item>	 -->
						<u-form-item label="会后检查单" label-width="150rpx" :border-bottom="flase" v-if="info.FactFromTimeText">
							<u-upload ref="uUpload3" :action="action" :header="uploadHeader" @on-remove="remove3" @on-success="uploadOk3" :file-list="info.InspectAfterList" ></u-upload>
						</u-form-item>
						<u-form-item label="备注内容" label-width="150rpx" :border-bottom="flase">
							<u-input v-model="InspectRemark" type="textarea" :border="true" />
						</u-form-item>		
					</u-form>
				</view>
			</u-card>
			<u-picker mode="time" v-model="startShow" :params="params" @confirm="startDate"></u-picker>
			<u-picker mode="time" v-model="endShow" :params="params" @confirm="endDate">></u-picker>
			
			<view class="mr30">
				<u-row gutter="16">
					<!-- <u-col span="6">
						<u-button type="default" @click="submit(4)">暂存信息</u-button>
					</u-col> -->
					<u-col span="12">
						<u-button type="primary" @click="submit()">提交信息</u-button>
					</u-col>
				</u-row>
			</view>
		</block>
		
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ReturnVisitRemark">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">跟踪回访</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item  class="cell-item-reset" title="内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ReturnVisitRemark}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.ReturnVisitImage" :key="index" :src="img" @click="lookImg(index,info.ReturnVisitImage)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		
		<!-- 反馈信息 -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.SoundEngineerRemark">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">音响师反馈</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item  class="cell-item-reset" title="内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.SoundEngineerRemark}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.SoundEngineerImage" :key="index" :src="img" @click="lookImg(index,info.SoundEngineerImage)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.CleaningRemark">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">保洁反馈</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item  class="cell-item-reset" title="内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.CleaningRemark}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.CleaningImage" :key="index" :src="img" @click="lookImg(index,info.CleaningImage)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.EquipmentRemark">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">设备反馈</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item  class="cell-item-reset" title="内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EquipmentRemark}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.EquipmentImage" :key="index" :src="img" @click="lookImg(index,info.EquipmentImage)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.SecurityRemark">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">安保反馈</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item  class="cell-item-reset" title="内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.SecurityRemark}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.SecurityImage" :key="index" :src="img" @click="lookImg(index,info.SecurityImage)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		
		<!-- 扭转单 -->
		<block v-if="info.ApplyStatusText!='待审核'&&!fankuiOk">
			
		
			<block v-if="(UserRemark=='004'&&info.EquipmentStatus==1)||(UserRemark=='009'&&info.EquipmentStatus==1)||(UserRemark=='108'&&info.CleaningStatus==1)||(UserRemark=='109'&&info.CleaningStatus==1)||(UserRemark=='110'&&info.SecurityStatus==1)||(UserRemark=='111'&&info.SecurityStatus==1)||(UserRemark=='112'&&info.SoundEngineerStatus==1)">
				<u-card :foot-border-top="false" padding="20" class="card-readyonly">
					<view slot="head">
						<view class="u-flex u-col-top u-row-between">
							<view class="u-flex-nowrap u-item-title u-font-28">反馈</view>
						</view>
					</view>
					<view slot="body">
						<u-form :model="form" ref="uForm">
							<u-form-item label="内容" label-width="150rpx" :border-bottom="flase">
								<u-input v-model="FeedBackRemark" type="textarea" :border="true" />
							</u-form-item>
							<u-form-item label="照片" label-width="150rpx" :border-bottom="flase">
								<u-upload ref="uUpload4" :action="action" :header="uploadHeader" @on-remove="remove4" @on-success="uploadOk4"></u-upload>
							</u-form-item>						
						</u-form>
					</view>
				</u-card>
				<u-picker mode="time" v-model="startShow" :params="params" @confirm="startDate"></u-picker>
				<u-picker mode="time" v-model="endShow" :params="params" @confirm="endDate">></u-picker>
				<view class="mr30">
					<u-button type="primary" @click="nzdSubmit">提交</u-button>
				</view>
			</block>
			<block  v-if="(UserRemark=='004'&&info.EquipmentStatus==0)||(UserRemark=='009'&&info.EquipmentStatus==0)||(UserRemark=='108'&&info.CleaningStatus==0)||(UserRemark=='109'&&info.CleaningStatus==0)||(UserRemark=='110'&&info.SecurityStatus==0)||(UserRemark=='111'&&info.SecurityStatus==0)||(UserRemark=='112'&&info.SoundEngineerStatus==0)">
				<view class="mr30">
					<u-button type="primary" @click="nzdJiedan">接单</u-button>
				</view>
			</block>
			<block v-if="info.CheckUserCode == DealUserCode">
				<u-card :foot-border-top="false" padding="20" class="card-readyonly">
					<view slot="head">
						<view class="u-flex u-col-top u-row-between">
							<view class="u-flex-nowrap u-item-title u-font-28">跟踪回访</view>
						</view>
					</view>
					<view slot="body">
						<u-form :model="form" ref="uForm">
							<u-form-item label="内容" label-width="150rpx" :border-bottom="flase">
								<u-input v-model="FeedBackRemark" type="textarea" :border="true" />
							</u-form-item>
							<u-form-item label="照片" label-width="150rpx" :border-bottom="flase">
								<u-upload ref="uUpload4" :action="action" :header="uploadHeader" @on-remove="remove4" @on-success="uploadOk4"></u-upload>
							</u-form-item>						
						</u-form>
					</view>
				</u-card>
				<view class="mr30">
					<u-button type="primary" @click="nzdSubmit">提交</u-button>
				</view>
			</block>
		</block>
		<view style="height:1px"></view>
	</view>
</template>

<script>
	import utils from '../../components/utils.js'
	const app = getApp();
	export default {
		data() {
			return {
				ProjectCode:'',
				MeetingCode:'',
				info:'',
				photos1:[],
				photos2:[],
				photos3:[],
				nzdphoto:[],
				params: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					second: false
				},
				uploadHeader:{
					Authorization: 'Bearer '+ uni.getStorageSync('access_token')
				},
				action:app.globalData.uploadUrl,
				hostUrl:app.globalData.hostUrl,
				InspectRemark:'',
				startShow:'',
				endShow:'',
				FactFromTime:'',
				FactEndTime:'',
				checkPhotos:'',
				isAccept:'',
				isZuzhang:'',
				UserRemark:'',
				FeedBackRemark:'',
				DealUserCode:'',
				showAddHis:'',
				addHisList:'',
				checkHisList:'',
				showCheckHis:'',
				fankuiOk:false,
				selectedlist2:'',
				fuwuLeaderAccept:false,
				checkBeforeList:'',
				checkAfterList:'',
				checkBeforeHisList:'',
				checkAfterHisList:''
			}
		},
		methods: {
			checkBefore(e,index){
				if(e==1){
					this.checkBeforeList[index].checked = '是'
				}else{
					this.checkBeforeList[index].checked = '否'
				}
			},
			checkAfter(e,index){
				if(e==1){
					this.checkAfterList[index].checked = '是'
				}else{
					this.checkAfterList[index].checked = '否'
				}
			},
			selectPeople(type='',selectFzr=false,range='',limit=false,renyuan=2){
				console.log(type)
				this.selectFzr=selectFzr
				this.renyuan=renyuan
				var arr=[]
				var list=this.info.UserList
				for(let index in list){
					arr.push(list[index].ServiceUserCode)
				}
				if(limit){
			
					uni.navigateTo({
						url:'../selectPeople/selectPeople?ProjectCode='+this.ProjectCode+'&type='+type+'&renyuan='+renyuan+'&range='+range+'&limit='+JSON.stringify(arr)
					})
				}
				else{
					uni.navigateTo({
						url:'../selectPeople/selectPeople?ProjectCode='+this.ProjectCode+'&type='+type+'&renyuan='+renyuan+'&range='+range
					})
				}
				
			},
			nzdSubmit(){
				var that = this
				var FeedBackRemark = this.FeedBackRemark
				var nzdphoto =this.nzdphoto
				var FeedBackImage =''
				if(nzdphoto.length>0){
					FeedBackImage = nzdphoto.toString()
				}
				var MeetingCode = this.MeetingCode
				var DealUserCode = uni.getStorageSync('UserCode')
				this.DealUserCode = DealUserCode
				var UserRemark=uni.getStorageSync('UserRemark')
				var Type=''
				if(UserRemark==108||UserRemark==109){
					Type=3
				}
				else if(UserRemark==112){
					Type=1
				}
				else if(UserRemark==110||UserRemark==111){
					Type=4
				}
				else{
					Type=2
				}
				
				if(!FeedBackRemark){
					return this.$u.toast('请填写内容！');
				}
				
				this.$http.post('/MeetingMngApi/Execute?doCmd=feedback', {
					MeetingCode,DealUserCode,Type,FeedBackRemark,FeedBackImage
				}).then(res => {
					that.$u.toast(res.ErrMsg);
					if(res.ErrCode==100){
						setTimeout(function(){
							that.FeedBackRemark =''
							that.nzdphoto = []
							that.$refs.uUpload4.clear();
							that.load()
						},1000)
					}
				})
			},
			nzdJiedan(){
				var that = this
				var MeetingCode = this.MeetingCode
				var ServiceUserCode = uni.getStorageSync('UserCode')
				var UserRemark=uni.getStorageSync('UserRemark')
				var ReceiveType=''
				if(UserRemark==108||UserRemark==109){
					ReceiveType=3
				}
				else if(UserRemark==112){
					ReceiveType=1
				}
				else if(UserRemark==110||UserRemark==111){
					ReceiveType=4
				}
				else{
					ReceiveType=2
				}
				this.$http.post('/MeetingMngApi/Execute?doCmd=receive', {
					MeetingCode,ReceiveType,ServiceUserCode
				}).then(res => {
					console.log(res)
					that.$u.toast(res.ErrMsg);
					if(res.ErrCode==100){
						setTimeout(function(){
							that.load()
						},1000)
					}
				})
			},
			lookImg(index,imgList){
				uni.previewImage({
					current:index,
					urls: imgList
				})
			},
			callPhone(e){
				uni.showModal({
				    title: '是否拨打电话？',
				    content: e,
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber:e
							})
				        } 
				    }
				});
			},
			renderAddList(){
				return new Promise((resolve)=>{
					this.$http.get('/MeetingMngApi/GetDetail?Docmd=history', {
						params:{
							MeetingCode:this.MeetingCode
						}
					}).then(res => {
						resolve(res.Data)
					})
				})		
			},
			jiedan(){
				var that=this
				var codes = []
				var selectedlist2 = this.selectedlist2
				for(let i in selectedlist2){
					codes.push(selectedlist2[i].usercode)
				}
				console.log(codes)
				uni.showModal({
				    title: '提示',
				    content: '确认接单？',
					confirmText:'确定',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							that.$http.post('/MeetingMngApi/Execute?Docmd=receive', {
								MeetingCode:that.MeetingCode,
								ServiceUserCode:uni.getStorageSync('UserCode'),
								ServiceUserCodes:codes.toString()
							}).then(res => {
								that.$u.toast(res.ErrMsg);
								if(res.ErrCode==100){
									setTimeout(function(){
										that.load()
									},1000)
								}
							})
				        } 
				    }
				});				
			},
			startDate(e){
				console.log(e)
				this.FactFromTime=e.year+'-'+e.month+'-'+e.day+' '+e.hour+':'+e.minute+':00'
			},
			endDate(e){
				console.log(e)
				this.FactEndTime=e.year+'-'+e.month+'-'+e.day+' '+e.hour+':'+e.minute+':00'
			},
			submit(e){
				var that=this
				var info = this.info
				var status = ''
				if(!info.FactFromTimeText){
					status = 4
					if(!this.photos1[0]){
						return that.$u.toast('上传会前检查！');
					}
					if(!that.FactFromTime){
						return that.$u.toast('请选择开始时间！');
					}
				}
				else{
					status = 5
					if(!this.photos3[0]){
						return that.$u.toast('上传会后检查！');
					}
					if(!that.FactEndTime){
						return that.$u.toast('请选择结束时间！');
					}
				}
				
				var checkBeforeList = []
				if(this.checkBeforeList.length>0){
					var a = this.checkBeforeList
					for(let i in a){
						if(a[i].checked=='是'){
							checkBeforeList.push(a[i].Code)
						}
					}
				}
				
				var checkAfterList = []
				if(this.checkAfterList.length>0){
					var a = this.checkAfterList
					for(let i in a){
						if(a[i].checked=='是'){
							checkAfterList.push(a[i].Code)
						}
					}
				}
				
				that.$http.post('/MeetingMngApi/Execute?Docmd=complete', {
					MeetingCode:that.MeetingCode,
					FactFromTime:that.FactFromTime,
					FactEndTime:that.FactEndTime,
					ApplyStatus:status,
					InspectBefore:that.photos1.toString(),
					InspectIn:that.photos2.toString(),
					InspectAfter:that.photos3.toString(),
					InspectRemark:that.InspectRemark,
					CheckBeforeCodes:!info.FactFromTimeText?checkBeforeList.toString():'',
					CheckAfterCodes:(info.FactFromTimeText&&!info.FactEndTimeText)?checkAfterList.toString():''
				}).then(res => {
					that.$u.toast(res.ErrMsg);
					if(res.ErrCode==100){
						setTimeout(function(){
							that.load()
						},1000)
					}
				})
				
				// if(e==4){
				// 	that.$http.post('/MeetingMngApi/Execute?Docmd=complete', {
				// 		MeetingCode:that.MeetingCode,
				// 		FactFromTime:that.FactFromTime,
				// 		FactEndTime:that.FactEndTime,
				// 		ApplyStatus:e,
				// 		InspectBefore:this.photos1.toString(),
				// 		InspectIn:this.photos2.toString(),
				// 		InspectAfter:this.photos3.toString(),
				// 		InspectRemark:that.InspectRemark
				// 	}).then(res => {
				// 		that.$u.toast(res.ErrMsg);
				// 	})
				// }
				// else{
				// 	if(!this.photos1[0]){
				// 		return that.$u.toast('上传会前检查！');
				// 	}
				// 	if(!this.photos3[0]){
				// 		return that.$u.toast('上传会后检查！');
				// 	}
				// 	if(!that.FactFromTime){
				// 		return that.$u.toast('请选择开始时间！');
				// 	}
				// 	if(!that.FactEndTime){
				// 		return that.$u.toast('请选择结束时间！');
				// 	}
				// 	if(!utils.checkTwoTime(that.FactFromTime,that.FactEndTime)){
				// 		return this.$u.toast('结束时间应大于开始时间');
				// 	}
					
				// }
							
			},
			remove1(index){
				this.photos1.splice(index,1)
				console.log(index)
			},
			remove2(index){
				this.photos2.splice(index,1)
				console.log(index)
			},
			remove3(index){
				this.photos3.splice(index,1)
				console.log(index)
			},
			remove4(index){
				this.nzdphoto.splice(index,1)
				console.log(index)
			},
			uploadOk1(data,index,lists){
				console.log(data)
				this.photos1.push(data.RetValue)
			},
			uploadOk2(data,index,lists){
				console.log(data)
				this.photos2.push(data.RetValue)
			},
			uploadOk3(data,index,lists){
				console.log(data)
				this.photos3.push(data.RetValue)
			},
			uploadOk4(data,index,lists){
				console.log(data)
				this.nzdphoto.push(data.RetValue)
			},
			load(){
				var UserRemark=uni.getStorageSync('UserRemark')
				this.UserRemark = UserRemark
				
				
				this.$http.get('/MeetingMngApi/GetDetail?Docmd=main', {
					params:{
						MeetingCode:this.MeetingCode
					}
				}).then(res => {
					var info=res.Data
					//格式化时间
					info.ApplyTime=info.ApplyTime?info.ApplyTime.replace('T',' '):''
					info.DispatchTime=info.DispatchTime?info.DispatchTime.replace('T',' '):''
					info.AcceptTime=info.AcceptTime?info.AcceptTime.replace('T',' '):''
					info.CompleteTime=info.CompleteTime?info.CompleteTime.replace('T',' '):''
					info.EvaluateTime=info.EvaluateTime?info.EvaluateTime.replace('T',' '):''
					info.CheckTime=info.CheckTime?info.CheckTime.replace('T',' '):''
					//格式化会议时间
					var ApplyFromTime=info.ApplyFromTime.split('T')
					var ApplyEndTime=info.ApplyEndTime.split('T')
					info.timeRange=ApplyFromTime[0]+' '+ApplyFromTime[1]+'至'+ApplyEndTime[1]					
					
					if(info.EvaluatePhotos){
						info.EvaluatePhotos=info.EvaluatePhotos.split(',')
						for(let index in info.EvaluatePhotos){
							info.EvaluatePhotos[index]=this.hostUrl+info.EvaluatePhotos[index]
						}
					}
					
					var checkPhotos=[]
					if(info.InspectBefore){
						info.InspectBefore=info.InspectBefore.split(',')
						this.photos1=JSON.parse(JSON.stringify(info.InspectBefore))
						for(let index in info.InspectBefore){
							info.InspectBefore[index] = this.hostUrl+info.InspectBefore[index]	
						}
					}
					if(info.InspectAfter){
						info.InspectAfter=info.InspectAfter.split(',')
						this.photos3=JSON.parse(JSON.stringify(info.InspectAfter))
						for(let index in info.InspectAfter){
							info.InspectAfter[index] = this.hostUrl+info.InspectAfter[index]	
						}
					}
					
					if(info.UserList){
						for(let index in info.UserList){
							if(info.UserList[index].ServiceUserCode==uni.getStorageSync('UserCode')&&info.UserList[index].IsAccept==1){
								this.isAccept=true
							}
							if(info.UserList[index].ServiceUserCode==uni.getStorageSync('UserCode')&&info.UserList[index].IsLeader==1){
								this.isZuzhang=true
							}
						}
					}
					
					if(res.Data.ReverseImage){
						res.Data.ReverseImage=res.Data.ReverseImage.split(',')
						for(let index in res.Data.ReverseImage){
							res.Data.ReverseImage[index]=this.hostUrl+res.Data.ReverseImage[index]
						}
					}
					
					
					if(res.Data.CleaningImage){
						res.Data.CleaningImage=utils.imgArray(res.Data.CleaningImage)
					}
					if(res.Data.SecurityImage){
						res.Data.SecurityImage=utils.imgArray(res.Data.SecurityImage)
					}
					if(res.Data.EquipmentImage){
						res.Data.EquipmentImage=utils.imgArray(res.Data.EquipmentImage)
					}
					if(res.Data.ReturnVisitImage){
						res.Data.ReturnVisitImage=utils.imgArray(res.Data.ReturnVisitImage)
					}
					if(res.Data.SoundEngineerImage){
						res.Data.SoundEngineerImage=utils.imgArray(res.Data.SoundEngineerImage)
					}
					this.checkPhotos=checkPhotos
					this.info=info
					this.InspectRemark = info.InspectRemark
					this.FactFromTime = info.FactFromTime=='0001-01-01T00:00:00'?'': info.FactFromTime
					//已反馈进行隐藏
					if((UserRemark=='108'||UserRemark=='109')&&info.CleaningRemark!=null){
						this.fankuiOk = true
					}
					if((UserRemark=='112')&&info.SoundEngineerRemark!=null){
						this.fankuiOk = true
					}
					if((UserRemark=='004'||UserRemark=='009')&&info.EquipmentRemark!=null){
						this.fankuiOk = true
					}
					if((UserRemark=='110'||UserRemark=='111')&&info.SecurityRemark!=null){
						this.fankuiOk = true
					}
					if((info.CheckUserCode == uni.getStorageSync('UserCode'))&&info.ReturnVisitRemark!=null){
						this.fankuiOk = true
					}
					
					for(var i=0;i<info.UserList.length;i++){
						if(info.UserList[i].IsLeader==1&&info.UserList[i].IsAccept==0){
							this.fuwuLeaderAccept = true
							break;
						}
						else{
							this.fuwuLeaderAccept = false
						}
					}
					
					if(!info.FactFromTimeText){
						this.$http.get('/MeetingMngApi/GetCheckContent', {
							params:{
								ProjectCode:uni.getStorageSync('projectCode'),
								checkType:'会前'
							}
						}).then(res => {
							console.log()
							if(res.Data.length>0){
								for(let i in res.Data){
									res.Data[i].checked = ''
								}
								this.checkBeforeList = res.Data
							}
						})
					}
					
					if(info.FactFromTimeText&&!info.FactEndTimeText){
						this.$http.get('/MeetingMngApi/GetCheckContent', {
							params:{
								ProjectCode:uni.getStorageSync('projectCode'),
								checkType:'会后'
							}
						}).then(res => {
							console.log()
							if(res.Data.length>0){
								for(let i in res.Data){
									res.Data[i].checked = ''
								}
								this.checkAfterList = res.Data
							}
						})
						this.$http.get('/MeetingMngApi/GetDetail', {
							params:{
								doCmd:"meetingcheck",
								meetingCode:this.MeetingCode
							}
						}).then(res => {
							this.checkBeforeHisList = res.Data
						})
					}
					
					if(info.FactFromTimeText&&info.FactEndTimeText){
						this.$http.get('/MeetingMngApi/GetDetail', {
							params:{
								doCmd:"meetingcheck",
								meetingCode:this.MeetingCode,
								checkType:'会前'
							}
						}).then(res => {
							this.checkBeforeHisList = res.Data
						})
						this.$http.get('/MeetingMngApi/GetDetail', {
							params:{
								doCmd:"meetingcheck",
								meetingCode:this.MeetingCode,
								checkType:'会后'
							}
						}).then(res => {
							this.checkAfterHisList = res.Data
						})
					}
				})
				
				//申请记录
				this.renderAddList().then(res=>{
					if(res.length>0){
						var checkHisList=[]
						for(let index in res){
							res[index].ChangeTime=res[index].ChangeTime.replace('T',' ')
							res[index].CheckTime=res[index].CheckTime.replace('T',' ')
							res[index].ApplyTime=res[index].ApplyTime.replace('T',' ')
							
							//格式化会议时间
							var ApplyFromTime=res[index].ApplyFromTime.split('T')
							var ApplyEndTime=res[index].ApplyEndTime.split('T')
							var timeRange=ApplyFromTime[0]+' '+ApplyFromTime[1]+'至'+ApplyEndTime[1]							
							res[index].timeRange=timeRange
													
							if(res[index].ApplyStatus!=0){
								checkHisList.push(res[index])
							}
						}
						this.addHisList=res
						this.checkHisList=checkHisList
					}
				})
			}
		},
		onLoad(e) {
			//详情
			console.log(e)
			var MeetingCode=e.code
			this.MeetingCode=MeetingCode
			this.ProjectCode=e.ProjectCode
			this.load()
			this.DealUserCode = uni.getStorageSync('UserCode')
			//会议室
			this.$http.get('/MeetingMngApi/GetAllList', {
				params:{
					ProjectCode:uni.getStorageSync('projectCode'),
					p:1,
					ps:50
				}
			}).then(res => {
				var list=[]
				for(let index in res.Data){
					var item={
						label:res.Data[index].RoomName,
						value:res.Data[index].RoomCode
					}
					list.push(item)
				}
				this.roomList=list
			})
			
			
		},
		onShow() {
			uni.$on('handleFun', res => {
				
				var res = res.selectedlist;
				var limit = this.info.UserList
				var res = res.filter(function (x) {
					for(var i=0;i<limit.length;i++){
						if(limit[i].ServiceUserCode!=x.usercode){
							return x
						}
					}
				    
				});
				this.selectedlist2 = res;
				// 清除监听
				uni.$off('handleFun');
			});
		}
	}
</script>

<style>
	page{
		background: #f6f6f6;
	}
	.u-avatar__sex{
		background: #35318f;
	}
	.work2{
	}
	.work2 .i{
		line-height: 1.4;
		display: inline-block;
		text-align: center;
		    margin: 0 20rpx;
			position: relative;
	}
	.work2 .i .tag{
		color: #ea6c23;
		top: 0;
		right:0;
		z-index: 2;
		font-size: 24rpx;
		margin-top: 4px;
	}
</style>
