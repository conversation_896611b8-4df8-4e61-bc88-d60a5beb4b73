<template>
	<view class="border-top">
		<view class="detail-top flex">
			<view class="flex-bd">
				<view class="t">工单号：{{info.RepairNo}}</view>
			</view>
		</view>
		<view class="detail-finish">
			<view class="q">
				<view class="q-t">{{info.repairStatus}}</view>
			</view>
		</view>
		<!-- <view class="detail-finish" v-if="info.repairStatus=='已完成'||info.repairStatus=='已评价'">
			<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/finish.png"></image>
		</view> -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">报修信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="报修人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.applyusername}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="报修时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ApplyTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修处室" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.Department}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修地点" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.Address}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="报修内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.RepairMatter}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.Remark}}</text>
					</u-cell-item>
					
				</u-cell-group>
				<view class="detail-img-list">
					<image v-for="(img,index) in info.Photos" :key="index" :src="img" @click="lookImg(index,info.Photos)"></image>
				</view>
				<view class="detail-phone" @click="callPhone(info.CellPhone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus=='待接单'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">派单信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="派单时间" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.DispatchTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="接单时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.AcceptTime!='0001-01-01 00:00:00'">{{info.AcceptTime}}</text>
					</u-cell-item>					
				</u-cell-group>
				<view class="work">
					<u-avatar src="http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png"></u-avatar>
					<view class="w-no"  v-if="selectedlist.repairJobNumber">{{info.repairJobNumber}}</view>
					<view class="w-name">{{info.repairusername}}</view>
				</view>
			</view>
		</u-card>
		<view class="mr30" v-if="info.repairStatus=='待接单'">
			<u-button type="primary" @click="jiedan">确认接单</u-button>
		</view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus!='待接单'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">派单信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="派单时间" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.DispatchTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="接单时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.AcceptTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="派单人" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.DispatchUserName}}</text>
					</u-cell-item>
				</u-cell-group>
				<view class="work">
					<u-avatar src="http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png"></u-avatar>
					<view class="w-no"  v-if="selectedlist.repairJobNumber">{{info.repairJobNumber}}</view>
					<view class="w-name">{{info.repairusername}}</view>
				</view>
				<view class="detail-phone" @click="callPhone(info.dispatchuserphone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus=='已接单'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">检修情况</view>
				</view>
			</view>
			<view slot="body" v-for="(item,index) in yongliaoList">
				<view class="tit bor-b flex">
					<view class="flex-bd">用料{{index+1}}</view>
					<u-icon :size="36" name="minus-circle-fill" color="#ff0000" @click="delYongliao(index)"></u-icon>
				</view>
				<u-form :model="form" ref="uForm">
					<view class="box">
						<u-form-item label="用料名称" label-width="140rpx" :border-bottom="flase">
							<u-input v-model="item.mc" :border="true" />
						</u-form-item>
						<u-form-item label="数量" label-width="140rpx" :border-bottom="flase">
							<u-input v-model="item.sl" :border="true" type="number" />
						</u-form-item>
						<u-form-item label="价格" label-width="140rpx" :border-bottom="flase">
							<u-input v-model="item.jg" :border="true" type="number" />
						</u-form-item>
					</view>
				</u-form>
			</view>
		</u-card>
		<view v-if="info.repairStatus=='已接单'" style="margin: 0 30rpx;">
			<u-button type="info" @click="addYongliao">增加用料</u-button>
		</view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus=='已接单'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">维修信息</view>
				</view>
			</view>
			<view slot="body">
				<u-form :model="form" ref="uForm">
					<!-- <u-form-item label="维修服务单" label-width="150rpx" :border-bottom="flase">
						<u-upload ref="uUpload1" :action="action" :header="uploadHeader" @on-remove="remove1" @on-success="uploadOk1" :file-list="fileList1" ></u-upload>
					</u-form-item>	 -->
					<u-form-item label="修复图片" label-width="150rpx" :border-bottom="flase">
						<u-upload ref="uUpload" :action="action" :header="uploadHeader" @on-remove="remove" @on-success="uploadOk" :file-list="fileList" ></u-upload>
					</u-form-item>
					<u-form-item label="备注" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="RepairContent" type="textarea" :border="true" />
					</u-form-item>
					
				</u-form>
			</view>
		</u-card>
		<view class="mr30" v-if="info.repairStatus=='已接单'">
			<u-button type="primary" @click="submit">确认完成</u-button>
		</view>
		
		<block v-if="info.RepairSituations.length>0">
			<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus=='已完成'||info.repairStatus=='已评价'">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">检修情况</view>
					</view>
				</view>
				<view slot="body" v-for="(item,index) in info.RepairSituations">
					<view class="tit bor-b flex">
						<view class="flex-bd">用料{{index+1}}</view>
					</view>
					<u-cell-group :border="flase">
						<u-cell-item class="cell-item-reset" title="用料名称" :border-bottom="flase" :arrow="flase" title-width="150">
							<text>{{item.MaterialName}}</text>
						</u-cell-item>
						<u-cell-item  class="cell-item-reset" title="数量" :border-bottom="flase" :arrow="flase"  title-width="150" >
							<text>{{item.MaterialNumber}}</text>
						</u-cell-item>
						<u-cell-item  class="cell-item-reset" title="价格" :border-bottom="flase" :arrow="flase"  title-width="150" >
							<text>{{item.MaterialPrice}}</text>
						</u-cell-item>
					</u-cell-group>
				</view>
			</u-card>
		</block>
		
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus=='已完成'||info.repairStatus=='已评价'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">维修信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="维修人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.repairusername}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.CompleteTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.RepairContent}}</text>
					</u-cell-item>
					
				</u-cell-group>
				<view class="detail-img-list">
					<image v-for="(img,index) in info.RepairPhotos" :key="index" :src="img" @click="lookImg(index,info.RepairPhotos)"></image>
				</view>
			</view>
		</u-card>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus=='已评价'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">评价信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="评价人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.applyusername}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修质量" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.RepairQuality}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="服务质量" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ServiceQuality}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价星级" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<u-rate :disabled="true" v-model="info.EvaluateLevel" :count="5" active-color="#eca805"></u-rate>
					</u-cell-item>
					<u-cell-item v-if="info.EvaluateContent" class="cell-item-reset" title="建议和要求" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateContent}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.EvaluatePhotos" :key="index" :src="img" @click="lookImg(index,info.EvaluatePhotos)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		<view style="height: 1px;"></view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				info:{
					RepairNo:'',
					repairStatus:'',
					applyusername:'',
					ApplyTime:'',
					Remark:'',
					Photos:'',
					dispatchtime:'',
					AcceptTime:'',
					DispatchUserJobNumber:'',
					DispatchUserName:'',
					dispatchuserphone:'',
					repairusername:'',
					completetime:'',
					RepairContent:'',
					RepairPhotos:'',
					applyusername:'',
					EvaluateTime:'',
					EvaluateLevel:'',
					EvaluateContent:'',
					Department:'',
					Address:''
				},
				uploadHeader:{
					Authorization: 'Bearer '+ uni.getStorageSync('access_token')
				},
				action:app.globalData.uploadUrl,
				ServiceList:[],
				photoList:[],
				RepairContent:'',
				hostUrl:app.globalData.hostUrl,
				yongliaoList:[]
			}
		},
		methods: {
			delYongliao(e){
				this.yongliaoList.splice(e,1)
			},
			addYongliao(){
				this.yongliaoList.push({})
			},
			lookImg(index,imgList){
				uni.previewImage({
					current:index,
					urls: imgList
				})
			},
			jiedan(){
				var that=this
				uni.showModal({
				    title: '提示',
				    content: '确认接单？',
					confirmText:'确定',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							that.$http.post('/RepairMngApi/Execute?Docmd=receive', {
								RepairCode:that.RepairCode
							}).then(res => {
								that.$u.toast(res.ErrMsg);
								if(res.ErrCode==100){
									setTimeout(function(){
										that.load()
									},1000)
								}
							})
				        } 
				    }
				});				
			},
			callPhone(e){
				uni.showModal({
				    title: '是否拨打电话？',
				    content: e,
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber:e
							})
				        } 
				    }
				});
			},
			submit(){
				var that=this
				
				// var photoList1=[]
				// var files1 = this.$refs.uUpload1.lists;
				// for(let index in files1){
				// 	photoList1.push(files1[index].response.RetValue)
				// }
				var photoList=this.photoList
				// if(!photoList1[0]){
				// 	return that.$u.toast('上传维修单！');
				// }
				if(!photoList[0]){
					return that.$u.toast('上传修复图片！');
				}
				
				var yl = this.yongliaoList
				console.log(yl[0])
				if(yl[0]){
					var ylStr = ''
					for(let i in yl){
						if(!yl[i].mc){
							return that.$u.toast('请输入用料名称！');
						}
						if(!yl[i].sl){
							return that.$u.toast('请输入数量！');
						}
						if(!yl[i].jg){
							return that.$u.toast('请输入价格！');
						}
						if(i==0){
							var a = yl[i].mc+','+yl[i].sl+','+yl[i].jg
						}
						else{
							var a = ';'+yl[i].mc+','+yl[i].sl+','+yl[i].jg
						}
						ylStr = ylStr+a
					}
				}
				else{
					var ylStr = ''
				}
				uni.showModal({
				    title: '提示',
				    content: '确认完成？',
					confirmText:'确定',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							that.$http.post('/RepairMngApi/Execute?Docmd=complete', {
								RepairCode:that.RepairCode,
								ServiceList:'',
								RepairPhotos:photoList.toString(),
								RepairContent:that.RepairContent,
								RepairSituation:ylStr
							}).then(res => {
								that.$u.toast(res.ErrMsg);
								if(res.ErrCode==100){
									setTimeout(function(){
										that.load()
									},1000)
								}
							})
				        } 
				    }
				});				
			},

			uploadOk(data,index,lists){
				console.log(data)
				this.photoList.push(data.RetValue)
			},
			remove(index){
				console.log(index)
				this.photoList.splice(index,1)
			},
			load(){
				this.$http.get('/RepairMngApi/GetDetail', {
					params:{
						RepairCode:this.RepairCode
					}
				}).then(res => {
					
					res.Data.ApplyTime=res.Data.ApplyTime?res.Data.ApplyTime.replace('T',' '):''
					res.Data.DispatchTime=res.Data.DispatchTime?res.Data.DispatchTime.replace('T',' '):''
					res.Data.AcceptTime=res.Data.AcceptTime?res.Data.AcceptTime.substring(0,19).replace('T',' '):''
					res.Data.CompleteTime=res.Data.ApplyTime?res.Data.CompleteTime.substring(0,19).replace('T',' '):''
					res.Data.EvaluateTime=res.Data.EvaluateTime?res.Data.EvaluateTime.replace('T',' '):''
					if(res.Data.Photos){
						res.Data.Photos=res.Data.Photos.split(',')
						for(let index in res.Data.Photos){
							res.Data.Photos[index]=this.hostUrl+res.Data.Photos[index]
						}
					}
					if(res.Data.RepairPhotos){
						res.Data.RepairPhotos=res.Data.RepairPhotos.split(',')
						for(let index in res.Data.RepairPhotos){
							res.Data.RepairPhotos[index]=this.hostUrl+res.Data.RepairPhotos[index]
						}
					}
					if(res.Data.ServiceList){
						res.Data.ServiceList=res.Data.ServiceList.split(',')
						for(let index in res.Data.ServiceList){
							res.Data.ServiceList[index]=this.hostUrl+res.Data.ServiceList[index]
						}
					}
					//合并图片
					if(res.Data.RepairPhotos&&res.Data.ServiceList){
						res.Data.RepairPhotos=res.Data.RepairPhotos.concat(res.Data.ServiceList)
					}
					if(res.Data.EvaluatePhotos){
						res.Data.EvaluatePhotos=res.Data.EvaluatePhotos.split(',')
						for(let index in res.Data.EvaluatePhotos){
							res.Data.EvaluatePhotos[index]=this.hostUrl+res.Data.EvaluatePhotos[index]
						}
					}
					this.info=res.Data
				})
			}
		},
		onLoad(e) {
			console.log(e)
			var RepairCode=e.code
			this.RepairCode=RepairCode
			this.ProjectCode=e.ProjectCode
			this.load()
		}
	}
</script>

<style>
	page{
		background: #f6f6f6;
	}
	.tit{
		font-size: 28rpx;
		font-weight: bold;
		padding-bottom: 20rpx;
		margin-top: 20rpx;
	}
</style>
