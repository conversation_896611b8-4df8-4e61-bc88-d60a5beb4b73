<template>
  <view class="content">
    <view class="u-page">
      <view class="index-header">
        <view class="u-flex u-col-top u-row-between">
          <view class="u-flex-nowrap">
            <h1 class="u-font-30">{{ projectName }}</h1>
            <p class="u-font-24">{{ UserName }},您好</p>
          </view>
          <view class="u-flex-nowrap" v-if="changeBtnShow">
            <u-button type="primary" size="mini" @click="gotoUrl('../../yezhu/yz-project/yz-project')">切换项目</u-button>
          </view>
        </view>
      </view>
      <u-swiper :list="swiper"></u-swiper>
      <view class="bg-w">
        <scroll-view scroll-x="true" class="project-index-tools">
          <navigator class="i" :url="item.page" v-for="(item, index) in appList" v-if="item.show">
            <view>
              <image class="iicon" :src="item.icon"></image>
            </view>
            <text class="name">{{ item.name }}</text>
          </navigator>

          <navigator class="i" url="/pages/messageList/messageList">
            <view>
              <image class="iicon" src="../../static/icon-kf.png"></image>
            </view>
            <text class="name">在线客服</text>
          </navigator>
          <view class="i" @click="tel">
            <view>
              <image class="iicon" src="../../static/icon-tel.png"></image>
            </view>
            <text class="name">热线电话</text>
          </view>
          <view class="i" @click="wash()">
            <view>
              <image class="iicon" src="../../static/icon-xy.png"></image>
            </view>
            <text class="name">洗衣</text>
          </view>
          <!-- <navigator class="i" url="/pages/404/404">
						<view>
							<image class="iicon" src="../../static/iicon4.png" style="filter: gray; filter: grayscale(100%);-webkit-filter: grayscale(100%);opacity: 0.7;"></image>
						</view>
						<text class="name">视频监控</text>
					</navigator>
					<navigator class="i" url="/pages/404/404">
						<view>
							<image class="iicon" src="../../static/iicon3.png" style="filter: gray; filter: grayscale(100%);-webkit-filter: grayscale(100%);opacity: 0.6;"></image>
						</view>
						<text class="name">停车缴费</text>
					</navigator>
					<navigator class="i" url="/pages/404/404">
						<view>
							<image class="iicon" src="../../static/iicon6.png" style="filter: gray;filter: grayscale(100%); -webkit-filter: grayscale(100%);opacity: 0.9;"></image>
						</view>
						<text class="name">生活超市</text>
					</navigator> -->
        </scroll-view>
      </view>
      <view class="index-notic u-border-bottom u-flex u-row-between">
        <view class="u-padding-right-30 u-border-right">
          <!-- <image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/index-notic.png"></image> -->
          <image src="https://annyou.oss-cn-shenzhen.aliyuncs.com/1/1/img/index-notic.png"></image>
        </view>
        <view class="u-flex-nowrap u-flex-12 u-margin-left-26 u-margin-right-26">
          <swiper class="swiper" :indicator-dots="indicatorDots" :autoplay="true" :interval="5000" :duration="500" :vertical="true">
            <swiper-item v-for="(item, index) in indexnotic" :key="index">
              <navigator :url="'../noticeDetail/noticeDetail?NoticeCode=' + item.code">
                <view class="notic_t">
                  <u-tag class="u-margin-right-10" text="已读" type="info" size="mini" mode="dark" v-if="item.IsSee == 1" />
                  <u-tag class="u-margin-right-10" text="未读" type="error" size="mini" mode="dark" v-else />

                  {{ item.date }}
                </view>
                <text class="notic_f">{{ item.title }}</text>
              </navigator>
            </swiper-item>
          </swiper>
        </view>
        <view>
          <u-button type="info" size="medium" @click="moreInfo">查看全部</u-button>
        </view>
      </view>
      <view class="project-index-list">
        <scroll-view scroll-x="true" class="tab-hd">
          <view :class="'i ' + (index == tabCurrent ? 'active' : '')" :key="index" v-for="(item, index) in tabItem" @tap="tapItem(index)">{{ item }}</view>
        </scroll-view>
        <view v-if="tabCurrent == 0">
          <view class="list">
            <view class="i flex border-all" @click="gotoItem(item.Type, item.Code)" :key="index" v-for="(item, index) in orderList">
              <view class="flex-hd image">
                <image v-if="item.Type == '卤菜外卖'" src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_wm.png"></image>
                <image v-else-if="item.Type == '餐食预留'" src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_cs.png"></image>
                <image v-else-if="item.Type == '会议预约'" src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_hy.png"></image>
                <image v-else src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_bx.png"></image>
                <view>{{ item.Type }}</view>
              </view>
              <view class="flex-bd">
                <view class="t">单号：{{ item.OrderNo }}</view>
                <view class="info" v-if="item.Type == '卤菜外卖'">取餐时间：{{ item.BeginTime }}</view>
                <view class="info" v-else-if="item.Type == '餐食预留'">预留时间：{{ item.BeginTime }}</view>
                <view class="info" v-else-if="item.Type == '会议预约'">会议时间：{{ item.BeginTime }}</view>
                <view class="info" v-else>预留时间：{{ item.BeginTime }}</view>
                <view class="info">申请时间：{{ item.ApplyTime }}</view>
              </view>
              <view class="flex-hd">
                <u-tag v-if="item.ApplyStatus == 0" type="info" size="mini" text="待审核" />
                <u-tag v-else-if="item.ApplyStatus == 1" type="success" size="mini" text="预约成功" />
                <u-tag v-else-if="item.ApplyStatus == 2" type="error" size="mini" text="预约失败" />
                <u-tag v-else-if="item.ApplyStatus == 3" type="warning" size="mini" text="预约取消" />
                <u-tag v-else-if="item.ApplyStatus == 4" type="success" size="mini" text="进行中" />
                <u-tag v-else-if="item.ApplyStatus == 5" type="success" size="mini" text="待评价" />
                <u-tag v-else type="success" size="mini" text="已完成" />
              </view>
            </view>
          </view>
          <u-empty v-if="!orderList" class="u-text-center" text="暂无内容" margin-top="300"></u-empty>
          <view v-else class="more-btn">
            <u-button size="medium" @click="gotoUrl('../../yezhu/yz-orderList/yz-orderList')">查看全部</u-button>
          </view>
        </view>
        <view v-else>
          <view class="list">
            <navigator class="i flex border-all" :key="index" v-for="(item, index) in repairList" :url="'../../yezhu/yz-repairDetail/yz-repairDetail?code=' + item.RepairCode">
              <view class="flex-bd">
                <view class="t">单号：{{ item.RepairNo }}</view>
                <view class="info">维修人：{{ item.applyusername }}</view>
                <view class="info">报修时间：{{ item.ApplyTime }}</view>
              </view>
              <view class="flex-hd">
                <u-tag v-if="item.RepairStatus == 0" type="info" size="mini" text="待接单" />
                <u-tag v-if="item.RepairStatus == 2" type="success" size="mini" text="已接单" />
                <u-tag v-if="item.RepairStatus == 3" type="success" size="mini" text="已完成" />
                <u-tag v-if="item.RepairStatus == 4" type="success" size="mini" text="已评价" />
              </view>
            </navigator>
          </view>
          <u-empty v-if="!repairList" class="u-text-center" text="暂无内容" margin-top="300"></u-empty>
          <view v-else class="more-btn">
            <u-button size="medium" @click="gotoUrl('../../yezhu/yz-repairList/yz-repairList')">查看全部</u-button>
          </view>
        </view>
      </view>
    </view>
    <u-tabbar v-model="current" :list="tabbar" :inactive-color="inactiveColor" :activeColor="ThemeColor" @change="change"></u-tabbar>
    <u-mask class="authshow u-text-center" :show="AuthShow">
      <view class="auth_box u-flex u-flex-wrap u-row-center">
        <view>
          <image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/auth_img.png" @click="authlink"></image>

          <view @click="AuthShow = false">
            <u-icon name="close-circle" color="#999" size="60" class="u-padding-30"></u-icon>
          </view>
        </view>
      </view>
    </u-mask>
  </view>
</template>
<script>
const app = getApp();
export default {
  data() {
    return {
      ThemeColor: "",
      inactiveColor: "#909399",
      AuthShow: "",
      UserName: "用户名",
      UserType: "1",
      projectName: "欢迎使用妙洁物业管理平台",
      projectCode: "",
      indexnotic: [
        {
          IsSee: 0,
          date: "2024-01-01",
          title: "关于物业进行安全管理的通知",
        },
      ],
      swiper: "",
      tabbar: [
        {
          iconPath: "shouye",
          selectedIconPath: "tabbar-icon01_h",
          text: "首页",
          customIcon: true,
          pagePath: "/pages/yz-home/yz-home",
        },
        {
          iconPath: "nfc",
          selectedIconPath: "nfc1",
          text: "NFC",
          customIcon: true,
          pagePath: "/pages/nfc/nfc",
        },
        {
          iconPath: "yonghu",
          selectedIconPath: "wode",
          text: "我的",
          customIcon: true,
          pagePath: "/pages/yz-user/yz-user",
        },
      ],
      appList: [
        {
          name: "我要报修",
          icon: "../../static/iicon1.png",
          page: "/yezhu/yz-repairAdd/yz-repairAdd",
          show: false,
        },
        {
          name: "会议室预约",
          icon: "../../static/iicon5.png",
          page: "/yezhu/yz-meetingAdd/yz-meetingAdd",
          show: false,
        },
        {
          name: "卤菜外卖",
          icon: "../../static/iicon2.png",
          page: "/yezhu/yz-foodAdd/yz-foodAdd",
          show: false,
        },
        {
          name: "餐食预留",
          icon: "../../static/iicon7.png",
          page: "/yezhu/yz-eatAdd/yz-eatAdd",
          show: false,
        },
        {
          name: "包厢预定",
          icon: "../../static/iicon8.png",
          page: "/yezhu/yz-roomAdd/yz-roomAdd",
          show: false,
        },
      ],
      current: 0,
      swiperCurrent: 0,
      orderList: "",
      repairList: "",
      tabItem: ["我的预约", "我的报修"],
      tabCurrent: 0,
      changeBtnShow: true,
      hostUrl: app.globalData.hostUrl,
    };
  },
  onLoad() {
    this.ThemeColor = getApp().globalData.ThemeColor;

    var UserType = uni.getStorageSync("UserType");
    var projectName = uni.getStorageSync("projectName");
    var IsCheck = uni.getStorageSync("IsCheck");
    var homeForm = uni.getStorageSync("homeForm");
    //判断是否业主是否审核通过
    if (UserType == 1 && IsCheck == 2 && homeForm != "projectChange") {
      this.$http
        .get("/HomepageMng/GetProjectAll", {
          params: {
            UserCode: uni.getStorageSync("UserCode"),
          },
        })
        .then((res) => {
          var list = JSON.parse(res.Data);
          //是否项目数目大于1，则弹窗切换
          if (list.length > 1) {
            uni.showModal({
              title: "是否切换项目？",
              content: "当前项目：" + projectName,
              confirmText: "切换",
              cancelText: "不切换",
              success: function (res) {
                if (res.confirm) {
                  uni.navigateTo({
                    url: "../../yezhu/yz-project/yz-project",
                  });
                }
              },
            });
          } else {
            this.changeBtnShow = false;
          }
        });
    }
    uni.setStorageSync("homeForm", "");

    this.getNotice();
    this.getOrder();
    this.getRepair();
    this.getSlide();
  },
  onShow() {
    //当审核中每次更新信息
    if (uni.getStorageSync("IsCheck") == 1) {
      this.authUpdate().then((res) => {
        uni.setStorageSync("IsCheck", res.Data.IsCheck);
        uni.setStorageSync("projectName", res.Data.DefaultProjectName);
        uni.setStorageSync("projectCode", res.Data.DefaultProject ? res.Data.DefaultProject.ProjectCode : "");
        uni.setStorageSync("UserRemark", res.Data.UserRemark);
        this.showFn();
      });
    } else {
      this.showFn();
    }
  },
  methods: {
    wash() {
      uni.navigateToMiniProgram({
        appId: "wxbf12f2c57e367aed",
        path: "/appointment/selectOrderList/selectOrderList?sid=7e6bb5fa-c45a-48c9-a59a-75ac8c469eca",
        envVersion: "release", // 开发版、体验版、正式版
        success(res) {},
      });
    },
    tel() {
      uni.makePhoneCall({
        phoneNumber: "051388888888",
      });
    },
    change(e) {
      console.log(e);
      if (e == 1) {
        uni.navigateTo({
          url: "../nfc/nfc",
        });
      }
    },
    showFn() {
      var IsCheck = uni.getStorageSync("IsCheck");
      //当未审核或审核不通过则弹出审核
      if (IsCheck == 0 || IsCheck == 3) {
        this.AuthShow = true;
      }
      this.UserType = uni.getStorageSync("UserType");
      this.projectName = uni.getStorageSync("projectName");
      this.projectCode = uni.getStorageSync("projectCode");
      this.UserName = uni.getStorageSync("UserName");
      this.IsCheck = IsCheck;

      if (uni.getStorageSync("projectCode")) {
        this.$http
          .post("/ProjectMngApi/GetProjectDeatil", {
            UserCode: uni.getStorageSync("UserCode"),
            ProjectCode: uni.getStorageSync("projectCode"),
          })
          .then((res) => {
            console.log(res);
            if (res.Data.Project.EnableService) {
              var EnableService = res.Data.Project.EnableService.split(",");
              if (EnableService.indexOf("设备报修") > -1) {
                this.appList[0].show = true;
              } else {
                this.appList[0].show = false;
              }
              if (EnableService.indexOf("会议预约") > -1) {
                this.appList[1].show = true;
              } else {
                this.appList[1].show = false;
              }
              if (EnableService.indexOf("卤菜外卖") > -1) {
                this.appList[2].show = true;
              } else {
                this.appList[2].show = false;
              }
              if (EnableService.indexOf("餐食预留") > -1) {
                this.appList[3].show = true;
              } else {
                this.appList[3].show = false;
              }
              if (EnableService.indexOf("包厢预定") > -1) {
                this.appList[4].show = true;
              } else {
                this.appList[4].show = false;
              }
            } else {
              this.appList[0].show = false;
              this.appList[1].show = false;
              this.appList[2].show = false;
              this.appList[3].show = false;
              this.appList[4].show = false;
            }
          });
      }
    },
    tapItem(e) {
      this.tabCurrent = e;
      this.swiperCurrent = e;
    },
    transition(e) {
      let dx = e.detail.dx;
      this.tabCurrent = dx;
    },
    animationfinish(e) {
      let current = e.detail.current;
      this.swiperCurrent = current;
      this.tabCurrent = current;
    },
    moreInfo() {
      uni.navigateTo({
        url: "../user-notice/user-notice",
      });
    },
    authlink() {
      uni.navigateTo({
        url: "/yezhu/yz-auth/yz-auth",
      });
      this.AuthShow = false;
    },
    gotoUrl(e) {
      if (this.authCheck()) {
        uni.navigateTo({
          url: e,
        });
      }
    },
    authCheck(e) {
      var IsCheck = uni.getStorageSync("IsCheck");
      if (IsCheck == 2) {
        return true;
      } else if (IsCheck == 1) {
        this.$u.toast("正在认证中");
      } else {
        this.AuthShow = true;
        return false;
      }
    },
    getNotice() {
      this.$http
        .post("/NoticeMng/GetListAll?p=1&ps=10", {
          UserCode: uni.getStorageSync("UserCode"),
        })
        .then((res) => {
          var list = [];
          for (let index in res.Data) {
            var item = {
              title: res.Data[index].Notice.Title,
              date: res.Data[index].Notice.CreateDatetimeText,
              code: res.Data[index].NoticeCode,
              IsSee: res.Data[index].IsSee,
            };
            list.push(item);
          }
          //this.indexnotic=list
        });
    },
    getOrder() {
      this.$http
        .get("/MeetingMngApi/GetList", {
          params: {
            p: 1,
            ps: 5,
            UserCode: uni.getStorageSync("UserCode"),
            projectCode: uni.getStorageSync("projectCode"),
            ApplyStatus: "",
            OrderNo: "",
          },
        })
        .then((res) => {
          for (let index in res.Data) {
            res.Data[index].ApplyTime = res.Data[index].ApplyTime.replace(/\//g, "-");
          }
          this.orderList = res.Data;
        });
    },
    getRepair() {
      this.$http
        .get("/RepairMngApi/GetListAll", {
          params: {
            p: 1,
            ps: 5,
            UserCode: uni.getStorageSync("UserCode"),
            projectCode: uni.getStorageSync("projectCode"),
            RepairStatus: "",
            RepairNo: "",
          },
        })
        .then((res) => {
          for (let index in res.Data) {
            res.Data[index].ApplyTime = res.Data[index].ApplyTime.replace("T", " ");
          }
          this.repairList = res.Data;
        });
    },
    gotoItem(type, code) {
      if (type == "卤菜外卖") {
        uni.navigateTo({
          url: "/yezhu/yz-foodDetail/yz-foodDetail?code=" + code,
        });
      } else if (type == "餐食预留") {
        uni.navigateTo({
          url: "/yezhu/yz-eatDetail/yz-eatDetail?code=" + code,
        });
      } else if (type == "会议预约") {
        uni.navigateTo({
          url: "/yezhu/yz-meetingDetail/yz-meetingDetail?code=" + code,
        });
      } else {
        uni.navigateTo({
          url: "/yezhu/yz-roomDetail/yz-roomDetail?code=" + code,
        });
      }
    },
    authUpdate() {
      return new Promise((resolve, reject) => {
        this.$http
          .get("/HomepageMng/GetUserInfo", {
            params: {
              CellPhone: uni.getStorageSync("CellPhone"),
            },
          })
          .then((res) => {
            resolve(res);
          });
      });
    },
    getSlide() {
      this.$http
        .get("HomepageMng/GetCmsInfo?channelCode=banner", {
          params: {},
        })
        .then((res) => {
          var swiper = [];
          for (let index in res.Data) {
            swiper.push({
              image: this.hostUrl + res.Data[index].SmallImage,
              title: res.Data[index].InfoTitle,
            });
          }
          this.swiper = swiper;
        });
    },
  },
  onPullDownRefresh() {
    this.getNotice();
    this.getOrder();
    this.getRepair();
    this.getSlide();
    setTimeout(function () {
      uni.stopPullDownRefresh();
    }, 1000);
  },
};
</script>
<style>
@import url("style.css");

.more-btn {
  text-align: center;
  margin: 30rpx auto;
}
.project-index-list .u-tag {
  width: 110rpx;
  text-align: center;
}
.iicon {
  width: 80rpx;
  height: 80rpx;
}
</style>
