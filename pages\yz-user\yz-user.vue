<template>
	<view class="content border-top">
		<view class="u-page">
			<view class="bg-w  u-p-30 ">
				<view class="u-flex user-top">
					<view class="u-flex-3">
						<u-avatar v-if="HeadImg" :src="(hostUrl+HeadImg)" mode="circle" size="160"></u-avatar>
						<view v-else style="width: 160rpx;height: 160rpx;border-radius: 50%;overflow:hidden;">
							<open-data type="userAvatarUrl"></open-data>  
						</view> 
					</view>
					<view class="u-flex-6">
						<view class="u-p-l-30">
							<h4>{{UserName}}</h4>
							<text v-if="JobNumber">工号：{{JobNumber}}</text>
							<text>{{roleName}}</text>
						</view>
					</view>
					<view class="u-flex-3">
						<u-image class="u-absolute" style="width:22vw;top:40rpx;right:40rpx;" src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/user_logo.png"
						 mode="widthFix" />
					</view>
				</view>
				<view v-if="IsCheck==2" class="user-bar u-flex u-p-20 u-m-t-30" @click="goToPage('/pages/user-card/user-card')">
					<view class="u-flex-1 u-p-r-20">
						<u-image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/userbar-icon.png" mode="widthFix"></u-image>
					</view>
					<view class="u-flex-7">
						<h1 v-if="UserType==1">认证业主</h1>
						<h1 v-else>认证业主</h1>
						<text>{{projectName}}</text>
					</view>
					<view class="u-flex-3 u-text-right">
						我的名片<u-icon name="right" custom-prefix="custom-icon" size="30" color="#9d6820"></u-icon>
					</view>
				</view>
			</view>
			<view class="u-m-t-15">
				<u-cell-group class="user-cell">
					<u-cell-item title="个人信息" :arrow="true" @click="goToPage('/pages/user-info/user-info')">
						<view class="icon-bg">
							<u-icon name="gerenxinxi" custom-prefix="custom-icon" size="50"></u-icon>
						</view>
						<block v-if="HeadImg">已完善</block>
						<block v-else>未完善</block>
					</u-cell-item>
					<u-cell-item title="更换手机" :arrow="true" @click="goToPage('/pages/user-changetel/user-changetel')">
						<view class="icon-bg" style="background-color:#d6d793;">
							<u-icon name="shouji1" custom-prefix="custom-icon" size="50"></u-icon>
						</view>
						{{CellPhone}}
					</u-cell-item>
					<u-cell-item title="解绑微信" :arrow="true" @click="goToPage('/pages/user-wechart/user-wechart')">
						<view class="icon-bg" style="background-color:#8bcfc3;">
							<u-icon name="weixin" custom-prefix="custom-icon" size="50"></u-icon>
						</view>
					</u-cell-item>
				</u-cell-group>
			</view>
			<view class="u-m-t-15">
				<u-cell-group class="user-cell">
					<u-cell-item title="我的项目" :arrow="true" @click="goToPage('/yezhu/yz-project/yz-project',true)">
						<view class="icon-bg" style="background-color:#b5ddff;">
							<u-icon name="xiangmu" custom-prefix="custom-icon" size="50"></u-icon>
						</view>
						{{projectCount}}
					</u-cell-item>
					<u-cell-item title="我的预约" :arrow="true" @click="goToPage('../../yezhu/yz-orderList/yz-orderList',true)">
						<view class="icon-bg" style="background-color:#dfd0ff;">
							<u-icon name="xiangmu" custom-prefix="custom-icon" size="50"></u-icon>
						</view>
						{{orderCount}}
					</u-cell-item>
					<u-cell-item title="我的消息" :arrow="true" @click="goToPage('/pages/user-inform/user-inform',true)">
						<view class="icon-bg" style="background-color:#fec791;">
							<u-icon name="xiaoxi" custom-prefix="custom-icon" size="50"></u-icon>
						</view>
						今日 {{messageCount}}
					</u-cell-item>
				</u-cell-group>
			</view>	
			<view class="u-m-80"><u-button type="info" shape="circle" @click="logout">退出账户</u-button></view>
		</view>
		
		<u-tabbar v-model="current" :list="tabbar" :inactive-color="inactiveColor" :activeColor="ThemeColor" @change="change"></u-tabbar>
		<u-toast ref="uToast" ></u-toast>
		<u-mask class="authshow u-text-center" :show="AuthShow">
			<view class="auth_box u-flex u-flex-wrap u-row-center">
				<view>
					<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/auth_img.png" @click="authlink"></image>
					<view @click="AuthShow = false">
						<u-icon name="close-circle" color="#999" size="60" class="u-padding-30"></u-icon>
					</view>
				</view>
			</view>
		</u-mask>
		
	</view>
</template>

<script>
	const app = getApp();
	import utils from "../../components/utils.js"
	export default {
		data() {
			return {
				ThemeColor: '',
				inactiveColor: '#909399',
				tabbar: [{
						iconPath: 'shouye',
						selectedIconPath: 'tabbar-icon01_h',
						text: '首页',
						customIcon: true,
						pagePath: '/pages/yz-home/yz-home'
					},
					{
						iconPath: 'nfc',
						selectedIconPath: 'nfc1',
						text: 'NFC',
						customIcon: true,
						pagePath: '/pages/nfc/nfc'
					},
					{
						iconPath: 'yonghu',
						selectedIconPath: 'wode',
						text: '我的',
						customIcon: true,
						pagePath: '/pages/yz-user/yz-user'
					}
				],
				AuthShow: false,
				UserName: '',
				IsCheck: '',
				CellPhone: '',
				messageCount: 0,
				projectCount:0,
				orderCount:'',
				wxInfo: '',
				projectName: '',
				HeadImg:'',
				roleName:'',
				UserType:'',
				logoutshow:false,
				hostUrl:app.globalData.hostUrl,
			}
		},
		onLoad() {
			//主题色
			this.ThemeColor = getApp().globalData.ThemeColor;
			this.HeadImg=uni.getStorageSync('HeadImg')
		},
		onShow() {
			if(uni.getStorageSync('IsCheck')==1){
				this.authUpdate().then(res=>{
					uni.setStorageSync('IsCheck',res.Data.IsCheck)
					uni.setStorageSync('projectName',res.Data.DefaultProjectName)
					uni.setStorageSync('projectCode',res.Data.DefaultProject?res.Data.DefaultProject.ProjectCode:'')
					uni.setStorageSync('UserRemark',res.Data.UserRemark)
					uni.setStorageSync('roleName',utils.getRole(res.Data.UserRemark))
					this.showFn()
				})	
			}
			else{
				this.showFn()
			}
			this.getMessageCount()
			this.getProjectCount()
			this.getOrderCount()
		},
		methods: {
			change(e){
				console.log(e)
				if(e==1){
					uni.navigateTo({
						url:"../nfc/nfc"
					})
				}
			},
			showFn(){
				var IsCheck=uni.getStorageSync('IsCheck')
				if(IsCheck==0||IsCheck==3){
					this.AuthShow=true
				}
				this.IsCheck=uni.getStorageSync('IsCheck')
				this.CellPhone=uni.getStorageSync('CellPhone')
				this.UserName=uni.getStorageSync('UserName')
				this.UserType=uni.getStorageSync('UserType')
				this.projectName=uni.getStorageSync('projectName')
				this.projectCode=uni.getStorageSync('projectCode')
				this.roleName=uni.getStorageSync('roleName')
				this.JobNumber=uni.getStorageSync('JobNumber')
				this.HeadImg=uni.getStorageSync('HeadImg')
			},
			getMessageCount() {
				this.$http.get('/UserApi/GetMessageNumber', {
					params:{
						UserCode:uni.getStorageSync('UserCode')
					}
				}).then(res => {
					this.messageCount = res.Data
				})
			},
			getProjectCount() {
				this.$http.get('/HomepageMng/GetProjectAll', {
					params:{
						UserCode:uni.getStorageSync('UserCode')
					}
				}).then(res => {
					var list=JSON.parse(res.Data)
					this.projectCount = list.length
				})
			},
			getOrderCount(){
				this.$http.get('/UserApi/GetMyApplyNum', {
					params:{
						UserCode:uni.getStorageSync('UserCode'),
						projectCode:uni.getStorageSync('projectCode')
					}
				}).then(res => {
					this.orderCount=res.Data.yYNum
				})
			},
			goToPage(e, auth) {
				if (auth) {
					if(this.authCheck()){
						uni.navigateTo({
							url: e
						})
					}
					
				} else {
					uni.navigateTo({
						url: e
					})
				}
			},
			authlink() {
				uni.navigateTo({
					url: '/yezhu/yz-auth/yz-auth'
				});
				this.AuthShow = false
			},
			authCheck() {
				var IsCheck = uni.getStorageSync('IsCheck')
				if (IsCheck == 2) {
					return true				
				} else if(IsCheck == 1){
					this.$u.toast('正在认证中');
				} else {
					this.AuthShow = true
					return false
				}
			},
			authUpdate(){
				return new Promise((resolve, reject)=>{
					this.$http.get('/HomepageMng/GetUserInfo', {
						params:{
							CellPhone: uni.getStorageSync('CellPhone')
						}
					}).then(res => {
						resolve(res)						
					})
				})
			},
			logout(){
				// 询问是否退出登录？
				uni.showModal({
					content:'是否要退出登录',
					success:(res) => {
						if (res.confirm) {
							uni.clearStorage();
							uni.setStorageSync('ThemeColor', '#35318f');							
							this.$u.toast("退出成功");
							setTimeout(function(){
								console.log(1)
								uni.reLaunch({
								    url: '/pages/login/login'
								});
											
							},1000);
						}
					}
				})
			}
			
		}
	}
</script>

<style>
	open-data{
		vertical-align: top;
		width:160rpx;
		height: 160rpx;
	}
</style>
