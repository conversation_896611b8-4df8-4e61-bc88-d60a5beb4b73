.flex {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}
.flext {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: flex-start;
  -webkit-box-align: flex-start;
  -webkit-align-items: flex-start;
}
.flexb {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: flex-end;
  -webkit-box-align: flex-end;
  -webkit-align-items: flex-end;
}
.flex-bd {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  min-width: 0;
}
.m30 {
  margin: 30rpx;
}

.status.success {
  border: 1px solid #53c21d;
  color: #53c21d;
  background: #f5fff0;
}
.status.error {
  border: 1px solid #fa4e0a;
  color: #fa4e0a;
  background: #fff0f0;
}
.status.warning {
  border: 1px solid #ffa500;
  color: #ffa500;
  background: #fdf6ec;
}

.status.normal {
  border: 1px solid #5771f9;
  color: #5771f9;
  background: #f0f5ff;
}

.status.info {
  border: 1px solid #ddd;
  color: #666;
  background: #f4f4f5;
}

.user {
}
.user .user-info {
  position: relative;
  margin: 30rpx;
  border-radius: 10px;
  overflow: hidden;
  padding: 60rpx 40rpx;
}
.user .user-info .bg-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.user .user-info .bg-img image {
  width: 100%;
  height: 100%;
}
.user .user-info .wrap {
  position: relative;
  z-index: 1;
}
.user .user-info .avatar image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 30rpx;
}
.user .user-info .name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.user .user-info .code {
  font-size: 24rpx;
  font-weight: normal;
  margin-left: 10rpx;
}
.user .user-info .dept {
  font-size: 24rpx;
  color: #666;
}
.user .card-list {
  margin: 30rpx;
}
.attendance-title {
  margin: 30rpx;
}
.attendance-title .flex-bd {
  font-size: 28rpx;
}
.attendance-title .arrow-down {
  width: 30rpx;
  height: 30rpx;
}
.attendance-list .item {
  margin: 30rpx;
  padding: 30rpx;
  border-radius: 10px;
  background-color: #fff;
}
.attendance-list .item .icon {
  width: 100rpx;
  height: 100rpx;
}
.attendance-list .item .info {
  margin-left: 30rpx;
}
.attendance-list .item .info .time {
  font-size: 28rpx;
  margin-bottom: 10rpx;
}
.attendance-list .item .info .desc {
  font-size: 28rpx;
  color: #999;
}
.attendance-list .item .status {
  font-size: 24rpx;
  padding: 8rpx 10rpx;
  border-radius: 4px;
  min-width: 100rpx;
  text-align: center;
}
.sign-top {
  padding: 30rpx;
  background-color: #fff;
}
.sign-top .avatar {
  width: 110rpx;
  height: 110rpx;
}
.sign-top .avatar image {
  width: 100%;
  height: 100%;
  border-radius: 10px;
}
.sign-top .flex-bd {
  margin-left: 30rpx;
}
.sign-top .flex-bd .name {
  font-size: 28rpx;
}
.sign-top .flex-bd .name .code {
  font-size: 24rpx;
}
.sign-top .flex-bd .dept {
  font-size: 24rpx;
}
.sign-top .btn {
  margin-left: 30rpx;
  text-align: center;
  font-size: 24rpx;
}
.sign-top .btn image {
  width: 46rpx;
  height: 46rpx;
}

.sign-info {
  padding: 30rpx;
  background-color: #fff;
  line-height: 1.8;
}
.sign-info .no {
}
.sign-info .title {
  font-size: 28rpx;
}
.sign-info .info {
  font-size: 24rpx;
  color: #999;
}
.sign-btn {
  padding: 30rpx;
  background-color: #fff;
}
.sign-btn .title {
  font-size: 24rpx;
  color: #666;
  text-align: center;
}
.sign-btn .btn {
  margin: 160rpx auto 30rpx auto;
  width: 240rpx;
  height: 240rpx;
  border-radius: 50%;
  background-image: linear-gradient(135deg, #5872f9, #8ea0fe);
  color: #fff;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.sign-btn .btn .t1 {
  font-size: 32rpx;
  margin-bottom: 10rpx;
}
.sign-btn .btn .t2 {
  font-size: 24rpx;
}
.sign-btn .addr {
  font-size: 24rpx;
  text-align: center;
  margin-bottom: 160rpx;
}
