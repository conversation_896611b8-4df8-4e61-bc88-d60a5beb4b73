body {
	background: #f8f9fb !important;
}
.flex {
	display: flex;
}
.ims {
	.tabbar-icon {
		width: 36rpx;
		height: 36rpx;
	}
	.home-header {
		line-height: 44px;
		box-sizing: border-box;
		display: flex;
		padding: 46px 5vw 0 5vw;
		.item {
			padding-right: 20px;
			color: #909193;
			font-size: 30rpx;
			&.active {
				font-weight: bold;
				color: #303133;
			}
		}
	}
	.home-main {
		padding: 0 5vw;
		.home-userinfo {
			display: flex;
			justify-content: space-between;
			margin-bottom: 20rpx;
			.item {
				line-height: 80rpx;
				font-size: 28rpx;
				.text-right {
					flex: 1;
				}
			}
		}
		.home-link {
			display: flex;
			gap: 15px;
			.item {
				flex: 1;
				width: 100%;
				padding: 0;
				image {
					width: 100%;
				}
			}
		}
		.card {
			background: #fff;
			padding: 10px;
			margin: 10px 0 0 0;
			border-radius: 10px;
			.card-title {
				color: #6c7278;
				font-size: 28rpx;
				line-height: 46rpx;
			}
			.uv-grid {
				.uv-grid-item {
					box-sizing: border-box;
					padding: 30rpx 0;
					.icon {
						width: 65%;
						image {
							width: 100%;
							height: auto;
						}
					}
					.grid-text {
						color: #000;
						font-size: 28rpx;
						line-height: 60rpx;
					}
				}
			}
		}
	}
	.page-wrap {
		padding: 0 3vw;
		.card {
			background: #fff;
			padding: 10px;
			margin: 10px 0 0 0;
			border-radius: 10px;
			.card-title {
				font-size: 28rpx;
				line-height: 46rpx;
				
			}
			.model-text{
				display: inline-block;
				position: relative;
				&::after{
					content: "";
					display: block;width: 100%;height: 4px;background: #5771f9;position: absolute;left: 0;bottom:-4px;border-radius: 2px;
				}
			}
			.card-header {
				font-size: 28rpx;
				line-height: 46rpx;
				padding-bottom: 5px;
			}
			.card-body {
				padding: 10px 0;
			}
		}
	}

	.page-tab {
		padding: 30rpx 0;
	}
	.form-pannel {
		.uv-form-item {
			position: relative;
			background: #fff;
			padding: 0 10px;
			border-radius: 15rpx;
			margin-top: 10px;
			.uv-form-item__body__left__content__labe {
				font-size: 28rpx;
				color: #303133;
			}
			.uv-input__content__field-wrapper__field {
				font-size: 30rpx;
				line-height: 30px;
				height: 30px;
			}
			.input-picker {
				display: flex;
				width: 100%;
				align-items: center;
				justify-content: center;
				.text {
					flex: 1;
					font-size: 30rpx;
					line-height: 30px;
					height: 30px;
				}
			}
			.add-files {
				position: absolute;
				right: 10px;
				top: 10px;
			}
			.uv-list {
				width: 100%;
				padding-top: 5px;
				border-top: #e5e5e5 1px solid;
				.uv-list-item {
					width: 100%;

					.file-item {
						display: flex;
						align-items: center;
						justify-content: space-between;
						width: 100%;
						gap: 10px;
						.file-title {
							flex: 1;
							font-size: 28rpx;
							line-height: 80rpx;
							height: 80rpx;
							padding-left: 10px;
							text-overflow: ellipsis;
							white-space: nowrap;
							overflow: hidden;
						}
					}
				}
			}
		}
	}
	.fixed-tab {
		position: relative;
		height:54px;
		.fixed-tab-box {
			position: fixed;
			z-index:10;
			background: #f8f9fb;
			width: 100%;
			top: 0;
			.page-tab {
				padding: 0;
			}
		}
	}
	.fixed-footer {
		position: relative;
		height: 80px;
		.fixed-footer-box {
			position: fixed;
			z-index: 200;
			background: #f8f9fb;
			width: 100%;
			bottom: 0;

			.button-box {
				display: flex;
				padding: 10px 3vw;
				gap: 10px;
				.item {
					flex: 1;
					.uv-icon {
						margin-right: 5px;
					}
				}
			}
		}
	}
	.list {
		padding-bottom: 10px;
		.list-item {
			background: #fff;
			border-radius: 10px;
			padding: 10px;
			margin-top: 10px;
			.list-item-header {
				display: flex;
				align-items: center;
				justify-content: space-between;
				.title-h1 {
					flex: 1;
					font-size: 26rpx;
					line-height: 50rpx;
				}
			}
			.list-item-body {
				.file-title {
					font-size: 28rpx;
					line-height: 56rpx;
				}
				.li-item {
					font-size: 26rpx;
					color: #909193;
					line-height: 46rpx;
					.primary-color {
						color: #5771f9;
					}
				}
			}
		}
	}
	.audit-list{
		padding-bottom: 10px;
		.list-item {
			background: #fff;
			border-radius: 10px;
			padding: 10px;
			margin-top: 10px;
			.list-item-header {
				display: flex;
				align-items: center;
				justify-content: space-between;
				.title-h1 {
					flex: 1;
					font-size: 26rpx;
					line-height: 50rpx;
				}
				padding-bottom:10px;
			}
			.list-item-body {
				display: flex;align-items: top;gap:10px;
				.icon{
					width:100rpx;
					image{width: 100%;}
				}
				.desc{flex:1;
					
					.li-item {
						font-size: 26rpx;
						color: #909193;
						line-height: 46rpx;
						.primary-color {
							color: #5771f9;
						}
					}
				
				}
				
			}
		}
		
	}
	.detail-cell {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-top: 10px;
		.title-h1 {
			flex: 1;
			font-size: 26rpx;
			line-height: 50rpx;
		}
	}
	.field-list {
		.field-list-item {
			display: flex;
			align-items: start;
			justify-content: space-between;
			gap: 10px;
			padding: 3px 0;
			.label {
				color: #767a82;
				font-size: 26rpx;
				line-height: 52rpx;
				min-width: 100px;
				white-space: nowrap;
			}
			.text {
				flex: 1;
				font-size: 26rpx;
				line-height: 52rpx;
			}
		}
	}
	.empty-tip {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 60rpx 0;
	}
	.approve-progress {
		
		.uv-text__value--main {
			text-align: center;
			font-size: 20rpx !important;
		}
		.uv-text__value--content {
			text-align: center;
			font-size: 20rpx !important;
		}
		/* .uv-steps-item[status="finish"],.uv-steps-item[status="wait"]{
			.uv-steps-item__wrapper__circle{background: #5771f9 !important;color: #fff !important;border: 0 !important;}
		}
		.uv-steps-item[status="error"]{
			.uv-steps-item__wrapper__circle{background:#f56c6c !important;color: #fff !important;border: 0 !important;}
		}
		.uv-icon__icon--error{color: #fff;} */
	}

	.search-container{
		display: flex;width: 100%;justify-content: space-between;align-items: center;
		.flex-item{flex:1}
		.search-icon{padding: 0 10px;}
		
		
	}
}
