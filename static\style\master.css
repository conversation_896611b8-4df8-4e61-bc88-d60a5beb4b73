body{background-color:#f2f2f2}
.bg-w{background: #fff;}
.index-header{padding:2vw 5vw;width:100%;line-height:40rpx}
.index-header h1{font-weight:700}
.u-item-title{position:relative}
.u-item-title::before{display:inline-block;vertical-align:middle;margin-right:18rpx;width:8rpx;height:30rpx;content:"";border-radius:2rpx;background-color:#35318f}
.card-reset .u-card__body{padding:0 30rpx!important}
.card-reset .u-cell{padding:18rpx 0rpx!important;align-items:flex-start!important}
.card-readyonly .u-card__body{padding:0 30rpx!important}
.card-readyonly .u-cell{padding:12rpx 0rpx!important;align-items:flex-start!important}
.card-readyonly .u-cell .u-cell__value{display:block;text-align:left;color:#333}
.index-notic{background:#fff;padding:15rpx 30rpx}
.index-notic image{width:80rpx;height:80rpx}
.index-notic .u-btn{padding:0 30rpx!important}
.index-notic .u-btn::after{display:none!important}
.index-notic swiper{height:100rpx}
.index-notic .notic_t{color:#999;line-height:50rpx;font-size:24rpx}
.index-notic .notic_f{display:block;line-height:50rpx;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;font-size:28rpx}
.auth_box{height:100%;}
.auth_box image{width:52vw;height:56vw}
.fixed-box-100{height:100rpx;position: relative;}
.search-fixed{position: fixed !important; z-index: 1000;width: 100%;}
.search-fixed .u-line{position: absolute;bottom: 0;left: 0;}
.select-list .u-checkbox{padding:30rpx;position: relative; 	}
.select-list .u-checkbox__label{padding-left:30rpx !important;}
.select-list .u-checkbox__label text{display: block;font-size: 24rpx;color:#C0C4CC;overflow-x:scroll;white-space:nowrap;width: 80vw;}
.select-list .u-checkbox .u-line{position: absolute;left:0;bottom:0rpx;}
.btn-foot-fixed{position: fixed;z-index: 1000;width:90vw;margin: 0 5vw;bottom:1vw;}
.content{min-height: 100vh;}
.user-top h4{font-size:40rpx;line-height:70rpx;font-weight: bold;}
.user-top text{display: block;font-size: 24rpx;line-height: 40rpx;font-weight: normal;}
.user-bar{background-image:linear-gradient(to right, #f1d9aa , #dbb570);border-radius: 20rpx;box-shadow: #e7c990 0px 0px 3px;color: #9d6820}
.user-bar h1{font-size: 30rpx;font-weight: bold;}
.user-bar text{font-size: 20rpx;}
.user-cell .u-cell{height:130rpx;}
.user-cell .u-cell_title{font-size:30rpx !important;color: #000 !important;padding-left: 120rpx !important;}
.user-cell .icon-bg{position: absolute;left: 30rpx;top:20rpx; width: 90rpx;height: 90rpx;border-radius:50%;background: #fe9e91;color: rgba(255,255,255,0.7);line-height: 90rpx;text-align: center;}
.checkList{
	line-height: 1.5;
}
.checkList .li{
	margin-bottom: 15rpx;
}
.checkList .li .flex-bd{
	margin-bottom: 10rpx;
}
.checkList .li:last-child{
	margin-bottom: 0;
}
.checkList .i{
	display: inline-block;
	margin-right: 30rpx;
}

.mr30{
	padding: 30rpx 0;
}
.bor-t,.bor-b,.bor-r{
	position: relative;
}
.bor-b:after{
	content: " ";
	position: absolute;
	left: 0;
	bottom: 0;
	right: 0;
	height: 1px;
	border-bottom: 1px solid rgba(0,0,0,0.1);
	color: rgba(0,0,0,0.1);
	-webkit-transform-origin: 0 100%;
	transform-origin: 0 100%;
	-webkit-transform: scaleY(0.5);
	transform: scaleY(0.5);
}
.border-top{
	padding-bottom: 1px;
}

.panD-list{
	
}
.panD-list .li{
	padding: 20rpx;
	background: #fff;
	margin: 20rpx;
}
.panD-list .li .cover{
	width: 100rpx;
	height: 100rpx;
	
}
.panD-list .li .img{
	margin-right: 20rpx;
	position: relative;
}
.panD-list .li .img .tag{
	font-size: 20rpx;
	position: absolute;
	bottom: 0;
	right: 0;
	color: #fff;
	padding: 0 2px;
}
.panD-list .li .img .tag0{
	background: #22c656;
}
.panD-list .li .img .tag1{
	background: #00a0f0;
}
.panD-list .li .img .tag2{
	background: #ff5959;
}
.panD-list .li .img .tag5{
	background: #acacac;
}
.panD-list .li .img .tag3{
	background: #ff7530;
}

.panD-list .li .name{
	font-size: 30rpx;
	margin-bottom: 10rpx;
	overflow: hidden;
	    text-overflow: ellipsis;
	    display: -webkit-box;
	    -webkit-box-orient: vertical;
	    -webkit-line-clamp: 2;
}
.panD-list  .li .no{
	font-size: 26rpx;
	color: #999;
}

.pd-info{
	
}
.pd-info .top{
	padding: 30rpx;
}
.pd-info .top .name{
	font-size: 32rpx;
	margin-bottom: 10rpx;
	font-weight: bold;
}
.pd-info .top .no{
	font-size: 26rpx;
	color: #999;
}
.pd-info .top .cover{
	width: 120rpx;
	height: 120rpx;
	margin-left: 20rpx;
}

.flex{
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	-webkit-box-align: center;
	-webkit-align-items: center;
	align-items: center;
}
.flext{
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
}
.flexb{
	display: -webkit-box;
	display: -webkit-flex;
	display: flex;
	align-items: flex-end;
	-webkit-box-align: flex-end;
	-webkit-align-items: flex-end;
}
.flex-bd{
	-webkit-box-flex: 1;
	-webkit-flex: 1;
	flex: 1;
	min-width: 0;
}
.pd030{
	padding: 0 30rpx;
}

.pannel .tit{
	padding-left: 20rpx;
	font-weight: bold;
	font-size: 30rpx;
	background: #fff;
	color: #35318f;
	border-left: 5px solid #35318f;
}
