/**
 * 金额输入处理混入
 * 功能：限制数字+1个小数点，最多2位小数，失焦自动格式化
 * 使用：在组件中引入并绑定 @input 和 @blur 事件
 */
export default {
  data() {
    return {
      // 存储每个字段的最后有效值（支持多字段）
      lastValidValues: {},
      // 可配置项（组件中可覆盖）
      amountConfig: {
        reg: {
          valid: /^(\d+)(\.\d{1,2})?$/, // 有效金额格式
          invalidChars: /[^\d.]/g       // 非法字符
        },
        tip: '请输入有效的数字（最多2位小数）' // 提示文字
      }
    };
  },
  methods: {
    /**
     * 输入实时验证
     * @param {String} value - 输入值
     * @param {String} field - 表单字段名（如 'amount'）
     */
    handleAmountInput(value, field) {
      // 初始化当前字段的最后有效值
      if (this.lastValidValues[field] === undefined) {
        this.lastValidValues[field] = '';
      }

      // 1. 空值处理
      if (value === '') {
        this.lastValidValues[field] = '';
        this.form[field] = '';
        return;
      }

      // 2. 非法字符检测
      if (this.amountConfig.reg.invalidChars.test(value)) {
        this.form[field] = this.lastValidValues[field];
        this.$uv.toast(this.amountConfig.tip);
		console.log(11111)
        return;
      }

      // 3. 多个小数点检测
      if (value.indexOf('.') !== value.lastIndexOf('.')) {
        this.form[field] = this.lastValidValues[field];
        this.$uv.toast(this.amountConfig.tip);
        return;
      }

      // 4. 小数位数超限处理（最多2位）
      const dotIndex = value.indexOf('.');
      if (dotIndex !== -1 && value.length - dotIndex > 3) {
        value = value.slice(0, dotIndex + 3); // 截断多余小数
      }

      // 5. 暂存合法值
      this.lastValidValues[field] = value;
      this.form[field] = value;
    },

    /**
     * 失焦格式化
     * @param {String} field - 表单字段名
     */
    handleAmountBlur(field) {
      let value = this.form[field];
      if (!value) return;

      // 1. 处理前导零（如 "0012" → "12"，"000" → "0"）
      if (value.startsWith('0') && value.length > 1 && !value.startsWith('0.')) {
        value = value.replace(/^0+/, '');
        value = value || '0'; // 全零情况处理
      }

      // 2. 补前导零/去末尾小数点（如 ".12" → "0.12"，"12." → "12"）
      if (value.startsWith('.')) {
        value = '0' + value;
      } else if (value.endsWith('.')) {
        value = value.slice(0, -1);
      }

      // 3. 最终有效性校验
      if (!this.amountConfig.reg.valid.test(value)) {
        this.form[field] = '';
        this.lastValidValues[field] = '';
        this.$uv.toast(this.amountConfig.tip);
        return;
      }

      // 4. 更新最终值
      this.form[field] = value;
      this.lastValidValues[field] = value;
    }
  }
};
