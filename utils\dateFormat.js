// utils/dateFormat.js
/**
 * 日期格式化函数
 * @param {string} rawDate - 原始日期字符串（如 "2025-08-04T00:00:00"）
 * @param {string} format - 目标格式（默认 "YYYY/MM/DD HH:MM"）
 * @returns {string} 格式化后的日期
 */
export const formatDate = (rawDate, format = "YYYY/MM/DD HH:MM") => {
  if (!rawDate) return ""; // 处理空值
  const date = new Date(rawDate); // 解析原始日期
  
  // 提取日期组件（注意：月份从 0 开始，需 +1）
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 补零（如 8 → "08"）
  const day = String(date.getDate()).padStart(2, "0");
  const hour = String(date.getHours()).padStart(2, "0");
  const minute = String(date.getMinutes()).padStart(2, "0");
  
  // 替换格式符
  return format
    .replace("YYYY", year)
    .replace("MM", month)
    .replace("DD", day)
    .replace("HH", hour)
    .replace("MM", minute); // 此处 "MM" 同时匹配月份和分钟，顺序不影响（因月份先替换）
};