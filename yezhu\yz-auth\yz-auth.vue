<template>
	<view class="content border-top">
		<view class="bg-w u-p-l-30 u-p-r-30">
			<u-form :model="form" ref="uForm" :errorType="errorType">
				<u-form-item label-width="140" label="手机号码">
					<u-input  v-model="form.CellPhone" type="tel" :value="form.CellPhone" disabled></u-input>
				</u-form-item>
				<u-form-item label-width="140" label="真实姓名" prop="UserName">
					<u-input  placeholder="请输入姓名" v-model="form.UserName" type="text" ></u-input>
				</u-form-item>			
				<u-form-item label-width="140" label="服务项目" >
					<u-input  disabled placeholder=" " :border-bottom="flase"  type="number" ></u-input>
					<u-button slot="right" type="primary" size="mini" @click="selectProject">选择项目</u-button>
				</u-form-item>
				<view class="u-p-t-20" v-if="selectedlist">
					<u-tag
						type="info"
						mode="plain"
						class="u-m-t-30 u-m-r-30"
						v-for="(item, index) in selectedlist"
						:key="index"
						:text="item.projectname"
						:show="item.onshow"
						@close="delProject(index)"
						closeable
					></u-tag>
				</view>
			</u-form>
		</view>
		<view class="u-m-80"><u-button type="primary" shape="circle" @click="submit">确认提交</u-button></view>
		<u-toast ref="uToast" />
	</view>
</template>

<script>
export default {
	data() {
		let that = this;
		return {
			form: {
				CellPhone: '',
				UserName: '',
				name: '',
				JobNumber: ''
			},
			rules: {
				UserName:[{
					required: true,
					message: '请输入姓名',
					trigger: 'blur' ,
				},
				{
					min: 2,
					max: 30,
					message: '姓名长度在2到30个字符',
					trigger: ['change','blur'],
				}]			
			},
			selectedlist: '',
			errorType: ['message']
		};
	},
	onLoad() {
		this.form.CellPhone = uni.getStorageSync('CellPhone');
	},
	onReady() {
		this.$refs.uForm.setRules(this.rules);
	},
	onShow() {
		uni.$on('handleFun', res => {
			this.selectedlist = res.selectedlist;		
			// 清除监听
			uni.$off('handleFun');
		});
	},
	methods: {
		selectProject() {
			uni.navigateTo({
				url: '/pages/selectProject/selectProject'
			});
		},
		delProject(index) {
			this.selectedlist.splice(this.selectedlist[index], 1)
		},		
		submit() {			
			this.$refs.uForm.validate(valid => {
				if (this.selectedlist.length == 0) {
					return this.$refs.uToast.show({icon: true,title: '请选择服务项目',type: 'warning'});					
				}
				if (valid) {
					var projectcodes=[]
					for(let index in this.selectedlist) { 
					    projectcodes.push(this.selectedlist[index].projectcode);
					}
					
					this.$http.post('/LoginMng/UserAuthentication', {
						CellPhone:this.form.CellPhone,
						UserName:this.form.UserName,
						JobNumber:'',
						ProjectCode:projectcodes.toString()
					}).then(res => {
						this.$u.toast(res.ErrMsg);
						if(res.ErrCode==100){
							setTimeout(function(){
								uni.navigateBack({
									delta: 1
								});
							},1000)
							uni.setStorageSync('IsCheck',1)
						}
					})
				} else {
					return this.$refs.uToast.show({							
						icon: true,
						title: '提交失败',
						type: 'warning'
					});
				}
			});
			
			
		}
	}
};
</script>

<style>
	.u-tag{
		margin-bottom: 20rpx;
	}
</style>
