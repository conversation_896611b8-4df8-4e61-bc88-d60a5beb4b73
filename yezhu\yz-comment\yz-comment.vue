<template>
	<view class="border-top">
		<view class="detail-top flex"><view class="flex-bd">工单号：{{no}}</view></view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between"><view class="u-flex-nowrap u-item-title u-font-28">评价内容</view></view>
			</view>
			<view slot="body">
				<u-form :model="form" :rules="rules" ref="uForm" :errorType="errorType">
					<u-form-item label="评价星级" label-width="140rpx" :border-bottom="flase" prop="EvaluateLevel">
						<u-rate :count="count" size="40" v-model="form.EvaluateLevel" active-color="#eca805" inactive-color="#eee"></u-rate>
					</u-form-item>
					<u-form-item label="评价内容" label-width="140rpx" :border-bottom="flase"  prop="EvaluateContent">
						<u-input v-model="form.EvaluateContent" type="textarea" :border="true" />
					</u-form-item>
					<u-form-item label="上传图片" label-width="140rpx" :border-bottom="flase">
						<u-upload ref="uUpload1" :action="action" :header="uploadHeader" @on-success="uploadOk" :file-list="fileList"></u-upload>
					</u-form-item>
				</u-form>
			</view>
		</u-card>
		<view class="mr30"><u-button type="primary" @click="submit">确认提交</u-button></view>
	</view>
</template>

<script>
const app = getApp();
export default {
	data() {
		return {
			uploadHeader:{
				Authorization: 'Bearer '+ uni.getStorageSync('access_token')
			},
			action:app.globalData.uploadUrl,
			count: 5,					
			errorType: ['message'],
			form:{
				UserCode:'',	
				EvaluateLevel:5,
				EvaluateContent:'',
				EvaluatePhotos:''
			},
			rules: {
			},
			fileList:[],
			photoList:[],
			code:'',
			type:'',
			redirectUrl:'',
			no:''
		}
	},
	onLoad(e) {
		console.log(e)
		this.redirectUrl=e.redirectUrl
		this.type=e.type
		this.no=e.no
		this.form.ProjectCode = uni.getStorageSync('projectCode')
		this.action=this.uploadUrl
		this.form.UserCode = uni.getStorageSync('UserCode');
		
		this.form.CellPhone = uni.getStorageSync('CellPhone')
		this.form.Photos=''
		if(e.type=='repair'){
			this.form.RepairCode=e.code
		}
		else if(e.type=='meeting'){
			this.form.MeetingCode=e.code
		}
		else if(e.type=='food'){
			this.form.OutCode=e.code
		}
		else if(e.type=='eat'){
			this.form.ReserveCode=e.code
		}
		else{
			this.form.BoxCode=e.code
		}
	},
	onReady() {
		this.$refs.uForm.setRules(this.rules);
	},
	methods: {
		submit(){
			var url
			var type=this.type
			if(type=='repair'){
				url='/RepairMngApi/Execute?Docmd=evaluate'
			}
			else if(type=='meeting'){
				url='/MeetingMngApi/Execute?Docmd=evaluate'
			}
			else if(type=='food'){
				url='/VegetablesTakeoutMngApi/Execute?Docmd=evaluate'
			}
			else if(type=='eat'){
				url='/FoodReservationMngApi/Execute?Docmd=evaluate'
			}
			else{
				url='/ReserveBoxMngApi/Execute?Docmd=evaluate'
			}
			
			var photoList1=[]
			var files1 = this.$refs.uUpload1.lists;
			for(let index in files1){
				photoList1.push(files1[index].response.RetValue)
			}			
			this.form.EvaluatePhotos=photoList1.toString();	
			
			if(!this.count){
				return this.$u.toast('请评分！');
			}
			this.form.EvaluateLevel=this.count
			this.$refs.uForm.validate(valid => {			
				if (valid) {
					console.log(this.form)
					this.$http.post(url,this.form).then(res => {
						this.$u.toast(res.ErrMsg);
						if(res.ErrCode==100){
							setTimeout(function(){
								uni.navigateBack({
									delta: 1
								})
								uni.$emit('update',{update:true})
							},1000)
						}
					})
				}
			})
		},
		uploadOk(data,index,lists){
			console.log(data)
			this.photoList.push(data.RetValue)
		}
	}
}
</script>

<style>
page {
	background: #f6f6f6;
}
</style>
