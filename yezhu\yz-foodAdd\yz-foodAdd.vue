<template>
	<view class="border-top">
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">预约信息</view>
				</view>
			</view>
			<view slot="body">
				<u-form :model="form" :rules="rules" ref="uForm" :errorType="errorType">
					<u-form-item label="申请人" label-width="150rpx" :border-bottom="flase">
						<u-input value="申请人" v-model="form.UserName" disabled />
					</u-form-item>
					<u-form-item label="联系电话" prop="CellPhone" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="form.CellPhone" :border="true"/>
					</u-form-item>
					<u-form-item label="取餐时间" prop="OutTime" label-width="150rpx" :border-bottom="flase">
						<u-input @click="shijianShow = true" v-model="form.OutTime" type="select" :border="true" />
					</u-form-item>
					<u-form-item label="备注内容" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="form.Remark" type="textarea" :border="true" />
					</u-form-item>	
				</u-form>
			</view>
		</u-card>
		<u-card padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">菜单信息</view>
					<view ><u-button type="primary" shape="circle" size="mini" @click="goChoose">选择菜品</u-button></view>
				</view>
			</view>
			<view slot="body">
				<view class="list">
					<view class="mr30" v-if="total==0">
						<u-empty text="暂无内容"></u-empty>
					</view>
					<view v-else class="i flex" v-for="(item,index) in selectedlist" :key="index">
						<view class="flex-hd">
							<image :src="item.DishImgs" mode="aspectFit"></image>
						</view>
						<view class="flex-bd">
							<view class="t">{{item.DishName}}</view>
							<view class="des" v-if="item.Remark">{{item.Remark}}</view>
						</view>
						<view class="flex-hd">
							<view class="num">
								<view class="inline">x</view>{{item.value}}
							</view>
						</view>
					</view>
				</view>
			</view>
			<view slot="foot">
				<view class="u-text-right total">总计<view class="inline">{{total}}</view>份菜品</view>
			</view>
		</u-card>
		<view class="mr30">
			<u-button type="primary" @click="submit">确认提交</u-button>
		</view>
		<u-picker mode="time" v-model="shijianShow" :params="params" @confirm="shijian"></u-picker>
	</view>
</template>

<script>
	import utils from '../../components/utils.js'
	export default {
		data() {
			return {
				total:0,
				shijianShow:'',
				params: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					second: false
				},
				form:{
					UserName:'',
					OutTime:'',
					Remark:'',
					DishCode:'',
					Number:'',
					CellPhone:''
				},
				errorType: ['message'],
				rules: {
					OutTime: [
						{
							required: true,
							message: '请选择取餐时间',
							trigger: ['change', 'blur']
						}
					],
					CellPhone:[
						{
							required: true, 
							message: '请输入手机号',
							trigger: ['change','blur'],
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// this.$u.test.mobile()就是返回true或者false的
								return this.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['change','blur'],
						}
					]
				},
				selectedlist:'',
				sfrom:''
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		onShow() {
			uni.$on('handleFun', res => {
				this.selectedlist = res.selectedlist;
				console.log(res.selectedlist)
				var total=0
				for(let index in res.selectedlist){
					console.log(res.selectedlist[index].value)
					total=total+res.selectedlist[index].value
				}
				this.total=total
				// 清除监听
				uni.$off('handleFun');
			});
		},
		onLoad(e) {
			this.form.CellPhone = uni.getStorageSync('CellPhone')
			this.form.UserName = uni.getStorageSync('UserName')
			if(e.ProjectCode){
				this.form.ProjectCode =e.ProjectCode
			}
			else{
				this.form.ProjectCode = uni.getStorageSync('projectCode')
			}
			this.form.ApplyUserCode = uni.getStorageSync('UserCode')
			if(e.sfrom=='change'){
				this.sfrom='change'
				this.form.OutCode=e.OutCode
			}
		},
		methods: {
			goChoose(){
				console.log('wwww')
				var list=this.selectedlist
				console.log(list)
				var slist=[]
				if(list){
					for(let index in list){
						slist.push({
							DishCode:list[index].DishCode,
							value:list[index].value
						})
					}
					uni.navigateTo({
						url:'../yz-foodSelect/yz-foodSelect?selectedlist='+JSON.stringify(slist)
					})
				}
				else{
					uni.navigateTo({
						url:'../yz-foodSelect/yz-foodSelect'
					})
				}
				
			},
			shijian(e){
				console.log(e)
				this.form.OutTime=e.year+'-'+e.month+'-'+e.day+' '+e.hour+':'+e.minute+':00'
			},
			submit(){ 
				this.$refs.uForm.validate(valid => {
					if (valid) {
						if(this.selectedlist.length==0){
							return this.$u.toast('请选择菜品');
						}
						if(!utils.checkTime(this.form.OutTime)){
							return this.$u.toast('选择时间应大于当前时间');
						}
						var DishCode=[]
						var Numbers=[]
						this.selectedlist.forEach((item, index) => {
							DishCode.push(item.DishCode)
							Numbers.push(item.value)
						});
						this.form.DishCode=DishCode.toString()
						this.form.Number=Numbers.toString()
						if(this.sfrom=='change'){
							var url='/VegetablesTakeoutMngApi/Execute?Docmd=change'
						}
						else{
							var url='/VegetablesTakeoutMngApi/Execute?Docmd=add'
						}
						
						this.$http.post(url, this.form).then(res => {
							this.$u.toast(res.ErrMsg);
							if(res.ErrCode==100||res.ErrCode==101){
								setTimeout(function(){
									uni.redirectTo({
										url:'../yz-orderList/yz-orderList?type=0'
									})
								},1000)
							}
						})
					}
				})
			}
		}
	}
</script>

<style>
	
	@import url("../yz-foodSelect/style.css");
	page{
		background: #f6f6f6;
	}
	.list{
		margin: 0;
		padding-top: 30rpx;
	}
	.list .i{
		padding:0 0 30rpx 0;
		margin-bottom: 30rpx;
	}
	.list .i:last-child{
		border-bottom: 0;
		margin-bottom: 0;
	}
	.num{
		color: #ff641f;
		font-size: 32rpx;
		margin-left: 20rpx;
	}
	.num .inline{
		margin-right: 10rpx;
		color: #666;
		font-size: 24rpx;
	}
	.total{
		color: #666;
	}
	.total .inline{
		font-size: 32rpx;
		color: #000;
		margin: 0 5px;
	}
</style>
