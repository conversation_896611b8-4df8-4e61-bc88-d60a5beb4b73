<template>
	<view class="border-top">
		<!-- 基础信息 -->
		<view class="detail-top flex">
			<view class="flex-hd"><image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_wm.png"></image></view>
			<view class="flex-bd">
				<view class="t">工单号:{{ info.OrderNo }}</view>
				<view class="desc">卤菜外卖</view>
			</view>
		</view>
		<view class="detail-finish">
			<view class="q">
				<view class="q-t">{{info.ApplyStatusName}}</view>
			</view>
		</view>
		<!-- 预约信息 -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">预约信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="申请人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{ info.ApplyUserName }}</text>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="申请时间" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{ info.ApplyTime }}</text>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="取餐时间" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{ info.OutTime }}</text>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{ info.Remark }}</text>
					</u-cell-item>
				</u-cell-group>
				<view class="detail-his-list" v-if="showAddHis">
					<view class="i" v-for="(item, index) in addHisList" :key="index">
						<view class="t">
							<view class="span">{{ item.ChangeTime }}</view>
						</view>
						<u-cell-group :border="flase">
							<u-cell-item class="cell-item-reset" title="申请人" :border-bottom="flase" :arrow="flase" title-width="150">
								<text>{{ item.ApplyUser.UserName }}</text>
							</u-cell-item>
							<u-cell-item class="cell-item-reset" title="申请时间" :border-bottom="flase" :arrow="flase" title-width="150">
								<text>{{ item.ApplyTime }}</text>
							</u-cell-item>
							<u-cell-item class="cell-item-reset" title="取餐时间" :border-bottom="flase" :arrow="flase" title-width="150">
								<text>{{ item.OutTime }}</text>
							</u-cell-item>
							<u-cell-item class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase" title-width="150">
								<text v-if="item.Remark">{{ item.Remark }}</text>
							</u-cell-item>
						</u-cell-group>
					</view>
				</view>
				<view class="detail-more-btn" v-if="addHisList" @click="showAddHis = !showAddHis">
					历史预约记录
					<i :class="'custom-icon ' + (showAddHis ? 'custom-icon-up-copy' : 'custom-icon-down')"></i>
				</view>
				<view class="detail-phone" @click="callPhone(info.CellPhone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		<!-- 菜品信息 -->
		<u-card padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between"><view class="u-flex-nowrap u-item-title u-font-28">菜单信息</view></view>
			</view>
			<view slot="body">
				<view class="list">
					<view class="i flex" v-for="(item, index) in FoodList" :key="index">
						<view class="flex-hd"><image :src="item.DishImgs"></image></view>
						<view class="flex-bd">
							<view class="t">{{ item.DishName }}</view>
							<view class="des" v-if="item.Remark">{{ item.Remark }}</view>
						</view>
						<view class="flex-hd">
							<view class="num">
								<view class="inline">x</view>
								{{ item.Number }}
							</view>
						</view>
					</view>
				</view>
			</view>
			<view slot="foot">
				<view class="u-text-right total">
					总计
					<view class="inline">{{ FoodTotal }}</view>
					份菜品
				</view>
			</view>
		</u-card>
		
		<!-- 审核信息 -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatusName!='待审核'&&info.CheckUserName">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">审核信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="审核时间" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{ info.CheckTime }}</text>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="联系人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{ info.CheckUserName}}</text>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="审核结果" :border-bottom="flase" :arrow="flase" title-width="150">
						<text v-if="info.ApplyStatus==1||info.ApplyStatus>2">审核通过</text>
						<text v-if="info.ApplyStatus==2">审核未通过</text>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase" title-width="150">
						<text v-if="info.CheckRemark">{{ info.CheckRemark }}</text>
					</u-cell-item>
				</u-cell-group>			
			</view>
			<view class="detail-his-list" v-if="showCheckHis">
				<view class="i" v-for="(item,index) in checkHisList" :key="index">
					<view class="t">
						<view class="span">{{item.ChangeTime}}</view>
					</view>
					<u-cell-group :border="flase">
						<u-cell-item class="cell-item-reset" title="审核时间" :border-bottom="flase" :arrow="flase" title-width="150">
							<text>{{item.CheckTime}}</text>
						</u-cell-item>
						<u-cell-item class="cell-item-reset" title="审核结果" :border-bottom="flase" :arrow="flase" title-width="150">
							<text v-if="item.ApplyStatus==1">审核通过</text>
							<text v-if="item.ApplyStatus==2">审核未通过</text>
						</u-cell-item>
						<u-cell-item  class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="150" >
							<text v-if="item.Remark==null"></text>
							<text v-else>{{item.Remark}}</text>
						</u-cell-item>	
					</u-cell-group>
				</view>
			</view>
			<view class="detail-more-btn" v-if="checkHisList.length>0" @click="showCheckHis=!showCheckHis">
				历史审核记录<i :class="'custom-icon '+(showCheckHis?'custom-icon-up-copy':'custom-icon-down')"></i>
			</view>
		</u-card>
		<view class="mr30" v-if="info.ApplyStatusName=='待审核'||info.ApplyStatusName=='审核通过'||info.ApplyStatusName=='审核不通过'">
			<u-row gutter="16">
				<u-col span="6">
					<u-button type="default" @click="cancle">取消预约</u-button>
				</u-col>
				<u-col span="6">
					<u-button type="primary" @click="change">变更预约</u-button>
				</u-col>				
			</u-row>
			
		</view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatusName=='已完成'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">评价信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="评价人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.ApplyUserName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价星级" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<u-rate :disabled="true" v-model="info.EvaluateLevel" :count="5" active-color="#eca805"></u-rate>
					</u-cell-item>
					<u-cell-item v-if="info.EvaluateContent" class="cell-item-reset" title="评价内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateContent}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.EvaluatePhotos" :key="index" :src="img" @click="lookImg(index,info.EvaluatePhotos)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		<view class="mr30" v-if="info.ApplyStatusName=='待评价'">
			<u-button type="primary" @click="gotoUrl">
				我要评价
			</u-button>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				hostUrl: app.globalData.hostUrl,
				OutCode: '',
				info: {
					OrderNo:''
				},
				FoodList: [],
				FoodTotal: '',
				addHisList: '',
				checkHisList: '',
				showCheckHis: '',
				showAddHis: '',
				list: [{
						name: '通过',
						disabled: false
					},
					{
						name: '不通过',
						disabled: false
					}
				],
				canPass: '',
				ProjectCode: ''
			}
		},
		methods: {
			lookImg(index,imgList){
				uni.previewImage({
					current:index,
					urls: imgList
				})
			},
			gotoUrl(e){
				uni.navigateTo({
					url:'../yz-comment/yz-comment?code='+this.OutCode+'&type=food&redirectUrl=/yezhu/yz-foodDetail/yz-foodDetail&no='+this.info.OrderNo
				})
			},
			renderAddList() {
				return new Promise((resolve)=>{
					this.$http.post('/VegetablesTakeoutMngApi/GetDetail?Docmd=history', {
						OutCode:this.OutCode
					}).then(res => {
						resolve(res.Data)
					})
				})
			},
			change(){
				uni.navigateTo({
					url:"../yz-foodAdd/yz-foodAdd?sfrom=change&OutCode="+this.OutCode
				})
			},
			cancle(){
				var that=this
				uni.showModal({
				    title: '提示',
				    content: '是否取消预约',
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							that.$http.post('/VegetablesTakeoutMngApi/Execute?Docmd=cancel', {
								OutCode:that.OutCode
							}).then(res => {
								that.$u.toast(res.ErrMsg);
								if(res.ErrCode==100){
									setTimeout(function(){
										that.load()
									},1000)
								}
							})
				        } 
				    }
				});
				
			},
			load(){
				this.$http.post('/VegetablesTakeoutMngApi/GetDetail?Docmd=main', {
					OutCode: this.OutCode
				}).then(res => {
					for (let index in res.Data.foodList) {
						if (res.Data.foodList[index].DishImgs) {
							res.Data.foodList[index].DishImgs = getApp().globalData.hostUrl + res.Data.foodList[index].DishImgs;
						}
					}
					res.Data.main.ApplyTime=res.Data.main.ApplyTime?res.Data.main.ApplyTime.replace('T',' '):''
					res.Data.main.OutTime=res.Data.main.OutTime?res.Data.main.OutTime.replace('T',' '):''
					res.Data.main.CheckTime=res.Data.main.CheckTime?res.Data.main.CheckTime.replace('T',' '):''
					res.Data.main.EvaluateTime=res.Data.main.EvaluateTime?res.Data.main.EvaluateTime.substring(0,19).replace('T',' '):''
					
					if(res.Data.main.EvaluatePhotos){
						res.Data.main.EvaluatePhotos=res.Data.main.EvaluatePhotos.split(',')
						for(let index in res.Data.main.EvaluatePhotos){
							res.Data.main.EvaluatePhotos[index]=this.hostUrl+res.Data.main.EvaluatePhotos[index]
						}
					}
					
					var num = 0;
					res.Data.foodList.forEach((item, index) => {
						num += item.Number;
					});
				
					this.FoodTotal = num;
					this.info = res.Data.main;
					this.FoodList = res.Data.foodList;
				});
				
				this.renderAddList().then(res => {
					if (res.length > 0) {
						var checkHisList = []
						for (let index in res) {
							res[index].ChangeTime = res[index].ChangeTime?res[index].ChangeTime.replace('T', ' '):''
							res[index].ApplyTime = res[index].ApplyTime?res[index].ApplyTime.replace('T', ' '):''
							res[index].OutTime = res[index].OutTime?res[index].OutTime.replace('T', ' '):''
							if (res[index].ApplyStatus != 0) {
								checkHisList.push(res[index])
							}
						}
						this.addHisList = res
						this.checkHisList = checkHisList
					}
				})
			}
		},
		onLoad(e) {
			console.log(e)
			var that=this
			var	type = e.type
			var	code = e.code
			this.type = type,
			this.ProjectCode = e.ProjectCode,
			this.OutCode = code,
			this.load()
		},
		onShow() {
			var that=this
			uni.$on('update',function(res){
				if(res.update){
					that.load()
				}
			})
		}
	}
</script>

<style>
	@import url("../yz-foodSelect/style.css");
	page{
		background: #f6f6f6;
	}
	.list{
		margin: 0;
		padding-top: 30rpx;
	}
	.list .i{
		padding:0 0 30rpx 0;
		margin-bottom: 30rpx;
		border-bottom: 1px solid #eee;
	}
	.list .i:last-child{
		border-bottom: 0;
		margin-bottom: 0;
	}
	.num{
		color: #ff641f;
		font-size: 32rpx;
	}
	.num .inline{
		margin-right: 10rpx;
		color: #666;
		font-size: 24rpx;
	}
	.total{
		color: #666;
	}
	.total .inline{
		font-size: 32rpx;
		color: #000;
		margin: 0 5px;
	}
	.detail-his-list .i{
		padding: 0;
		border:0 ;
	}
</style>
