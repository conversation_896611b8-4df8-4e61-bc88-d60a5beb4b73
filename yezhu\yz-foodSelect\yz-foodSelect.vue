<template>
	<view class="border-top">
		<view class="list">
			<view class="i flex" v-for="(item,index) in list" :key="index">
				<view class="flex-hd">
					<image :src="item.DishImgs" mode="aspectFit"></image>
				</view>
				<view class="flex-bd">
					<view class="t">{{item.DishName}}</view>
					<view class="des" style="margin: 10rpx 0;">库存：{{item.Stock}}</view>
					<view class="des" style="margin: 10rpx 0;" v-if="item.Unit">单位：{{item.Unit}}</view>
					<view class="des" v-if="item.Remark">{{item.Remark}}</view>
				</view>
				<view class="flex-hd">
					<u-number-box :disabled="item.Stock==0" @change="valChange" v-model="item.value" input-width=60 input-height=60></u-number-box>
				</view>
			</view>
		</view>
		<view class="fix-btn">
			<view class="submit" @click="submit">确认选择</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				list:'',
				hostUrl:app.globalData.hostUrl
			}
		},
		onLoad(e) {
			console.log(e)
			
			this.$http.post('/VegetablesTakeoutMngApi/GetAllList',{
				ProjectCode:uni.getStorageSync('projectCode')
			}).then(res => {
				console.log(res);
				for(let index in res.Data){
					res.Data[index].value=0
					if(res.Data[index].DishImgs){
						res.Data[index].DishImgs=this.hostUrl+res.Data[index].DishImgs
					}
					else{
						res.Data[index].DishImgs='http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/fd1.jpg'
					}
				}
				
				var res=res.Data
				if(e.selectedlist){
					var selectedlist=JSON.parse(e.selectedlist)
					var res = res.filter(function (x) {
						for(var i=0;i<selectedlist.length;i++){
							if(selectedlist[i].DishCode==x.DishCode){
								x.value=selectedlist[i].value
							}
						}
					    return x
					});
				}
				this.list=res
			})
		},
		methods: {
			valChange(e){
				console.log(e)
			},
			submit(){
				var selectedlist = [];
				var total=0
				this.list.forEach((item, index) => {
					if (item.value>0) {
						selectedlist.push(item);
					}
					total+=item.value
				});
				if(total==0){
					return this.$u.toast('请选择菜品');
				}
				uni.$emit("handleFun",{selectedlist: selectedlist});
				uni.navigateBack();
			}
		}
		
	}
</script>

<style>
@import url("style.css");
</style>
