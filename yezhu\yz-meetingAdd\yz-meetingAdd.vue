<template>
	<view class="border-top">
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">预约信息</view>
				</view>
			</view>
			<view slot="body">
				<u-form :model="form" :rules="rules" ref="uForm" :errorType="errorType">
					<u-form-item label="申请人" label-width="160" :border-bottom="flase">
						<u-input value="申请人" v-model="form.UserName" disabled />
					</u-form-item>
					<u-form-item label="联系电话" prop="CellPhone" label-width="160" :border-bottom="flase">
						<u-input v-model="form.CellPhone" :border="true"/>
					</u-form-item>
					<u-form-item label="开始时间" prop="ApplyFromTime" label-width="160" :border-bottom="flase">
						<u-input @click="startShow = true" v-model="form.ApplyFromTime" type="select" :border="true" />
					</u-form-item>
					<u-form-item label="结束时间" prop="ApplyEndTime" label-width="160" :border-bottom="flase">
						<u-input @click="endShow = true" v-model="form.ApplyEndTime" type="select" :border="true" />
					</u-form-item>
					<u-form-item label="申请会议室" prop="ApplyRoomName" label-width="160" :border-bottom="flase">
						<u-input @click="showRooms" v-model="form.ApplyRoomName"  type="select" :border="true" />
					</u-form-item>
					
					<u-form-item label="会议名称" prop="MeetingName" label-width="160" :border-bottom="flase">
						<u-input v-model="form.MeetingName" :border="true" />
					</u-form-item>
					<u-form-item label="会议主持" prop="MeetingHost" label-width="160" :border-bottom="flase">
						<u-input v-model="form.MeetingHost" :border="true" />
					</u-form-item>
					<u-form-item label="主席台名单" prop="RostrumList" label-width="160" :border-bottom="flase">
						<u-input v-model="form.RostrumList" :border="true" />
					</u-form-item>
					
					<u-form-item label="参会人数" prop="PersonNumber" label-width="160" :border-bottom="flase">
						<u-input v-model="form.PersonNumber" :border="true" type="number" />
					</u-form-item>
					<u-form-item label="服务需求" label-width="160" :border-bottom="flase">
						<u-input disabled placeholder=" " />
						<u-button slot="right" type="primary" size="mini"  @click="meetingService">选择服务需求</u-button>
					</u-form-item>
					<view class="u-p-t-20" v-if="selectedlist">
						<u-tag
							type="info"
							class="u-m-b-30 u-m-r-30"
							mode="plain"
							v-for="(item, index) in selectedlist"
							:key="index"
							:text="item.name"
							:show="item.onshow"
							@close="del(index)"
							closeable
						></u-tag>
					</view>
					<u-form-item label="备注内容" label-width="160" :border-bottom="flase">
						<u-input v-model="form.Remark" :border="true" />
					</u-form-item>	
				</u-form>
			</view>
		</u-card>
		<view class="mr30">
			<u-button type="primary" @click="submit">确认提交</u-button>
		</view>
		<u-picker mode="time" v-model="startShow" :params="params" @confirm="startDate"></u-picker>
		<u-picker mode="time" v-model="endShow" :params="params" @confirm="endDate">></u-picker>
		<u-picker mode="selector" range-key="label" v-model="shiShow"  :default-selector="[0]" :range="roomList" @confirm="shi"></u-picker>
	</view>
</template>

<script>
	import utils from '../../components/utils.js'
	export default {
		data() {
			return {
				shiShow:'',
				endShow:'',
				startShow:'',
				params: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: false,
					second: false,
					half:true
				},
				roomList:[],
				selectedlist:'',
				form:{
					UserName:'',
					ApplyFromTime:'',
					ApplyEndTime:'',
					ApplyRoomCode:'',
					ApplyRoomName:'',
					MeetingName:'',
					MeetingHost:'',
					RostrumList:'',
					Demand:'',
					PersonNumber:'',
					Remark:'',
					CellPhone:''
				},
				errorType: ['message'],
				rules: {
					ApplyFromTime: [
						{
							required: true,
							message: '请选择开始时间',
							trigger: ['change', 'blur']
						}
					],
					ApplyEndTime: [
						{
							required: true,
							message: '请选择结束时间',
							trigger: ['change', 'blur']
						}
					],
					ApplyRoomName: [
						{
							required: true,
							message: '请选择会议室',
							trigger: ['change', 'blur']
						}
					],
					MeetingName: [
						{
							required: true,
							message: '请输入会议名称',
							trigger: ['change', 'blur']
						}
					],
					MeetingHost: [
						{
							required: true,
							message: '请输入会议主持',
							trigger: ['change', 'blur']
						}
					],
					RostrumList: [
						{
							required: true,
							message: '请输入主席台名单',
							trigger: ['change', 'blur']
						}
					],
					PersonNumber: [
						{
							required: true,
							message: '请输入参会人数',
							trigger: ['change', 'blur']
						}
					],
					CellPhone:[
						{
							required: true, 
							message: '请输入手机号',
							trigger: ['change','blur'],
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// this.$u.test.mobile()就是返回true或者false的
								return this.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['change','blur'],
						}
					]
				},
				startDateArray:'',
				endDateArray:''
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		onShow() {
			uni.$on('handleFun', res => {
				this.selectedlist = res.selectedlist;
				
				// 清除监听
				uni.$off('handleFun');
			});
		},
		onLoad(e) {
			this.form.UserName = uni.getStorageSync('UserName')
			if(e.ProjectCode){
				this.form.ProjectCode =e.ProjectCode
			}
			else{
				this.form.ProjectCode = uni.getStorageSync('projectCode')
			}
			this.form.ApplyUserCode = uni.getStorageSync('UserCode')
			this.form.CellPhone = uni.getStorageSync('CellPhone')
			
			if(e.sfrom=='change'){
				this.sfrom='change'
				this.form.MeetingCode=e.MeetingCode
			}
		},
		methods: {
			showRooms(){
				if(!this.form.ApplyFromTime){
					return this.$u.toast('请选择开始时间！'); 
				}
				if(!this.form.ApplyEndTime){
					return this.$u.toast('请选择结束时间！'); 
				}
				
				this.$http.get('/MeetingMngApi/GetAllList', {
					params:{
						ProjectCode:uni.getStorageSync('projectCode'),
						p:1,
						ps:50,
						startDate:this.form.ApplyFromTime,
						endDate:this.form.ApplyEndTime
					}
				}).then(res => {
					var list=[]
					for(let index in res.Data){
						var item={
							label:res.Data[index].RoomName,
							value:res.Data[index].RoomCode
						}
						list.push(item)
					}
					this.roomList=list
					this.shiShow = true
				})
				
				
			},
			del(index) {
				this.selectedlist.splice(this.selectedlist[index], 1)
			},	
			startDate(e){
				console.log(e)
				this.startDateArray=e.year+'-'+e.month+'-'+e.day
				this.form.ApplyFromTime=e.year+'-'+e.month+'-'+e.day+' '+e.hour+':'+e.half+':00'
			},
			endDate(e){
				console.log(e)
				this.endDateArray=e.year+'-'+e.month+'-'+e.day
				this.form.ApplyEndTime=e.year+'-'+e.month+'-'+e.day+' '+e.hour+':'+e.half+':00'
			},
			shi(e){
				console.log(e)
				this.form.ApplyRoomName=this.roomList[e[0]].label
				this.form.ApplyRoomCode=this.roomList[e[0]].value
			},
			submit(){
				this.$refs.uForm.validate(valid => {
					if (valid) {
						if(this.startDateArray!=this.endDateArray){
							return this.$u.toast('会议时间不能跨天！');
						}
						if(!utils.checkTime(this.form.ApplyFromTime)){
							return this.$u.toast('开始时间应大于当前时间');
						}
						if(!utils.checkTime(this.form.ApplyEndTime)){
							return this.$u.toast('结束时间应大于当前时间');
						}
						if(!utils.checkTwoTime(this.form.ApplyFromTime,this.form.ApplyEndTime)){
							return this.$u.toast('结束时间应大于开始时间');
						}
						var projectcodes=[]
						for(let index in this.selectedlist) { 
						    projectcodes.push(this.selectedlist[index].code);
						}
						this.form.Demand=projectcodes.toString()
						
						if(this.sfrom=='change'){
							var url='/MeetingMngApi/Execute?Docmd=change'
						}
						else{
							var url='/MeetingMngApi/Execute?Docmd=add'
						}
						
						this.$http.post(url, this.form).then(res => {
							this.$u.toast(res.ErrMsg);
							if(res.ErrCode==100){
								setTimeout(function(){
									uni.redirectTo({
										url:'../yz-orderList/yz-orderList?type=0'
									})
								},1000)
							}
						})
					}
				})
			},
			meetingService(){
				uni.navigateTo({
					url:'../yz-meetingService/yz-meetingService'
				})
			}
		}
	}
</script>

<style>
	.u-tag{
		margin-bottom: 20rpx;
	}
</style>
