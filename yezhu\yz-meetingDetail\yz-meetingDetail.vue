<template>
	<view class="border-top">
		<view class="detail-top flex">
			<view class="flex-hd">
				<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_hy.png"></image>
			</view>
			<view class="flex-bd">
				<view class="t">工单号：{{info.OrderNo}}</view>
				<view class="desc">会议预约</view>
			</view>
		</view>
		<view class="detail-finish">
			<view class="q">
				<view class="q-t">{{info.ApplyStatusText}}</view>
			</view>
		</view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">预约信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="申请人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.ApplyUserName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="申请时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ApplyTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="预约时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.timeRange}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="会议名称" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.MeetingName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="申请会议室" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ApplyRoomName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="参会人数" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.PersonNumber}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="会议主持" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.MeetingHost}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="主席台名单" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.RostrumList}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="服务要求" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.DemandText">{{info.DemandText}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.Remark">{{info.Remark}}</text>
					</u-cell-item>
				</u-cell-group>
				<view class="detail-his-list" v-if="showAddHis">
					<view class="i" v-for="(item,index) in addHisList" :key="index">
						<view class="t">
							<view class="span">{{item.ChangeTime}}</view>
						</view>
						<u-cell-group :border="flase">
							<u-cell-item  class="cell-item-reset" title="会议时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.timeRange}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="参会人数" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.PersonNumber}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text v-if="item.Remark">{{item.Remark}}</text>
							</u-cell-item>
						</u-cell-group>
					</view>
				</view>
				<view class="detail-phone" @click="callPhone(info.CellPhone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
				<view class="detail-more-btn" v-if="addHisList" @click="showAddHis=!showAddHis">
					历史预约记录<i :class="'custom-icon '+(showAddHis?'custom-icon-up-copy':'custom-icon-down')"></i>
				</view>
			</view>
		</u-card>
		
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatusText!='待审核'&&info.CheckUserName">>
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">审核信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item  class="cell-item-reset" title="审核时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.CheckTime}}</text>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="联系人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.CheckUserName}}</text>
					</u-cell-item>
					<u-cell-item class="cell-item-reset" title="审核结果" :border-bottom="flase" :arrow="flase" title-width="150">
						<text v-if="info.ApplyStatus!=2">审核通过</text>
						<text v-else>审核不通过</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.CheckRemark">{{info.CheckRemark}}</text>
					</u-cell-item>
					
				</u-cell-group>
				<view class="detail-his-list" v-if="showCheckHis">
					<view class="i" v-for="(item,index) in checkHisList" :key="index">
						<u-cell-group :border="flase">
							<u-cell-item  class="cell-item-reset" title="审核时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{info.CheckTime}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="联系人" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{info.CheckUserName}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="审核结果" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text v-if="info.ApplyStatus==1||info.ApplyStatus>2">审核通过</text>
								<text v-if="info.ApplyStatus==2">审核未通过</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text v-if="info.CheckRemark==null"></text>
								<text v-else>{{info.CheckRemark}}</text>
							</u-cell-item>
						</u-cell-group>
					</view>
				</view>
				<view class="detail-more-btn" v-if="checkHisList.length>0" @click="showCheckHis=!showCheckHis">
					历史审核记录<i :class="'custom-icon '+(showCheckHis?'custom-icon-up-copy':'custom-icon-down')"></i>
				</view>
				<view class="detail-phone" @click="callPhone(info.CheckUserCellPhone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
	
		
		
		<!-- 会议安排 -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatusText!='待审核'&&info.ApplyStatusText!='取消预约'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">会议安排</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="会议室" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.RoomName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="会议时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.timeRange}}</text>
					</u-cell-item>			
					<u-cell-item v-if="info.SecondCheckUserName" class="cell-item-reset" title="服务负责人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.SecondCheckUserName}}</text>
					</u-cell-item>
					<u-cell-item v-else class="cell-item-reset" title="服务人员" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.UserList.length}}</text>
					</u-cell-item>
				</u-cell-group>
				<view class="work">
					 <scroll-view scroll-x="true">
						<view class="i" v-for="(item,index) in info.UserList" :key="index">
							<u-avatar :show-level="item.IsLeader==1" level-icon="star-fill" :src="item.HeadImg?'item.HeadImg':'http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png'"></u-avatar>
							<view class="w-no" v-if="item.JobNumber">{{item.JobNumber}}</view>
							<view class="w-name" v-if="item.UserName">{{item.UserName}}</view>
						</view>						
					</scroll-view>					
				</view>
			</view>
		</u-card>	
		<view class="mr30" v-if="info.ApplyStatusText=='待审核'||info.ApplyStatusText=='审核通过'||info.ApplyStatusText=='审核不通过'">
			<u-row gutter="16">
				<u-col span="6">
					<u-button type="default" @click="cancle">取消预约</u-button>
				</u-col>
				<u-col span="6">
					<u-button type="primary" @click="change">变更预约</u-button>				
				</u-col>
			</u-row>			
		</view>
			
		<!-- <u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatusText=='待评价'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">会议信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="开始时间" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.FactFromTimeText}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="结束时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.FactEndTimeText}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.InspectRemark}}</text>
					</u-cell-item>
					<u-form-item label="检查单" label-width="150rpx" :border-bottom="flase">
						<view class="detail-img-list">
							<image v-for="(img,index) in checkPhotos" :key="index" :src="img" @click="lookImg(index,checkPhotos)"></image>
						</view>
					</u-form-item>
					
				</u-cell-group>
			</view>
		</u-card> -->
		<view class="mr30" v-if="info.ApplyStatusText=='待评价'">
			<u-button type="primary" @click="gotoUrl">
				我要评价
			</u-button>
		</view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.ApplyStatusText=='已完成'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">评价信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="评价人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.ApplyUserName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价星级" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<u-rate :disabled="true" v-model="info.EvaluateLevel" :count="5" active-color="#eca805"></u-rate>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateContent}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.EvaluatePhotos" :key="index" :src="img" @click="lookImg(index,info.EvaluatePhotos)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				ProjectCode:'',
				MeetingCode:'',
				info:{
					OrderNo:''
				},
				hostUrl:app.globalData.hostUrl,
				showAddHis:'',
				addHisList:'',
				checkHisList:'',
				showCheckHis:'',
				checkPhotos:[]
			}
		},
		methods: {
			lookImg(index,imgList){
				uni.previewImage({
					current:index,
					urls: imgList
				})
			},
			callPhone(e){
				uni.showModal({
				    title: '是否拨打电话？',
				    content: e,
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber:e
							})
				        } 
				    }
				});
			},
			renderAddList(){
				return new Promise((resolve)=>{
					this.$http.get('/MeetingMngApi/GetDetail?Docmd=history', {
						params:{
							MeetingCode:this.MeetingCode
						}
					}).then(res => {
						resolve(res.Data)
					})
				})		
			},
			change(){
				uni.navigateTo({
					url:"../yz-meetingAdd/yz-meetingAdd?sfrom=change&MeetingCode="+this.MeetingCode
				})
			},
			cancle(){
				var that=this
				uni.showModal({
				    title: '提示',
				    content: '是否取消预约',
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							that.$http.post('/MeetingMngApi/Execute?Docmd=cancel', {
								MeetingCode:that.MeetingCode
							}).then(res => {
								that.$u.toast(res.ErrMsg);
								if(res.ErrCode==100){
									that.load()
								}
							})
				        } 
				    }
				});
				
			},
			load(){
				this.$http.get('/MeetingMngApi/GetDetail?Docmd=main', {
					params:{
						MeetingCode:this.MeetingCode
					}
				}).then(res => {
					var info=res.Data
					//格式化时间
					info.ApplyTime=info.ApplyTime?info.ApplyTime.replace('T',' '):''
					info.DispatchTime=info.DispatchTime?info.DispatchTime.replace('T',' '):''
					info.AcceptTime=info.AcceptTime?info.AcceptTime.replace('T',' '):''
					info.CompleteTime=info.CompleteTime?info.CompleteTime.replace('T',' '):''
					info.EvaluateTime=info.EvaluateTime?info.EvaluateTime.substring(0,19).replace('T',' '):''
					info.CheckTime=info.CheckTime?info.CheckTime.replace('T',' '):''
					//格式化会议时间
					var ApplyFromTime=info.ApplyFromTime.split('T')
					var ApplyEndTime=info.ApplyEndTime.split('T')
					info.timeRange=ApplyFromTime[0]+' '+ApplyFromTime[1]+'至'+ApplyEndTime[1]
					
					if(res.Data.EvaluatePhotos){
						res.Data.EvaluatePhotos=res.Data.EvaluatePhotos.split(',')
						for(let index in res.Data.EvaluatePhotos){
							res.Data.EvaluatePhotos[index]=this.hostUrl+res.Data.EvaluatePhotos[index]
						}
					}
					
					var checkPhotos=[]
					if(res.Data.InspectBefore){
						res.Data.InspectBefore=res.Data.InspectBefore.split(',')
						for(let index in res.Data.InspectBefore){
							checkPhotos.push(this.hostUrl+res.Data.InspectBefore[index])
						}
					}
					if(res.Data.InspectAfter){
						res.Data.InspectAfter=res.Data.InspectAfter.split(',')
						for(let index in res.Data.InspectAfter){
							checkPhotos.push(this.hostUrl+res.Data.InspectAfter[index])
						}
					}
					
					
					this.checkPhotos=checkPhotos
					
					this.info=info
					
					this.renderAddList().then(res=>{
						if(res.length>0){
							var checkHisList=[]
							for(let index in res){
								res[index].ChangeTime=res[index].ChangeTime.replace('T',' ')
								res[index].CheckTime=res[index].CheckTime.replace('T',' ')
								res[index].ApplyTime=res[index].ApplyTime.replace('T',' ')
								
								//格式化会议时间
								var ApplyFromTime=res[index].ApplyFromTime.split('T')
								var ApplyEndTime=res[index].ApplyEndTime.split('T')
								var timeRange=ApplyFromTime[0]+' '+ApplyFromTime[1]+'至'+ApplyEndTime[1]							
								res[index].timeRange=timeRange
								
								if(res[index].ApplyStatus!=0){
									checkHisList.push(res[index])
								}
							}
							this.addHisList=res
							this.checkHisList=checkHisList
						}
					})
				
				})
			},
			gotoUrl(e){
				uni.navigateTo({
					url:'../yz-comment/yz-comment?code='+this.MeetingCode+'&type=meeting&redirectUrl=/yezhu/yz-meetingDetail/yz-meetingDetail&no='+this.info.OrderNo
				})
			},
			
		},
		onLoad(e) {
			//详情
			console.log(e)
			var MeetingCode=e.code
			this.MeetingCode=MeetingCode
			this.ProjectCode=e.ProjectCode
			this.load()
		},
		onShow() {
			var that=this
			uni.$on('update',function(res){
				if(res.update){
					that.load()
				}
			})
		}
	}
</script>

<style>
	page{
		background: #f6f6f6;
	}
</style>
