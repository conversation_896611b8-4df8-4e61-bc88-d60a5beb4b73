<template>
	<view class="border-top">
		<view class="list">
			<u-checkbox-group v-model="value"  @change="checkboxGroupChange" active-color="#27246c" :wrap="true" width="100%">
				<u-checkbox shape="circle" @change="checkboxChange" v-model="item.checked" v-for="(item, index) in list" :key="index" :name="item.Name">
					<view style="width: 650rpx">{{item.Name}}</view>
				</u-checkbox>
			</u-checkbox-group>
		</view>
		<view class="fix-btn">
			<view class="submit" @click="submit">确认选择</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				// u-radio-group的v-model绑定的值如果设置为某个radio的name，就会被默认选中
				value: 'orange',
			}
		},
		methods: {
			// 选中某个复选框时，由checkbox时触发
			checkboxChange(e) {
				//console.log(e);
			},
			// 选中任一checkbox时，由checkbox-group触发
			checkboxGroupChange(e) {
				// console.log(e);
			},
			submit() {
				var selectedlist = [];
				this.list.forEach((item, index) => {
					if (item.checked == true) {
						selectedlist.unshift({"code":item.Code,"name":item.Name,"onshow":true});
					}
				});
				uni.$emit("handleFun",{selectedlist: selectedlist});
				uni.navigateBack();
			}
		},
		onLoad() {
			this.$http.get('/MeetingMngApi/GetDemandInfo',{
				params:{}
			}).then(res => {
				for(let index in res.Data){
					res.Data[index].checked=false
					res.Data[index].disabled=false
				}
				this.list=res.Data
			})
		}
	}
</script>

<style>
	@import url("style.css");
</style>
