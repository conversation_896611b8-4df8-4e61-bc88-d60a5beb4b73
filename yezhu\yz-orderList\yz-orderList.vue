<template>
	<view class="swiper-wrap border-top">
		<view class="pd">
			<u-search placeholder="请输入单号" :show-action="true" v-model="keyword" shape="square" @custom="search"></u-search>
		</view>
		<u-tabs-swiper ref="uTabs" active-color="#35318f" :list="list" :current="current" @change="tabsChange" :is-scroll="false" swiperWidth="750"></u-tabs-swiper>
		<view class="swiper-box">
			<scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="onreachBottom">
				<view class="count">共{{orderCount}}条预约记录</view>
				<view class="list">
					<block v-for="(item,index) in orderList" :key="index">
						<view class="i flex border-all" v-if="item.Type=='卤菜外卖'" @click="gotoUrl('/yezhu/yz-foodDetail/yz-foodDetail?code='+item.Code)">
							<view class="flex-hd">
								<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_wm.png"></image>
								<view>卤菜外卖</view>
							</view>
							<view class="flex-bd">
								<view class="t">单号：{{item.OrderNo}}</view>
								<view class="info">取餐时间：{{item.BeginTime}}</view>
								<view class="info">申请时间：{{item.ApplyTime}}</view>
							</view>
							<view class="flex__ft"></view>
						</view>
						<view class="i flex border-all" v-else-if="item.Type=='餐食预留'" @click="gotoUrl('/yezhu/yz-eatDetail/yz-eatDetail?code='+item.Code)">
							<view class="flex-hd">
								<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_cs.png"></image>
								<view>餐食预留</view>
							</view>
							<view class="flex-bd">
								<view class="t">单号：{{item.OrderNo}}</view>
								<view class="info">预留时间：{{item.BeginTime}}</view>
								<view class="info">申请时间：{{item.ApplyTime}}</view>
							</view>
							<view class="flex__ft"></view>
						</view>
						<view class="i flex border-all" v-else-if="item.Type=='会议预约'" @click="gotoUrl('/yezhu/yz-meetingDetail/yz-meetingDetail?code='+item.Code)">
							<view class="flex-hd">
								<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_hy.png"></image>
								<view>会议预约</view>
							</view>
							<view class="flex-bd">
								<view class="t">单号：{{item.OrderNo}}</view>
								<view class="info">会议时间：{{item.BeginTime}}</view>
								<view class="info">申请时间：{{item.ApplyTime}}</view>
							</view>
							<view class="flex__ft"></view>
						</view>
						<view class="i flex border-all" v-else @click="gotoUrl('/yezhu/yz-roomDetail/yz-roomDetail?code='+item.Code)">
							<view class="flex-hd">
								<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_bx.png"></image>
								<view>包厢预定</view>
							</view>
							<view class="flex-bd">
								<view class="t">单号：{{item.OrderNo}}</view>
								<view class="info">预留时间：{{item.BeginTime}}</view>
								<view class="info">申请时间：{{item.ApplyTime}}</view>
							</view>
							<view class="flex__ft"></view>
						</view>
					</block>
					<view class="mr30" v-if="!empty">
						<u-loadmore :status="loadmore.status" :icon-type="loadmore.iconType" :load-text="loadmore.loadText" />
					</view>
					<u-empty class="u-text-center" v-if="empty" text="暂无内容" margin-top="300"></u-empty>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [{
					name: '预约成功',
					type:1
				}, {
					name: '预约失败',
					type:2
				}, {
					name: '待审核',
					type:0
				}, {
					name: '已完成',
					type:3
				}],
				current: 0,
				keyword:'',
				orderList:'',
				empty:false,
				page:1,
				loadmore:{
					status: 'loading',
					iconType: 'flower',
					loadText: {
						loading: '努力加载中',
						nomore: '已无更多'
					}
				},
				orderCount:0,
				type:1,
				projectCode:''
			}
		},
		onLoad(e) {
			if(e.type){
				var type=0
				this.type=0
				this.current=2
			}
			else{
				var type=1
			}
			if(e.projectCode){
				var projectCode=e.projectCode
			}
			else{
				var projectCode=uni.getStorageSync('projectCode')
			}
			this.projectCode=projectCode
			this.renderList(1,type).then(res=>{
				this.orderList=res
				if(res.length<10){
					this.loadmore.status='nomore'
				}
			})
			this.getNum()
			
			this.$http.get('/ProjectMngApi/GetMyApplyCount', {
				params:{
					UserCode:uni.getStorageSync('UserCode'),
					ProjectCode:projectCode,
					orderNo:'',
					applyStatusList:'1,2,0,3'
				}
			}).then(res => {
				var data =res.Data
				var list = [{
					name: '预约成功',
					type:1,
					count:data.Count1
				}, {
					name: '预约失败',
					type:2,
					count:data.Count2
				}, {
					name: '待审核',
					type:0,
					count:data.Count3
				}, {
					name: '已完成',
					type:3,
					count:data.Count4
				}]
				this.list = list
			})
		},
		methods: {
			getNum(){
				this.$http.get('/UserApi/GetVariousNum', {
					params:{
						UserCode:uni.getStorageSync('UserCode'),
						ProjectCode:this.ProjectCode,
						ApplyStatus:this.type,
						OrderNo:this.keyword,
						projectCode:this.projectCode
					}
				}).then(res => {
					this.orderCount=res.Data.applyNum
				})
			},
			gotoUrl(e){
				uni.navigateTo({
					url:e
				})
			},
			search(value) {
				this.renderList(1,this.type,value).then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.page=1
				this.getNum()
			},
			tabsChange(item) {
				if(this.current == item.index){
					return
				}
				this.current = item.index;
				this.type=item.item.type
				this.loadmore.status='loading'
				this.renderList(1,item.item.type).then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.page=1
				this.getNum()
			},
			renderList(page,type=1){
				return new Promise((resolve)=>{
					this.$http.get('/MeetingMngApi/GetList', {
						params:{
							p:page,
							ps:10,
							UserCode:uni.getStorageSync('UserCode'),
							projectCode:this.projectCode,
							ApplyStatus:type,
							OrderNo:this.keyword
						}
					}).then(res => {
						if(page==1&res.Data.length==0){
							this.empty=true
						}
						else{
							this.empty=false
							for(let index in res.Data){
								res.Data[index].ApplyTime=res.Data[index].ApplyTime.replace(/\//g,'-')
								res.Data[index].ApplyTime=res.Data[index].ApplyTime.replace('T',' ')
								res.Data[index].ReserveTime=res.Data[index].ReserveTime?res.Data[index].ReserveTime.replace('T',' '):''
								res.Data[index].fromtime=res.Data[index].fromtime?res.Data[index].fromtime.replace('T',' '):''
								res.Data[index].outtime=res.Data[index].outtime?res.Data[index].outtime.replace('T',' '):''
							}
						}
						var list=[]
						resolve(res.Data)
					})
				})		
			},
			onreachBottom() {
				if(this.loadmore.status=='nomore'){
					return
				}
				var orderList=this.orderList
				this.renderList(this.page+1,this.type).then(res=>{
					for(let index in res){
						orderList.push(res[index])
					}
					if(res.length>0){
						this.orderList=orderList
						this.page++
					}
					else{
						this.loadmore.status='nomore'
					}
				})
			}
		}
		
	}
</script>

<style>
	@import url("style.css");
	
</style>
