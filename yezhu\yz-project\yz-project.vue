<template>
	<view class="project-list">
		<view class="i flex border-all" v-for="(item,index) in projectList"  :key="index" @click="change(item.ProjectCode,item.ProjectName,item.selected)">
			<view class="flex-hd">
				<image :src="item.image" v-if="item.image"></image>
				<image src="http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/projectimg01.png" v-else></image>
			</view>
			<view class="flex-bd">
				<view class="name">{{item.ProjectName}}</view>
				<view class="info">地址：{{item.Address}}</view>
				<view class="info">业务负责人：{{item.OwnerinChargeName}}</view>
				<view class="count">
					<view class="f inline">
						我的预约：<view class="inline" v-if="item.applynumber">{{item.applynumber}}</view><view class="inline" v-else>0</view>
					</view>
					<view class="f inline">
						我的保修：<view class="inline" v-if="item.repairnumber">{{item.repairnumber}}</view><view class="inline" v-else>0</view>
					</view>
				</view>
			</view>
			<view class="flex__ft"></view>
			<view class="selected" v-if="item.selected">
				<i class="custom-icon custom-icon-selected-rt"></i>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				projectList:[]
			}
		},
		onLoad() {
			this.$http.get('/HomepageMng/GetProjectAll', {
				params:{
					UserCode:uni.getStorageSync('UserCode')
				}
			}).then(res => {
				var list=JSON.parse(res.Data)
				//判断当前选中项目
				var nowCode=uni.getStorageSync('projectCode')
				for(let index in list){
					if(list[index].ProjectCode==nowCode){
						list[index].selected=true
					}
				}
				this.projectList=list
			})
		},
		methods: {
			change(DefaultProjectCode,ProjectName,selected){
				if(selected){
					return
				}
				var that=this
				uni.showModal({
				    title: '提示',
				    content: '是否切换项目？',
					confirmText:'切换',
					cancelText:'不切换',
				    success: function (res) {
				        if (res.confirm) {
							that.$http.post('/HomepageMng/UpdateDefaultProject', {
								UserCode:uni.getStorageSync('UserCode'),
								DefaultProjectCode
							}).then(res => {								
								if(res.ErrCode==101){
									uni.setStorageSync('projectName',ProjectName)
									uni.setStorageSync('projectCode',DefaultProjectCode)
									uni.setStorageSync('homeForm','projectChange')
									that.$u.toast('切换成功，正在返回首页');
									setTimeout(function(){
										uni.reLaunch({
											url:'../../pages/yz-home/yz-home'
										})
									},1000)
								}else{
									that.$u.toast(res.ErrMsg);
								}
							})
				        } 
				    }
				});
			}
		}
	}
</script>

<style>
	page{
		background: #f6f6f6;
	}
	.selected{
		position: absolute;
		top: 0;
		right: 0;
		color: #35318f;
	}
	.selected i{
		font-size: 60rpx;
	}
</style>
