<template>
	<view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">预约信息</view>
				</view>
			</view>
			<view slot="body">
				<u-form :model="form" :rules="rules" ref="uForm" :errorType="errorType">
					<u-form-item label="申请人" label-width="140rpx" :border-bottom="flase">
						<u-input :value="form.UserName" disabled/>
					</u-form-item>
					<u-form-item label="联系电话" prop="CellPhone" label-width="140rpx" :border-bottom="flase">
						<u-input v-model="form.CellPhone" :border="true"/>
					</u-form-item>
					<u-form-item label="维修处室" prop="Department" label-width="140rpx" :border-bottom="flase">
						<u-input v-model="form.Department" :border="true" />
					</u-form-item>
					<u-form-item label="维修地点" prop="Address" label-width="140rpx" :border-bottom="flase">
						<u-input v-model="form.Address" :border="true" />
					</u-form-item>
					<u-form-item label="报修内容" label-width="140rpx" :border-bottom="flase">
						<u-input v-model="form.RepairMatter" type="textarea" :border="true" />
					</u-form-item>
					<u-form-item label="备注" label-width="140rpx" :border-bottom="flase">
						<u-input v-model="form.Remark" type="textarea" :border="true" />
					</u-form-item>
					<u-form-item label="上传图片" label-width="140rpx" :border-bottom="flase">
						<u-upload ref="uUpload" :action="action" :header="uploadHeader" @on-success="uploadOk" :file-list="fileList" ></u-upload>
					</u-form-item>		
				</u-form>
			</view>
		</u-card>
		<view class="mr30">
			<u-button type="primary" @click="submit">确认提交</u-button>
		</view>
		
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				uploadHeader:{
					Authorization: 'Bearer '+ uni.getStorageSync('access_token')
				},
				action:app.globalData.uploadUrl,
				form:{
					UserName:'',
					Department:'',
					Address:'',
					Remark:'',
					CellPhone:''
				},
				errorType: ['message'],
				rules: {
					Department: [
						{
							required: true,
							message: '请输入维修处室',
							trigger: ['change', 'blur']
						},
						{
							max: 20, 
							message: '不超过20个字', 
							trigger: 'change'
						}
					],
					Address: [
						{
							required: true,
							message: '请输入维修地点',
							trigger: ['change', 'blur']
						},
						,
						{
							max: 20, 
							message: '不超过20个字', 
							trigger: 'change'
						}
					],
					CellPhone:[
						{
							required: true, 
							message: '请输入手机号',
							trigger: ['change','blur'],
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// this.$u.test.mobile()就是返回true或者false的
								return this.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['change','blur'],
						}
					]
				},
				fileList:[],
				photoList:[]
			}
		},
		onLoad(e) {
			this.action=this.uploadUrl
			this.form.UserName = uni.getStorageSync('UserName')
			if(e.ProjectCode){
				this.form.ProjectCode =e.ProjectCode
			}
			else{
				this.form.ProjectCode = uni.getStorageSync('projectCode')
			}
			this.form.ApplyUserCode = uni.getStorageSync('UserCode')
			this.form.CellPhone = uni.getStorageSync('CellPhone')
			this.form.Photos=''
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		methods: {
			submit(){
				var photoList=[]
				var files = this.$refs.uUpload.lists;
				for(let index in files){
					photoList.push(files[index].response.RetValue)
				}				
				this.form.Photos=photoList.toString()
				this.$refs.uForm.validate(valid => {
					if (valid) {
						this.$http.post('/RepairMngApi/Execute?Docmd=add', this.form).then(res => {
							this.$u.toast(res.ErrMsg);
							if(res.ErrCode==100){
								setTimeout(function(){
									uni.redirectTo({
										url:'../yz-repairList/yz-repairList'
									})
								},1000)
							}
						})
					}
				})
			},
			uploadOk(data,index,lists){
				console.log(data)
				this.photoList.push(data.RetValue)
			}
		}
	}
</script>

<style>
</style>
