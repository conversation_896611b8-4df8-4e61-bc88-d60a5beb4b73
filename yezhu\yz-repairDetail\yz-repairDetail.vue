<template>
	<view class="border-top">
		<view class="detail-top flex">
			<view class="flex-bd">
				工单号：{{info.RepairNo}}
			</view>
		</view>
		<view class="detail-finish">
			<view class="q">
				<view class="q-t">{{info.repairStatus}}</view>
			</view>
		</view>
		<!-- <view class="detail-finish" v-if="info.repairStatus=='已完成'||info.repairStatus=='已评价'">
			<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/finish.png"></image>
		</view> -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">报修信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="报修人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.applyusername}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="报修时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ApplyTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修处室" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.Department}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修地点" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.Address}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="报修内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.RepairMatter}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.Remark}}</text>
					</u-cell-item>
				</u-cell-group>
				<view class="detail-img-list">
					<image v-for="(img,index) in info.Photos" :key="index" :src="img" @click="lookImg(index,info.Photos)"></image>
				</view>
				<view class="detail-phone" @click="callPhone(info.CellPhone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus!='待指派'&&info.repairStatus!='作废'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">派单信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="派单时间" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.DispatchTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="接单时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.AcceptTime}}</text>
					</u-cell-item>					
				</u-cell-group>
				<view class="work">
					<u-avatar src="http://annyou.oss-cn-shenzhen.aliyuncs.com/miaojie/yuangong.png"></u-avatar>
					<view class="w-no"  v-if="selectedlist.repairJobNumber">{{info.repairJobNumber}}</view>
					<view class="w-name">{{info.repairusername}}</view>
				</view>
				<view class="detail-phone" @click="callPhone(info.dispatchuserphone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus=='已完成'||info.repairStatus=='已评价'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">维修信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="维修人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.repairusername}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.CompleteTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.RepairContent}}</text>
					</u-cell-item>
					
				</u-cell-group>
				<view class="detail-img-list">
					<image v-for="(img,index) in info.RepairPhotos" :key="index" :src="img" @click="lookImg(index,info.RepairPhotos)"></image>
				</view>
			</view>
		</u-card>
		<view class="mr30" v-if="info.repairStatus!='已完成'&&info.repairStatus!='已评价'&&info.repairStatus!='作废'">
			<u-button type="primary" @click="cancle">
				取消
			</u-button>
		</view>
		<view class="mr30" v-if="info.repairStatus=='已完成'">
			<u-button type="primary" @click="gotoUrl">
				我要评价
			</u-button>
		</view>
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.repairStatus=='已评价'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">评价信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="评价人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.applyusername}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="维修质量" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.RepairQuality}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="服务质量" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ServiceQuality}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价星级" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<u-rate :disabled="true" v-model="info.EvaluateLevel" :count="5" active-color="#eca805"></u-rate>
					</u-cell-item>
					<u-cell-item v-if="info.EvaluateContent" class="cell-item-reset" title="建议和要求" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateContent}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.EvaluatePhotos" :key="index" :src="img" @click="lookImg(index,info.EvaluatePhotos)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		<view style="height: 1px;"></view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				info:{
					RepairNo:'',
					repairStatus:'',
					applyusername:'',
					ApplyTime:'',
					Remark:'',
					Photos:'',
					dispatchtime:'',
					AcceptTime:'',
					DispatchUserJobNumber:'',
					DispatchUserName:'',
					dispatchuserphone:'',
					repairusername:'',
					completetime:'',
					RepairContent:'',
					RepairPhotos:'',
					applyusername:'',
					EvaluateTime:'',
					EvaluateLevel:'',
					EvaluateContent:'',
					Department:'',
					Address:''
				},
				RepairCode:"",
				hostUrl:app.globalData.hostUrl,
				
			}
		},
		methods: {
			cancle(){
				var that=this
				uni.showModal({
				    title: '提示',
				    content: '是否取消预约',
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							that.$http.post('/RepairMngApi/Execute?Docmd=cancel', {
								RepairCode:that.RepairCode
							}).then(res => {
								that.$u.toast(res.ErrMsg);
								if(res.ErrCode==100){
									that.load()
								}
							})
				        } 
				    }
				});
				
			},
			lookImg(index,imgList){
				uni.previewImage({
					current:index,
					urls: imgList
				})
			},
			gotoUrl(e){
				uni.navigateTo({
					url:'../yz-repairComment/yz-repairComment?code='+this.RepairCode+'&type=repair&redirectUrl=/yezhu/yz-repairDetail/yz-repairDetail&no='+this.info.RepairNo
				})
			},
			callPhone(e){
				uni.showModal({
				    title: '是否拨打电话？',
				    content: e,
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber:e
							})
				        } 
				    }
				});
			},
			load(){
				this.$http.get('/RepairMngApi/GetDetail', {
					params:{
						RepairCode:this.RepairCode
					}
				}).then(res => {
					
					res.Data.ApplyTime=res.Data.ApplyTime?res.Data.ApplyTime.replace('T',' '):''
					res.Data.DispatchTime=res.Data.DispatchTime?res.Data.DispatchTime.replace('T',' '):''
					res.Data.AcceptTime=res.Data.AcceptTime?res.Data.AcceptTime.replace('T',' '):''
					res.Data.CompleteTime=res.Data.ApplyTime?res.Data.CompleteTime.replace('T',' '):''
					res.Data.EvaluateTime=res.Data.EvaluateTime?res.Data.EvaluateTime.replace('T',' '):''
					if(res.Data.Photos){
						res.Data.Photos=res.Data.Photos.split(',')
						for(let index in res.Data.Photos){
							res.Data.Photos[index]=this.hostUrl+res.Data.Photos[index]
						}
					}
					if(res.Data.RepairPhotos){
						res.Data.RepairPhotos=res.Data.RepairPhotos.split(',')
						for(let index in res.Data.RepairPhotos){
							res.Data.RepairPhotos[index]=this.hostUrl+res.Data.RepairPhotos[index]
						}
					}
					if(res.Data.ServiceList){
						res.Data.ServiceList=res.Data.ServiceList.split(',')
						for(let index in res.Data.ServiceList){
							res.Data.ServiceList[index]=this.hostUrl+res.Data.ServiceList[index]
						}
					}
					//合并图片
					if(res.Data.RepairPhotos&&res.Data.ServiceList){
						res.Data.RepairPhotos=res.Data.RepairPhotos.concat(res.Data.ServiceList)
					}
					if(res.Data.EvaluatePhotos){
						res.Data.EvaluatePhotos=res.Data.EvaluatePhotos.split(',')
						for(let index in res.Data.EvaluatePhotos){
							res.Data.EvaluatePhotos[index]=this.hostUrl+res.Data.EvaluatePhotos[index]
						}
					}
					
					this.info=res.Data
				})
			}
		},
		onLoad(e) {
			
			console.log(e)
			var RepairCode=e.code
			this.RepairCode=RepairCode
			this.load()
		},
		onShow() {
			var that=this
			uni.$on('update',function(res){
				if(res.update){
					that.load()
				}
			})
		}
	}
</script>

<style>
	page{
		background: #f6f6f6;
	}
</style>
