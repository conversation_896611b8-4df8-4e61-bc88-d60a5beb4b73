<template>
	<view class="swiper-wrap border-top">
		<view class="pd">
			<u-search placeholder="请输入单号" :show-action="true" v-model="keyword" shape="square" @custom="search"></u-search>
		</view>
		<u-tabs-swiper ref="uTabs" active-color="#35318f" :list="list" :current="current" @change="tabsChange" :is-scroll="false" swiperWidth="750"></u-tabs-swiper>
		<view class="swiper-box">
			<scroll-view scroll-y style="height: 100%;width: 100%;" @scrolltolower="onreachBottom">
				<view class="count">共{{orderCount}}条报修记录</view>
				<view class="list">
					<navigator class="i flex border-all" v-for="(item,index) in orderList" :key="index" :url="'../yz-repairDetail/yz-repairDetail?code='+item.RepairCode">
						<view class="flex-bd">
							<view class="t">工单号：{{item.RepairNo}}</view>
							<view class="info">报修人：{{item.applyusername}}</view>
							<view class="info">报修时间：{{item.ApplyTime}}</view>
						</view>
						<view class="flex__ft"></view>
					</navigator>
					<u-empty class="u-text-center" v-if="empty" text="暂无内容" margin-top="300"></u-empty>
				</view>
				<view class="mr30" v-if="!empty">
					<u-loadmore :status="loadmore.status" :icon-type="loadmore.iconType" :load-text="loadmore.loadText" />
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [{
					name: '待维修',
					type:0
				}, {
					name: '待评价',
					type:3
				}, {
					name: '已完成',
					type:4
				}],
				current: 0,
				orderList:[],
				keyword:'',
				empty:false,
				page:1,
				loadmore:{
					status: 'loading',
					iconType: 'flower',
					loadText: {
						loading: '努力加载中',
						nomore: '已无更多'
					}
				},
				orderCount:0,
				type:0,
				projectCode:''
			}
		},
		onLoad(e) {
			if(e.projectCode){
				var projectCode=e.projectCode
			}
			else{
				var projectCode=uni.getStorageSync('projectCode')
			}
			this.projectCode=projectCode
			this.renderList(1).then(res=>{
				this.orderList=res
				if(res.length<10){
					this.loadmore.status='nomore'
				}
			})
			this.getNum()
			
			this.$http.get('/ProjectMngApi/GetMyRepairCount', {
				params:{
					UserCode:uni.getStorageSync('UserCode'),
					ProjectCode:projectCode,
					orderNo:'',
					applyStatusList:'0,3,4'
				}
			}).then(res => {
				var data =res.Data
				var list = [{
					name: '待维修',
					type:0,
					count:data.Count1
				}, {
					name: '待评价',
					type:3,
					count:data.Count2
				}, {
					name: '已完成',
					type:4,
					count:data.Count3
				}]
				this.list = list
			})
		},
		methods: {
			getNum(){
				this.$http.get('/UserApi/GetMyApplyNum', {
					params:{
						UserCode:uni.getStorageSync('UserCode'),
						projectCode:this.projectCode,
						applyStatus:this.type,
						orderNo:this.keyword
					}
				}).then(res => {
					this.orderCount=res.Data.bxNum
				})
			},
			search(value) {
				this.renderList(1).then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.getNum()
				this.page=1
			},
			tabsChange(item) {
				if(this.current == item.index){
					return
				}
				this.current = item.index;
				var type=item.item.type
				this.type=type				
				this.loadmore.status='loading'
				this.renderList(1).then(res=>{
					this.orderList=res
					if(res.length<10){
						this.loadmore.status='nomore'
					}
				})
				this.page=1
				this.getNum()
			},
			renderList(page){
				return new Promise((resolve)=>{
					this.$http.get('/RepairMngApi/GetListAll', {
						params:{
							p:page,
							ps:10,
							UserCode:uni.getStorageSync('UserCode'),
							projectCode:this.projectCode,
							RepairStatus:this.type,
							RepairNo:this.keyword
						}
					}).then(res => {
						if(page==1&res.Data.length==0){
							this.empty=true
						}
						else{
							this.empty=false
						}
						for(let index in res.Data){
							res.Data[index].ApplyTime=res.Data[index].ApplyTime.replace('T',' ')
						}
						resolve(res.Data)
					})
				})		
			},
			onreachBottom() {
				if(this.loadmore.status=='nomore'){
					return
				}
				var orderList=this.orderList
				this.renderList(this.page+1,this.type).then(res=>{
					for(let index in res){
						orderList.push(res[index])
					}
					if(res.length>0){
						this.orderList=orderList
						this.page++
					}
					else{
						this.loadmore.status='nomore'
					}
				})
			}
		}
	}
</script>

<style>
	@import url("style.css");
</style>
