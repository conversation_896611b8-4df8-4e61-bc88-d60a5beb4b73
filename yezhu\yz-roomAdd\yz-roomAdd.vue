<template>
	<view class="border-top">
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">预约信息</view>
				</view>
			</view>
			<view slot="body">
				<u-form :model="form" :rules="rules" ref="uForm" :errorType="errorType">
					<u-form-item label="申请人" label-width="150rpx" :border-bottom="flase">
						<u-input value="申请人" v-model="form.UserName" disabled />
					</u-form-item>
					<u-form-item label="联系电话" prop="CellPhone" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="form.CellPhone" :border="true"/>
					</u-form-item>
					<u-form-item label="预约时间" prop="FromTime" label-width="150rpx" :border-bottom="flase">
						<u-input @click="shijianShow = true" v-model="form.FromTime" type="select" :border="true" />
					</u-form-item>
					<u-form-item label="用餐人数" prop="Number" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="form.Number" type="number" :border="true" />
					</u-form-item>
					<u-form-item label="用餐标准" prop="AvgMoney" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="form.AvgMoney" type="number" :border="true" />
						<text slot="right">元/人</text>
					</u-form-item>
					<u-form-item label="总金额" prop="TotalMoney" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="form.TotalMoney" type="number" :border="true" />
						<text slot="right">元</text>
					</u-form-item>
					<u-form-item label="备注内容" label-width="150rpx" :border-bottom="flase">
						<u-input v-model="form.Remark" type="textarea" :border="true" />
					</u-form-item>	
				</u-form>
			</view>
		</u-card>
		<view class="mr30">
			<u-button type="primary" @click="submit">确认提交</u-button>
		</view>
		<u-picker mode="time" v-model="shijianShow" :params="params" @confirm="shijian"></u-picker>
	</view>
</template>

<script>
	import utils from '../../components/utils.js'
	export default {
		data() {
			return {
				riqiShow:'',
				shijianShow:'',
				shiShow:'',
				params: {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					second: false
				},
				numList:Array.from({length:19}, (v,k) => (k+1)),
				form:{
					FromTime:'',
					Number:'',
					AvgMoney:'',
					TotalMoney:'',
					Remark:'',
					UserName:'',
					CellPhone:''
				},
				errorType: ['message'],
				rules: {
					FromTime: [
						{
							required: true,
							message: '请选择预约时间',
							trigger: ['change', 'blur']
						}
					],
					Number: [
						{
							required: true,
							message: '请输入用餐人数',
							trigger: ['change', 'blur']
						}
					],
					CellPhone:[
						{
							required: true, 
							message: '请输入手机号',
							trigger: ['change','blur'],
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// 上面有说，返回true表示校验通过，返回false表示不通过
								// this.$u.test.mobile()就是返回true或者false的
								return this.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['change','blur'],
						}
					]
				},
				sfrom:''
			}
		},
		onReady() {
			this.$refs.uForm.setRules(this.rules);
		},
		onLoad(e) {
			this.form.CellPhone = uni.getStorageSync('CellPhone')
			this.form.UserName = uni.getStorageSync('UserName')
			if(e.ProjectCode){
				this.form.ProjectCode =e.ProjectCode
			}
			else{
				this.form.ProjectCode = uni.getStorageSync('projectCode')
			}
			this.form.ApplyUserCode = uni.getStorageSync('UserCode')
			if(e.sfrom=='change'){
				this.sfrom='change'
				this.form.BoxCode=e.BoxCode
			}
		},
		methods: {
			shijian(e){
				console.log(e)
				this.form.FromTime=e.year+'-'+e.month+'-'+e.day+' '+e.hour+':'+e.minute+':00'
			},
			submit(){ 
				this.$refs.uForm.validate(valid => {
					if (valid) {
						if(this.sfrom=='change'){
							var url='/ReserveBoxMngApi/Execute?Docmd=change'
						}
						else{
							var url='/ReserveBoxMngApi/Execute?Docmd=add'
						}
						if(!utils.checkTime(this.form.FromTime)){
							return this.$u.toast('预约时间应大于当前时间');
						}
						if(!this.form.AvgMoney&&!this.form.TotalMoney){
							return this.$u.toast('请输入用餐标准或总金额');
						}
						if(!this.form.AvgMoney){
							this.form.AvgMoney=0
						}
						if(!this.form.TotalMoney){
							this.form.TotalMoney=0
						}
						this.$http.post(url, this.form).then(res => {
							this.$u.toast(res.ErrMsg);
							if(res.ErrCode==100){
								setTimeout(function(){
									uni.redirectTo({
										url:'../yz-orderList/yz-orderList?type=0'
									})
								},1000)
							}
						})
					}
				})
			}
		}
	}
</script>

<style>
</style>
