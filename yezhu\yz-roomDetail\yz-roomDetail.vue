<template>
	<view class="border-top">
		
		<!-- 基础信息 -->
		<view class="detail-top flex">
			<view class="flex-hd">
				<image src="https://annyousoft.oss-cn-shanghai.aliyuncs.com/mjwy/dd-icon_bx.png"></image>
			</view>
			<view class="flex-bd">
				<view class="t">工单号：{{info.OrderNo}}</view>
				<view class="desc">包厢预定</view>
			</view>
		</view>
		<view class="detail-finish">
			<view class="q">
				<view class="q-t">{{info.IntiStatus}}</view>
			</view>
		</view>
		<!-- 预约信息 -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">预约信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="申请人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.ApplyUserName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="申请时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.ApplyTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="预约时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.FromTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="用餐人数" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.Number}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="用餐标准" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.AvgMoney}}元/人</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="总金额" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.TotalMoney}}元</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text v-if="info.Remark">{{info.Remark}}</text>
					</u-cell-item>				
				</u-cell-group>
				<view class="detail-his-list" v-if="showAddHis">
					<view class="i" v-for="(item,index) in addHisList" :key="index">
						<view class="t">
							<view class="span">{{item.ChangeTime}}</view>
						</view>
						<u-cell-group :border="flase">
							<u-cell-item  class="cell-item-reset" title="预约时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.FromTime}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="用餐人数" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.Number}}</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="用餐标准" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.AvgMoney}}元/人</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="总金额" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text>{{item.TotalMoney}}元</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="备注内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
								<text v-if="item.Remark">{{item.Remark}}</text>
							</u-cell-item>
						</u-cell-group>
					</view>
				</view>
				<view class="detail-more-btn" v-if="addHisList" @click="showAddHis=!showAddHis">
					历史预约记录<i :class="'custom-icon '+(showAddHis?'custom-icon-up-copy':'custom-icon-down')"></i>
				</view>
				<view class="detail-phone" @click="callPhone(info.CellPhone)">
					<i class="custom-icon custom-icon-lujingbeifen3"></i>
				</view>
			</view>
		</u-card>
		
		<block v-if="info.IntiStatus!='待审核'&&info.CheckUserName&&info.IntiStatus!='审核不通过'&&info.IntiStatus!='取消预约'">
			<!-- 包厢安排 -->
			<u-card :foot-border-top="false" padding="20" class="card-readyonly">
				<view slot="head">
					<view class="u-flex u-col-top u-row-between">
						<view class="u-flex-nowrap u-item-title u-font-28">包厢安排</view>
					</view>
				</view>
				<view slot="body">
					<u-cell-group :border="flase">
						<u-cell-item  class="cell-item-reset" title="安排包厢" :border-bottom="flase" :arrow="flase"  title-width="180" >
							<text>{{info.BoxName}}</text>
						</u-cell-item>
						<u-cell-item  class="cell-item-reset" title="分配时间" :border-bottom="flase" :arrow="flase"  title-width="180" >
							<text>{{info.BoxFromTime}}</text>
						</u-cell-item>
						<u-cell-item  class="cell-item-reset" title="包厢联系人" :border-bottom="flase" :arrow="flase"  title-width="180" >
							<text>{{info.BoxContacts}}</text>
						</u-cell-item>
					</u-cell-group>
					<view class="detail-phone" @click="callPhone(info.BoxContactsPhone)">
						<i class="custom-icon custom-icon-lujingbeifen3"></i>
					</view>
				</view>
			</u-card>
			
			
			<!-- 信息审核 -->
			<block>
				<u-card :foot-border-top="false" padding="20" class="card-readyonly">
					<view slot="head">
						<view class="u-flex u-col-top u-row-between">
							<view class="u-flex-nowrap u-item-title u-font-28">审核信息</view>
						</view>
					</view>
					<view slot="body">
						<u-cell-group :border="flase">
							<u-cell-item class="cell-item-reset" title="审核时间" :border-bottom="flase" :arrow="flase" title-width="180">
								<text>{{info.CheckTime}}</text>
							</u-cell-item>					
							<u-cell-item class="cell-item-reset" title="审核结果" :border-bottom="flase" :arrow="flase" title-width="180">
								<text v-if="info.ApplyStatus==1||info.ApplyStatus>2">审核通过</text>
								<text v-if="info.ApplyStatus==2">审核未通过</text>
							</u-cell-item>
							<u-cell-item  class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="180" >
								<text v-if="info.CheckRemark==null"></text>
								<text v-else>{{info.CheckRemark}}</text>
							</u-cell-item>		
						</u-cell-group>
						<view class="detail-his-list" v-if="showCheckHis">
							<view class="i" v-for="(item,index) in checkHisList" :key="index">
								<view class="t">
									<view class="span">{{item.ChangeTime}}</view>
								</view>
								<u-cell-group :border="flase">
									<u-cell-item class="cell-item-reset" title="审核时间" :border-bottom="flase" :arrow="flase" title-width="150">
										<text>{{item.CheckTime}}</text>
									</u-cell-item>
									<u-cell-item class="cell-item-reset" title="审核结果" :border-bottom="flase" :arrow="flase" title-width="150">
										<text v-if="item.ApplyStatus==1">审核通过</text>
										<text v-if="item.ApplyStatus==2">审核未通过</text>
									</u-cell-item>
									<u-cell-item  class="cell-item-reset" title="安排包厢" :border-bottom="flase" :arrow="flase"  title-width="150" >
										<text v-if="item.BoxName">{{item.BoxName}}</text>
									</u-cell-item>
									<u-cell-item  class="cell-item-reset" title="备注" :border-bottom="flase" :arrow="flase"  title-width="150" >
										<text v-if="item.CheckRemark==null"></text>
										<text v-else>{{item.CheckRemark}}</text>
									</u-cell-item>	
								</u-cell-group>
							</view>
						</view>
						<view class="detail-more-btn" v-if="checkHisList.length>0" @click="showCheckHis=!showCheckHis">
							历史审核记录<i :class="'custom-icon '+(showCheckHis?'custom-icon-up-copy':'custom-icon-down')"></i>
						</view>
					</view>
				</u-card>
			</block>
			
			
		</block>
		
		<view class="mr30" v-if="info.IntiStatus=='待审核'||info.IntiStatus=='审核通过'||info.IntiStatus=='审核不通过'">
			<u-row gutter="16">
				<u-col span="6">
					<u-button type="default" @click="cancle">取消预约</u-button>
				</u-col>
				<u-col span="6">
					<u-button type="primary" @click="change">变更预约</u-button>
				</u-col>			
			</u-row>			
		</view>
		
		<!-- 评价信息 -->
		<u-card :foot-border-top="false" padding="20" class="card-readyonly" v-if="info.IntiStatus=='已完成'">
			<view slot="head">
				<view class="u-flex u-col-top u-row-between">
					<view class="u-flex-nowrap u-item-title u-font-28">评价信息</view>
				</view>
			</view>
			<view slot="body">
				<u-cell-group :border="flase">
					<u-cell-item class="cell-item-reset" title="评价人" :border-bottom="flase" :arrow="flase" title-width="150">
						<text>{{info.ApplyUserName}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价时间" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateTime}}</text>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价星级" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<u-rate :disabled="true" v-model="info.EvaluateLevel" :count="5" active-color="#eca805"></u-rate>
					</u-cell-item>
					<u-cell-item  class="cell-item-reset" title="评价内容" :border-bottom="flase" :arrow="flase"  title-width="150" >
						<text>{{info.EvaluateContent}}</text>
					</u-cell-item>
					<view class="detail-img-list">
						<image v-for="(img,index) in info.EvaluatePhotos" :key="index" :src="img" @click="lookImg(index,info.EvaluatePhotos)"></image>
					</view>
				</u-cell-group>
			</view>
		</u-card>
		<view class="mr30" v-if="info.IntiStatus=='待评价'">
			<u-button type="primary" @click="gotoUrl">我要评价</u-button>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		data() {
			return {
				hostUrl:app.globalData.hostUrl,
				BoxCode:'',
				addHisList:'',
				checkHisList:'',
				showCheckHis:'',
				showAddHis:'',
				info:{
					OrderNo:''
				},
				CheckRemark:''
			}
		},
		methods: {
			callPhone(e){
				uni.showModal({
				    title: '是否拨打电话？',
				    content: e,
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber:e
							})
				        } 
				    }
				});
			},
			lookImg(index,imgList){
				uni.previewImage({
					current:index,
					urls: imgList
				})
			},
			change(){
				uni.navigateTo({
					url:"../yz-roomAdd/yz-roomAdd?sfrom=change&BoxCode="+this.BoxCode
				})
			},
			cancle(){
				var that=this
				uni.showModal({
				    title: '提示',
				    content: '是否取消预约',
					confirmText:'确认',
					cancelText:'取消',
				    success: function (res) {
				        if (res.confirm) {
							that.$http.post('/ReserveBoxMngApi/Execute?Docmd=cancel', {
								BoxCode:that.BoxCode
							}).then(res => {
								that.$u.toast(res.ErrMsg);
								if(res.ErrCode==100){
									setTimeout(function(){
										that.load()
									},1000)
								}
							})
				        } 
				    }
				});
				
			},
			renderAddList(){
				return new Promise((resolve)=>{
					this.$http.get('/ReserveBoxMngApi/GetDetail?Docmd=history', {
						params:{
							BoxCode:this.BoxCode
						}
					}).then(res => {
						resolve(res.Data)
					})
				})		
			},
			gotoUrl(e){
				uni.navigateTo({
					url:'../yz-comment/yz-comment?type=room&code='+this.BoxCode+'&no='+this.info.OrderNo
				})
			},
			load(){
				this.$http.get('/ReserveBoxMngApi/GetDetail?Docmd=main', {
					params:{
						BoxCode:this.BoxCode
					}
				}).then(res => {
					res.Data.ApplyTime=res.Data.ApplyTime?res.Data.ApplyTime.replace('T',' '):''
					res.Data.CheckTime=res.Data.CheckTime?res.Data.CheckTime.replace('T',' '):''
					res.Data.FromTime=res.Data.FromTime?res.Data.FromTime.replace('T',' '):''
					res.Data.BoxFromTime=res.Data.BoxFromTime?res.Data.BoxFromTime.replace('T',' '):''
					res.Data.EvaluateTime=res.Data.EvaluateTime?res.Data.EvaluateTime.replace('T',' '):''
					
					if(res.Data.EvaluatePhotos){
						res.Data.EvaluatePhotos=res.Data.EvaluatePhotos.split(',')
						for(let index in res.Data.EvaluatePhotos){
							res.Data.EvaluatePhotos[index]=this.hostUrl+res.Data.EvaluatePhotos[index]
						}
					}
					
					this.info=res.Data
				})
				
				
				this.renderAddList().then(res=>{
					if(res.length>0){
						var checkHisList=[]
						for(let index in res){
							res[index].ChangeTime=res[index].ChangeTime.replace('T',' ')
							res[index].FromTime=res[index].FromTime.replace('T',' ')
							res[index].ApplyTime=res[index].ApplyTime.replace('T',' ')
							
							res[index].CheckTime=res[index].CheckTime.replace('T',' ')
							if(res[index].ApplyStatus!=0){
								checkHisList.push(res[index])
							}
						}
						this.addHisList=res
						this.checkHisList=checkHisList
					}
				})
			}
		},
		onLoad(e) {
			
			console.log(e)
			var BoxCode=e.code
			this.BoxCode=BoxCode
			this.load()
		},
		onShow() {
			var that=this
			uni.$on('update',function(res){
				if(res.update){
					that.load()
				}
			})
		}
	}
</script>

<style>
	@import url("style.css");
</style>
